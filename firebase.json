{"firestore": {"database": "(default)", "location": "southamerica-east1", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}], "hosting": {"source": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "frameworksBackend": {"region": "us-central1"}, "headers": [{"source": "**", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate, max-age=0"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}]}, "storage": {"rules": "storage.rules"}, "database": {"rules": "database.rules.json"}}