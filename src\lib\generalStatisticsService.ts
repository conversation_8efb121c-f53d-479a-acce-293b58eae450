import { db } from './firebase';
import { collection, getDocs, query } from 'firebase/firestore';
import { getUserConversations } from './conversationService';
import { chatStatisticsService } from './chatStatisticsService';
import { ChatStatistics } from './types/chat';

// Interface para estatísticas gerais agregadas
export interface GeneralStatistics {
  // Contadores básicos
  totalChats: number;
  totalMessages: number;
  totalUserMessages: number;
  totalAssistantMessages: number;
  totalAttachments: number;

  // Contagem de palavras
  totalWords: number;
  totalUserWords: number;
  totalAssistantWords: number;

  // Tokens e custos
  totalPromptTokens: number;
  totalCompletionTokens: number;
  totalTokens: number;
  totalCost: number;

  // Métricas de tempo (médias ponderadas)
  averageResponseTime: number; // em ms
  averageMessageLength: number; // caracteres
  averageUserTypingSpeed: number; // caracteres por minuto
  averageTimeBetweenMessages: number; // em ms

  // Sessões
  totalSessions: number;
  averageSessionDuration: number; // em ms
  averageInactivityPeriod: number; // em ms

  // Análise temporal agregada
  activityHeatmap: {
    hourOfDay: number[]; // 24 elementos (0-23h)
    dayOfWeek: number[]; // 7 elementos (0-6, domingo-sábado)
  };

  // Palavras mais usadas (top 20 agregadas)
  mostUsedWords: Array<{
    word: string;
    count: number;
  }>;

  // Histórico de performance agregado
  modelPerformanceHistory: Array<{
    timestamp: number;
    responseTime: number;
    model: string;
  }>;

  // Metadados
  firstMessageAt: number;
  lastMessageAt: number;
  lastUpdatedAt: number;

  // === NOVAS ESTATÍSTICAS ÚNICAS AGREGADAS ===

  // Estatísticas de edição e regeneração
  messageEdits: {
    totalEdits: number;
    userMessageEdits: number;
    averageEditsPerMessage: number;
    mostEditedMessageLength: number;
  };

  messageRegenerations: {
    totalRegenerations: number;
    averageRegenerationsPerAssistantMessage: number;
    regenerationReasons: Array<{
      reason: string;
      count: number;
    }>;
  };

  // Análise de sentimento agregada
  sentimentAnalysis: {
    positive: number;
    neutral: number;
    negative: number;
    averageSentimentScore: number;
    sentimentTrend: Array<{
      timestamp: number;
      score: number;
    }>;
  };

  // Padrões de conversa agregados
  conversationPatterns: {
    averageConversationLength: number;
    longestConversationStreak: number;
    shortestResponseTime: number;
    longestResponseTime: number;
    questionsAsked: number;
    codeBlocksShared: number;
    mathExpressionsUsed: number;
  };

  // Complexidade das mensagens agregada
  messageComplexity: {
    averageWordsPerMessage: number;
    averageSentencesPerMessage: number;
    averageSyllablesPerWord: number;
    readabilityScore: number;
    technicalTermsUsed: number;
  };

  // Métricas de leitura agregadas
  readingMetrics: {
    estimatedReadingTime: number;
    averageTimeToRespond: number;
    quickResponses: number;
    thoughtfulResponses: number;
  };

  // Uso de recursos agregado
  featureUsage: {
    attachmentsUsed: {
      images: number;
      pdfs: number;
      totalSizeMB: number;
    };
    latexUsage: number;
    codeLanguages: Array<{
      language: string;
      count: number;
    }>;
    topicsDiscussed: Array<{
      topic: string;
      count: number;
    }>;
  };

  // Eficiência da conversa agregada
  conversationEfficiency: {
    goalAchievementRate: number;
    clarificationRequests: number;
    followUpQuestions: number;
    satisfactionIndicators: number;
  };

  // Estatísticas por chat (para análises detalhadas)
  chatStatistics: Array<{
    chatId: string;
    chatName: string;
    stats: ChatStatistics;
  }>;
}

class GeneralStatisticsServiceImpl {
  
  /**
   * Obtém todas as estatísticas gerais de um usuário
   */
  async getGeneralStatistics(userId: string): Promise<GeneralStatistics> {
    try {
      // Obter todas as conversas do usuário
      const conversations = await getUserConversations(userId, false);
      
      if (conversations.length === 0) {
        return this.getEmptyStatistics();
      }

      // Obter estatísticas de cada chat
      const chatStatistics: Array<{
        chatId: string;
        chatName: string;
        stats: ChatStatistics;
      }> = [];

      for (const conversation of conversations) {
        const stats = await chatStatisticsService.getChatStatistics(userId, conversation.id);
        if (stats) {
          chatStatistics.push({
            chatId: conversation.id,
            chatName: conversation.name,
            stats
          });
        }
      }

      // Agregar todas as estatísticas
      return this.aggregateStatistics(chatStatistics);

    } catch (error) {
      console.error('Error getting general statistics:', error);
      throw error;
    }
  }

  /**
   * Agrega estatísticas de múltiplos chats
   */
  private aggregateStatistics(chatStatistics: Array<{
    chatId: string;
    chatName: string;
    stats: ChatStatistics;
  }>): GeneralStatistics {
    
    if (chatStatistics.length === 0) {
      return this.getEmptyStatistics();
    }

    // Inicializar contadores
    let totalMessages = 0;
    let totalUserMessages = 0;
    let totalAssistantMessages = 0;
    let totalAttachments = 0;
    let totalWords = 0;
    let totalUserWords = 0;
    let totalAssistantWords = 0;
    let totalPromptTokens = 0;
    let totalCompletionTokens = 0;
    let totalTokens = 0;
    let totalCost = 0;
    let totalSessions = 0;

    // Para médias ponderadas
    let weightedResponseTime = 0;
    let weightedMessageLength = 0;
    let weightedTypingSpeed = 0;
    let weightedTimeBetweenMessages = 0;
    let weightedSessionDuration = 0;
    let weightedInactivityPeriod = 0;

    // Heatmaps agregados
    const hourOfDay = new Array(24).fill(0);
    const dayOfWeek = new Array(7).fill(0);

    // Palavras mais usadas agregadas
    const wordCounts: { [word: string]: number } = {};

    // Performance history agregado
    const allPerformanceHistory: Array<{
      timestamp: number;
      responseTime: number;
      model: string;
    }> = [];

    // Timestamps
    let firstMessageAt = Number.MAX_SAFE_INTEGER;
    let lastMessageAt = 0;

    // === NOVOS CONTADORES PARA ESTATÍSTICAS ÚNICAS ===

    // Edições e regenerações
    let totalEdits = 0;
    let totalUserMessageEdits = 0;
    let totalRegenerations = 0;
    let mostEditedMessageLength = 0;
    const regenerationReasons: { [reason: string]: number } = {};

    // Análise de sentimento
    let totalPositive = 0;
    let totalNeutral = 0;
    let totalNegative = 0;
    let weightedSentimentScore = 0;
    const allSentimentTrend: Array<{ timestamp: number; score: number }> = [];

    // Padrões de conversa
    let totalQuestionsAsked = 0;
    let totalCodeBlocksShared = 0;
    let totalMathExpressionsUsed = 0;
    let shortestResponseTime = Number.MAX_SAFE_INTEGER;
    let longestResponseTime = 0;

    // Complexidade das mensagens
    let weightedWordsPerMessage = 0;
    let weightedSentencesPerMessage = 0;
    let totalTechnicalTermsUsed = 0;

    // Métricas de leitura
    let totalEstimatedReadingTime = 0;
    let totalQuickResponses = 0;
    let totalThoughtfulResponses = 0;
    let weightedTimeToRespond = 0;

    // Uso de recursos
    let totalImages = 0;
    let totalPdfs = 0;
    let totalSizeMB = 0;
    let totalLatexUsage = 0;
    const codeLanguageCounts: { [language: string]: number } = {};

    // Eficiência da conversa
    let totalSatisfactionIndicators = 0;
    let totalClarificationRequests = 0;

    // Processar cada chat
    for (const { stats } of chatStatistics) {
      // Somar contadores básicos
      totalMessages += stats.totalMessages;
      totalUserMessages += stats.userMessages;
      totalAssistantMessages += stats.assistantMessages;
      totalAttachments += stats.totalAttachments;
      totalWords += stats.totalWords || 0;
      totalUserWords += stats.userWords || 0;
      totalAssistantWords += stats.assistantWords || 0;
      totalPromptTokens += stats.totalPromptTokens;
      totalCompletionTokens += stats.totalCompletionTokens;
      totalTokens += stats.totalTokens;
      totalCost += stats.totalCost;
      totalSessions += stats.totalSessions;

      // Calcular médias ponderadas
      if (stats.assistantMessages > 0) {
        weightedResponseTime += stats.averageResponseTime * stats.assistantMessages;
      }
      if (stats.totalMessages > 0) {
        weightedMessageLength += stats.averageMessageLength * stats.totalMessages;
      }
      // Velocidade de digitação removida - sempre 0
      // if (stats.userMessages > 0) {
      //   weightedTypingSpeed += stats.averageUserTypingSpeed * stats.userMessages;
      // }
      if (stats.totalMessages > 1) {
        weightedTimeBetweenMessages += stats.averageTimeBetweenMessages * (stats.totalMessages - 1);
      }
      if (stats.totalSessions > 0) {
        weightedSessionDuration += stats.averageSessionDuration * stats.totalSessions;
        weightedInactivityPeriod += stats.averageInactivityPeriod * stats.totalSessions;
      }

      // Agregar heatmaps
      for (let i = 0; i < 24; i++) {
        hourOfDay[i] += stats.activityHeatmap.hourOfDay[i];
      }
      for (let i = 0; i < 7; i++) {
        dayOfWeek[i] += stats.activityHeatmap.dayOfWeek[i];
      }

      // === PROCESSAR NOVAS ESTATÍSTICAS ===

      // Edições e regenerações
      if (stats.messageEdits) {
        totalEdits += stats.messageEdits.totalEdits || 0;
        totalUserMessageEdits += stats.messageEdits.userMessageEdits || 0;
        if ((stats.messageEdits.mostEditedMessageLength || 0) > mostEditedMessageLength) {
          mostEditedMessageLength = stats.messageEdits.mostEditedMessageLength || 0;
        }
      }

      if (stats.messageRegenerations) {
        totalRegenerations += stats.messageRegenerations.totalRegenerations || 0;
        // Agregar razões de regeneração
        if (stats.messageRegenerations.regenerationReasons) {
          stats.messageRegenerations.regenerationReasons.forEach(reason => {
            regenerationReasons[reason.reason] = (regenerationReasons[reason.reason] || 0) + reason.count;
          });
        }
      }

      // Análise de sentimento
      if (stats.sentimentAnalysis) {
        totalPositive += stats.sentimentAnalysis.positive || 0;
        totalNeutral += stats.sentimentAnalysis.neutral || 0;
        totalNegative += stats.sentimentAnalysis.negative || 0;

        if (stats.userMessages > 0) {
          weightedSentimentScore += (stats.sentimentAnalysis.averageSentimentScore || 0) * stats.userMessages;
        }

        // Agregar trend de sentimento (manter últimos 100)
        if (stats.sentimentAnalysis.sentimentTrend) {
          allSentimentTrend.push(...stats.sentimentAnalysis.sentimentTrend);
        }
      }

      // Padrões de conversa
      if (stats.conversationPatterns) {
        totalQuestionsAsked += stats.conversationPatterns.questionsAsked || 0;
        totalCodeBlocksShared += stats.conversationPatterns.codeBlocksShared || 0;
        totalMathExpressionsUsed += stats.conversationPatterns.mathExpressionsUsed || 0;

        if ((stats.conversationPatterns.shortestResponseTime || 0) > 0 &&
            (stats.conversationPatterns.shortestResponseTime || 0) < shortestResponseTime) {
          shortestResponseTime = stats.conversationPatterns.shortestResponseTime || 0;
        }
        if ((stats.conversationPatterns.longestResponseTime || 0) > longestResponseTime) {
          longestResponseTime = stats.conversationPatterns.longestResponseTime || 0;
        }
      }

      // Complexidade das mensagens
      if (stats.messageComplexity) {
        if (stats.totalMessages > 0) {
          weightedWordsPerMessage += (stats.messageComplexity.averageWordsPerMessage || 0) * stats.totalMessages;
          weightedSentencesPerMessage += (stats.messageComplexity.averageSentencesPerMessage || 0) * stats.totalMessages;
        }
        totalTechnicalTermsUsed += stats.messageComplexity.technicalTermsUsed || 0;
      }

      // Métricas de leitura
      if (stats.readingMetrics) {
        totalEstimatedReadingTime += stats.readingMetrics.estimatedReadingTime || 0;
        totalQuickResponses += stats.readingMetrics.quickResponses || 0;
        totalThoughtfulResponses += stats.readingMetrics.thoughtfulResponses || 0;

        if (stats.userMessages > 0) {
          weightedTimeToRespond += (stats.readingMetrics.averageTimeToRespond || 0) * stats.userMessages;
        }
      }

      // Uso de recursos
      if (stats.featureUsage) {
        if (stats.featureUsage.attachmentsUsed) {
          totalImages += stats.featureUsage.attachmentsUsed.images || 0;
          totalPdfs += stats.featureUsage.attachmentsUsed.pdfs || 0;
          totalSizeMB += stats.featureUsage.attachmentsUsed.totalSizeMB || 0;
        }
        totalLatexUsage += stats.featureUsage.latexUsage || 0;

        // Agregar linguagens de código
        if (stats.featureUsage.codeLanguages) {
          stats.featureUsage.codeLanguages.forEach(lang => {
            codeLanguageCounts[lang.language] = (codeLanguageCounts[lang.language] || 0) + lang.count;
          });
        }
      }

      // Eficiência da conversa
      if (stats.conversationEfficiency) {
        totalSatisfactionIndicators += stats.conversationEfficiency.satisfactionIndicators || 0;
        totalClarificationRequests += stats.conversationEfficiency.clarificationRequests || 0;
      }

      // Agregar palavras mais usadas
      for (const wordData of stats.mostUsedWords) {
        wordCounts[wordData.word] = (wordCounts[wordData.word] || 0) + wordData.count;
      }

      // Agregar performance history
      allPerformanceHistory.push(...stats.modelPerformanceHistory);

      // Atualizar timestamps
      if (stats.firstMessageAt < firstMessageAt) {
        firstMessageAt = stats.firstMessageAt;
      }
      if (stats.lastMessageAt > lastMessageAt) {
        lastMessageAt = stats.lastMessageAt;
      }
    }

    // Calcular médias finais
    const averageResponseTime = totalAssistantMessages > 0 ? weightedResponseTime / totalAssistantMessages : 0;
    const averageMessageLength = totalMessages > 0 ? weightedMessageLength / totalMessages : 0;
    const averageUserTypingSpeed = 0; // Velocidade de digitação removida
    const averageTimeBetweenMessages = totalMessages > 1 ? weightedTimeBetweenMessages / (totalMessages - 1) : 0;
    const averageSessionDuration = totalSessions > 0 ? weightedSessionDuration / totalSessions : 0;
    const averageInactivityPeriod = totalSessions > 0 ? weightedInactivityPeriod / totalSessions : 0;

    // Ordenar palavras mais usadas (top 20)
    const mostUsedWords = Object.entries(wordCounts)
      .map(([word, count]) => ({ word, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);

    // Ordenar performance history por timestamp e manter apenas os últimos 200
    const modelPerformanceHistory = allPerformanceHistory
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 200);

    // === CALCULAR ESTATÍSTICAS FINAIS DAS NOVAS MÉTRICAS ===

    // Calcular médias das novas estatísticas
    const averageEditsPerMessage = totalMessages > 0 ? totalEdits / totalMessages : 0;
    const averageRegenerationsPerAssistantMessage = totalAssistantMessages > 0 ? totalRegenerations / totalAssistantMessages : 0;
    const averageSentimentScore = totalUserMessages > 0 ? weightedSentimentScore / totalUserMessages : 0;
    const averageWordsPerMessage = totalMessages > 0 ? weightedWordsPerMessage / totalMessages : 0;
    const averageSentencesPerMessage = totalMessages > 0 ? weightedSentencesPerMessage / totalMessages : 0;
    const averageTimeToRespond = totalUserMessages > 0 ? weightedTimeToRespond / totalUserMessages : 0;

    // Converter objetos de contagem em arrays ordenados
    const regenerationReasonsArray = Object.entries(regenerationReasons)
      .map(([reason, count]) => ({ reason, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const codeLanguagesArray = Object.entries(codeLanguageCounts)
      .map(([language, count]) => ({ language, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 15);

    // Ordenar e limitar sentiment trend
    const sentimentTrend = allSentimentTrend
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 100);

    // Calcular métricas de conversa
    const averageConversationLength = chatStatistics.length > 0 ? totalMessages / chatStatistics.length : 0;
    const longestConversationStreak = Math.max(...chatStatistics.map(c => c.stats.totalMessages), 0);

    return {
      totalChats: chatStatistics.length,
      totalMessages,
      totalUserMessages,
      totalAssistantMessages,
      totalAttachments,
      totalWords,
      totalUserWords,
      totalAssistantWords,
      totalPromptTokens,
      totalCompletionTokens,
      totalTokens,
      totalCost,
      averageResponseTime,
      averageMessageLength,
      averageUserTypingSpeed,
      averageTimeBetweenMessages,
      totalSessions,
      averageSessionDuration,
      averageInactivityPeriod,
      activityHeatmap: {
        hourOfDay,
        dayOfWeek
      },
      mostUsedWords,
      modelPerformanceHistory,
      firstMessageAt: firstMessageAt === Number.MAX_SAFE_INTEGER ? Date.now() : firstMessageAt,
      lastMessageAt: lastMessageAt === 0 ? Date.now() : lastMessageAt,
      lastUpdatedAt: Date.now(),

      // === NOVAS ESTATÍSTICAS ÚNICAS ===
      messageEdits: {
        totalEdits,
        userMessageEdits: totalUserMessageEdits,
        averageEditsPerMessage,
        mostEditedMessageLength
      },

      messageRegenerations: {
        totalRegenerations,
        averageRegenerationsPerAssistantMessage,
        regenerationReasons: regenerationReasonsArray
      },

      sentimentAnalysis: {
        positive: totalPositive,
        neutral: totalNeutral,
        negative: totalNegative,
        averageSentimentScore,
        sentimentTrend
      },

      conversationPatterns: {
        averageConversationLength,
        longestConversationStreak,
        shortestResponseTime: shortestResponseTime === Number.MAX_SAFE_INTEGER ? 0 : shortestResponseTime,
        longestResponseTime,
        questionsAsked: totalQuestionsAsked,
        codeBlocksShared: totalCodeBlocksShared,
        mathExpressionsUsed: totalMathExpressionsUsed
      },

      messageComplexity: {
        averageWordsPerMessage,
        averageSentencesPerMessage,
        averageSyllablesPerWord: 0, // Não implementado ainda
        readabilityScore: 0, // Não implementado ainda
        technicalTermsUsed: totalTechnicalTermsUsed
      },

      readingMetrics: {
        estimatedReadingTime: totalEstimatedReadingTime,
        averageTimeToRespond,
        quickResponses: totalQuickResponses,
        thoughtfulResponses: totalThoughtfulResponses
      },

      featureUsage: {
        attachmentsUsed: {
          images: totalImages,
          pdfs: totalPdfs,
          totalSizeMB: totalSizeMB
        },
        latexUsage: totalLatexUsage,
        codeLanguages: codeLanguagesArray,
        topicsDiscussed: [] // Não implementado ainda
      },

      conversationEfficiency: {
        goalAchievementRate: 0, // Não implementado ainda
        clarificationRequests: totalClarificationRequests,
        followUpQuestions: 0, // Não implementado ainda
        satisfactionIndicators: totalSatisfactionIndicators
      },

      chatStatistics
    };
  }

  /**
   * Retorna estatísticas vazias para usuários sem dados
   */
  private getEmptyStatistics(): GeneralStatistics {
    return {
      totalChats: 0,
      totalMessages: 0,
      totalUserMessages: 0,
      totalAssistantMessages: 0,
      totalAttachments: 0,
      totalWords: 0,
      totalUserWords: 0,
      totalAssistantWords: 0,
      totalPromptTokens: 0,
      totalCompletionTokens: 0,
      totalTokens: 0,
      totalCost: 0,
      averageResponseTime: 0,
      averageMessageLength: 0,
      averageUserTypingSpeed: 0,
      averageTimeBetweenMessages: 0,
      totalSessions: 0,
      averageSessionDuration: 0,
      averageInactivityPeriod: 0,
      activityHeatmap: {
        hourOfDay: new Array(24).fill(0),
        dayOfWeek: new Array(7).fill(0)
      },
      mostUsedWords: [],
      modelPerformanceHistory: [],
      firstMessageAt: Date.now(),
      lastMessageAt: Date.now(),
      lastUpdatedAt: Date.now(),

      // === NOVAS ESTATÍSTICAS ÚNICAS VAZIAS ===
      messageEdits: {
        totalEdits: 0,
        userMessageEdits: 0,
        averageEditsPerMessage: 0,
        mostEditedMessageLength: 0
      },

      messageRegenerations: {
        totalRegenerations: 0,
        averageRegenerationsPerAssistantMessage: 0,
        regenerationReasons: []
      },

      sentimentAnalysis: {
        positive: 0,
        neutral: 0,
        negative: 0,
        averageSentimentScore: 0,
        sentimentTrend: []
      },

      conversationPatterns: {
        averageConversationLength: 0,
        longestConversationStreak: 0,
        shortestResponseTime: 0,
        longestResponseTime: 0,
        questionsAsked: 0,
        codeBlocksShared: 0,
        mathExpressionsUsed: 0
      },

      messageComplexity: {
        averageWordsPerMessage: 0,
        averageSentencesPerMessage: 0,
        averageSyllablesPerWord: 0,
        readabilityScore: 0,
        technicalTermsUsed: 0
      },

      readingMetrics: {
        estimatedReadingTime: 0,
        averageTimeToRespond: 0,
        quickResponses: 0,
        thoughtfulResponses: 0
      },

      featureUsage: {
        attachmentsUsed: {
          images: 0,
          pdfs: 0,
          totalSizeMB: 0
        },
        latexUsage: 0,
        codeLanguages: [],
        topicsDiscussed: []
      },

      conversationEfficiency: {
        goalAchievementRate: 0,
        clarificationRequests: 0,
        followUpQuestions: 0,
        satisfactionIndicators: 0
      },

      chatStatistics: []
    };
  }

  /**
   * Migra todas as estatísticas de um usuário para incluir contagem de palavras
   */
  async migrateAllUserWordCountStatistics(userId: string): Promise<void> {
    try {
      console.log(`Starting word count migration for user ${userId}`);

      // Obter todas as conversas do usuário
      const conversations = await getUserConversations(userId, false);

      if (conversations.length === 0) {
        console.log('No conversations found for user');
        return;
      }

      let migratedCount = 0;

      for (const conversation of conversations) {
        try {
          await chatStatisticsService.migrateWordCountStatistics(userId, conversation.id);
          migratedCount++;
        } catch (error) {
          console.error(`Error migrating chat ${conversation.id}:`, error);
        }
      }

      console.log(`Migration completed: ${migratedCount}/${conversations.length} chats migrated`);
    } catch (error) {
      console.error('Error during word count migration:', error);
      throw error;
    }
  }
}

export const generalStatisticsService = new GeneralStatisticsServiceImpl();
