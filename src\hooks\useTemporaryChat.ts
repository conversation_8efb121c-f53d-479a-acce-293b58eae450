import { useState, useCallback } from 'react';
import { ChatMessage, AttachmentMetadata, TemporaryAttachmentMetadata } from '@/lib/types/chat';

interface TemporaryMessage extends Omit<ChatMessage, 'id'> {
  id: string;
  isTemporary: true;
}

export const useTemporaryChat = () => {
  const [messages, setMessages] = useState<TemporaryMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Adicionar mensagem temporária
  const addTemporaryMessage = useCallback((
    role: 'user' | 'assistant',
    content: string,
    attachments?: AttachmentMetadata[] | TemporaryAttachmentMetadata[],
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
      cost: number;
    },
    responseTime?: number,
    typingSpeed?: number
  ): string => {
    const tempId = `temp-${role}-${Date.now()}-${Math.random()}`;
    const tempMessage: TemporaryMessage = {
      id: tempId,
      role,
      content,
      timestamp: Date.now(),
      isTemporary: true,
      ...(attachments && { attachments }),
      ...(usage && { usage }),
      ...(responseTime && { responseTime }),
      ...(typingSpeed && { typingSpeed })
    };

    setMessages(prev => [...prev, tempMessage]);
    return tempId;
  }, []);

  // Atualizar mensagem temporária (para streaming)
  const updateTemporaryMessage = useCallback((messageId: string, content: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, content } : msg
    ));
  }, []);

  // Remover mensagem temporária
  const removeTemporaryMessage = useCallback((messageId: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== messageId));
  }, []);

  // Editar mensagem temporária
  const editTemporaryMessage = useCallback((messageId: string, newContent: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, content: newContent } : msg
    ));
  }, []);

  // Limpar todas as mensagens
  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  // Obter mensagens para envio à IA (formato compatível)
  const getMessagesForAI = useCallback((): ChatMessage[] => {
    return messages.map(msg => ({
      id: msg.id,
      role: msg.role,
      content: msg.content,
      timestamp: msg.timestamp,
      ...(msg.attachments && { attachments: msg.attachments }),
      ...(msg.usage && { usage: msg.usage }),
      ...(msg.responseTime && { responseTime: msg.responseTime }),
      ...(msg.typingSpeed && { typingSpeed: msg.typingSpeed })
    }));
  }, [messages]);

  return {
    messages,
    loading,
    error,
    setLoading,
    setError,
    addTemporaryMessage,
    updateTemporaryMessage,
    removeTemporaryMessage,
    editTemporaryMessage,
    clearMessages,
    getMessagesForAI
  };
};
