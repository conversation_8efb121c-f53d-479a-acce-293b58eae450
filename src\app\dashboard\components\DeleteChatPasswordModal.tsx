'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Eye, EyeOff, Trash2, AlertTriangle } from 'lucide-react';

interface DeleteChatPasswordModalProps {
  isOpen: boolean;
  chatName: string;
  onPasswordSubmit: (password: string) => void;
  onCancel: () => void;
  error?: string;
}

const DeleteChatPasswordModal = ({ 
  isOpen, 
  chatName, 
  onPasswordSubmit, 
  onCancel, 
  error 
}: DeleteChatPasswordModalProps) => {
  const [mounted, setMounted] = useState(false);
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isOpen) {
      setPassword('');
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!password.trim()) return;

    setIsSubmitting(true);
    try {
      await onPasswordSubmit(password);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onCancel();
    }
  };

  if (!mounted || !isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center transition-opacity duration-300 z-[60]"
      onClick={onCancel}
      onKeyDown={handleKeyDown}
    >
      <div
        className="glass-effect rounded-xl shadow-2xl p-0 w-full max-w-md transform transition-all duration-300 border border-red-500/30 shadow-red-500/20"
        onClick={(e) => e.stopPropagation()}
        style={{
          background: 'rgba(15, 23, 42, 0.95)',
          backdropFilter: 'blur(20px)',
        }}
      >
        {/* Modal Header */}
        <div className="flex justify-between items-center p-6 border-b border-red-700/30">
          <h2 className="text-xl font-semibold text-white text-glow-sm flex items-center">
            <Trash2 className="w-6 h-6 mr-3 text-red-400" />
            Deletar Chat Protegido
          </h2>
          <button
            onClick={onCancel}
            className="text-red-400 hover:text-red-300 transition-colors duration-200 p-1 rounded-lg hover:bg-red-900/20"
            disabled={isSubmitting}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className="p-6">
          {/* Warning Message */}
          <div className="flex items-start gap-3 p-4 bg-red-900/20 border border-red-700/30 rounded-lg mb-6">
            <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-red-300 font-medium mb-1">Atenção!</p>
              <p className="text-red-200/80 text-sm">
                Você está prestes a deletar o chat protegido <strong>"{chatName}"</strong>. 
                Esta ação não pode ser desfeita. Digite a senha para confirmar.
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Campo de senha */}
            <div>
              <label className="block text-sm font-medium text-red-300 mb-2 flex items-center gap-2">
                <Lock className="w-4 h-4 text-red-400" />
                Senha do Chat
                <span className="text-red-400 text-lg">*</span>
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Digite a senha do chat para confirmar"
                  className="w-full bg-red-900/30 border border-red-700/50 rounded-lg px-4 py-3 pr-12 text-red-100 placeholder:text-red-400/50 focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:border-red-500/50 transition-all"
                  required
                  autoFocus
                  disabled={isSubmitting}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-400 hover:text-red-300 transition-colors duration-200"
                  disabled={isSubmitting}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {error && (
                <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4" />
                  {error}
                </p>
              )}
            </div>

            {/* Buttons */}
            <div className="flex justify-end gap-3 pt-2">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 rounded-lg text-red-300 hover:text-white transition-colors duration-200"
                disabled={isSubmitting}
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !password.trim()}
                className={`px-6 py-2 bg-gradient-to-r from-red-600 to-red-700 rounded-lg text-white font-medium shadow-lg shadow-red-700/20 hover:shadow-red-700/30 transition-all duration-200 hover:from-red-500 hover:to-red-600 flex items-center ${
                  isSubmitting || !password.trim() ? 'opacity-70 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Verificando...
                  </>
                ) : (
                  <>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Deletar Chat
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default DeleteChatPasswordModal;
