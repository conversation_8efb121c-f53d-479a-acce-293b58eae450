'use client';

import { useState, FormEvent, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { handleFirebaseError } from '@/utils/firebase-errors';
import { Eye, EyeOff, Mail, Lock, UserPlus, LogIn } from 'lucide-react';
import MaintenanceWrapper from '@/components/MaintenanceWrapper';

type AuthMode = 'login' | 'register';

interface FormData {
  username: string;
  email: string;
  password: string;
}

const AuthPage = () => {
  const [mode, setMode] = useState<AuthMode>('login');
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    password: '',
  });
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { signIn, signUp, user } = useAuth();
  const router = useRouter();

  // Efeito para redirecionar se já estiver autenticado
  useEffect(() => {
    if (user) {
      router.push('/dashboard');
    }
  }, [user, router]);

  // Efeito para criar a animação de estrelas no background
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    // Configuração das estrelas
    const stars: { x: number; y: number; radius: number; opacity: number; speed: number }[] = [];
    const starCount = Math.floor(window.innerWidth * window.innerHeight / 3000);
    
    for (let i = 0; i < starCount; i++) {
      stars.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        radius: Math.random() * 1.5 + 0.5,
        opacity: Math.random() * 0.8 + 0.2,
        speed: Math.random() * 0.05 + 0.01
      });
    }

    let animationFrameId: number;

    // Função de animação
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Desenhar estrelas
      stars.forEach(star => {
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity})`;
        ctx.fill();
        
        // Pulsar estrelas (variar opacidade)
        star.opacity += Math.sin(Date.now() * star.speed) * 0.005;
        
        // Movimento lento
        star.y -= star.speed * 0.2;
        
        // Reposicionar se saiu da tela
        if (star.y < -star.radius) {
          star.y = canvas.height + star.radius;
          star.x = Math.random() * canvas.width;
        }
      });
      
      animationFrameId = requestAnimationFrame(animate);
    };
    
    // Iniciar animação
    animate();
    
    // Redimensionar canvas se a janela for redimensionada
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      cancelAnimationFrame(animationFrameId);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const validateForm = (): boolean => {
    const { email, password, username } = formData;
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Formato de email inválido.');
      return false;
    }

    if (password.length < 6) {
      setError('A senha deve ter pelo menos 6 caracteres.');
      return false;
    }

    if (mode === 'register' && username.trim() === '') {
      setError('Nome de usuário é obrigatório.');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!validateForm()) return;

    try {
      setIsSubmitting(true);
      
      if (mode === 'login') {
        await signIn(formData.email, formData.password);
      } else {
        await signUp(formData.email, formData.password, formData.username);
      }
      
      router.push('/dashboard');
    } catch (err) {
      setError(handleFirebaseError(err));
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleMode = () => {
    setMode((prevMode) => (prevMode === 'login' ? 'register' : 'login'));
    setError(null);
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <MaintenanceWrapper>
      <main className="relative min-h-screen overflow-hidden bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-indigo-900 via-purple-950 to-slate-950">
      {/* Canvas para animação de estrelas */}
      <canvas 
        ref={canvasRef}
        className="absolute inset-0 w-full h-full pointer-events-none"
      />
      
      {/* Container principal */}
      <div className="flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Logo e título */}
        <div className="mb-6 sm:mb-8 text-center">
          <div className="flex justify-center mb-3 sm:mb-4">
            <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-purple-500/10 flex items-center justify-center border border-purple-400/30 shadow-lg shadow-purple-500/20">
              <div className="w-9 h-9 sm:w-12 sm:h-12 rounded-full bg-gradient-to-br from-indigo-400 via-purple-500 to-purple-900 flex items-center justify-center animate-pulse">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-purple-800 flex items-center justify-center">
                  <div className="w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-indigo-300"></div>
                </div>
              </div>
            </div>
          </div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-light tracking-[0.15em] sm:tracking-[0.2em] text-transparent bg-clip-text bg-gradient-to-r from-indigo-200 to-purple-300 mb-1 font-display">
            RafthorIA
          </h1>
          <p className="text-xs sm:text-sm text-purple-300/70 tracking-wider font-sans">
            Portal de Inteligência Galáctica
          </p>
        </div>

        {/* Painel de Login/Registro */}
        <div className="w-full max-w-sm sm:max-w-md">
          <div className="backdrop-blur-sm bg-slate-900/50 rounded-xl sm:rounded-2xl border border-purple-500/10 shadow-xl shadow-purple-500/5 p-6 sm:p-8 overflow-hidden relative">
            {/* Glow effect */}
            <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/10 to-indigo-500/10 rounded-xl blur-xl"></div>

            <div className="relative">
              <h2 className="text-xl sm:text-2xl font-medium text-center text-indigo-200 mb-4 sm:mb-6 font-display">
                {mode === 'login' ? 'Acessar Sistema' : 'Criar Nova Conta'}
              </h2>

              {/* Error message */}
              {error && (
                <div className="mb-3 sm:mb-4 p-3 bg-red-900/20 border border-red-800/30 rounded-lg text-red-300 text-sm">
                  {error}
                </div>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5">
                {/* Username field (only for register) */}
                {mode === 'register' && (
                  <div className="space-y-1">
                    <label htmlFor="username" className="block text-xs font-medium text-indigo-300/80">
                      Nome de usuário
                    </label>
                    <div className="relative">
                      <input
                        id="username"
                        name="username"
                        type="text"
                        required
                        value={formData.username}
                        onChange={handleChange}
                        className="block w-full px-3 sm:px-4 py-2.5 sm:py-3 pl-9 sm:pl-10 bg-slate-800/60 text-indigo-100 rounded-lg border border-indigo-500/20 focus:outline-none focus:ring-2 focus:ring-purple-500/40 focus:border-transparent transition-all placeholder:text-indigo-300/30 text-sm sm:text-base"
                        placeholder="Seu nome"
                      />
                      <UserPlus className="absolute left-2.5 sm:left-3 top-2.5 sm:top-3.5 h-4 w-4 text-indigo-400/60" />
                    </div>
                  </div>
                )}

                {/* Email field */}
                <div className="space-y-1">
                  <label htmlFor="email" className="block text-xs font-medium text-indigo-300/80">
                    Email
                  </label>
                  <div className="relative">
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="block w-full px-3 sm:px-4 py-2.5 sm:py-3 pl-9 sm:pl-10 bg-slate-800/60 text-indigo-100 rounded-lg border border-indigo-500/20 focus:outline-none focus:ring-2 focus:ring-purple-500/40 focus:border-transparent transition-all placeholder:text-indigo-300/30 text-sm sm:text-base"
                      placeholder="<EMAIL>"
                    />
                    <Mail className="absolute left-2.5 sm:left-3 top-2.5 sm:top-3.5 h-4 w-4 text-indigo-400/60" />
                  </div>
                </div>

                {/* Password field */}
                <div className="space-y-1">
                  <label htmlFor="password" className="block text-xs font-medium text-indigo-300/80">
                    Senha
                  </label>
                  <div className="relative">
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      autoComplete={mode === 'login' ? 'current-password' : 'new-password'}
                      required
                      value={formData.password}
                      onChange={handleChange}
                      className="block w-full px-3 sm:px-4 py-2.5 sm:py-3 pl-9 sm:pl-10 pr-9 sm:pr-10 bg-slate-800/60 text-indigo-100 rounded-lg border border-indigo-500/20 focus:outline-none focus:ring-2 focus:ring-purple-500/40 focus:border-transparent transition-all placeholder:text-indigo-300/30 text-sm sm:text-base"
                      placeholder={mode === 'login' ? 'Sua senha' : 'Crie uma senha (mín. 6 caracteres)'}
                    />
                    <Lock className="absolute left-2.5 sm:left-3 top-2.5 sm:top-3.5 h-4 w-4 text-indigo-400/60" />
                    <button
                      type="button"
                      onClick={toggleShowPassword}
                      className="absolute right-2.5 sm:right-3 top-2.5 sm:top-3 text-indigo-400/60 hover:text-indigo-300"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" /> : <Eye className="h-4 w-4 sm:h-5 sm:w-5" />}
                    </button>
                  </div>
                </div>

                {/* Submit button */}
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full relative py-2.5 sm:py-3 mt-4 sm:mt-6 rounded-lg flex items-center justify-center bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-600 bg-size-200 bg-pos-0 hover:bg-pos-100 text-white font-medium transition-all duration-500 shadow-lg shadow-purple-600/20 overflow-hidden text-sm sm:text-base"
                >
                  <span className="relative z-10 flex items-center">
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 sm:h-5 sm:w-5 text-white/80" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Processando...</span>
                      </>
                    ) : mode === 'login' ? (
                      <>
                        <LogIn className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                        <span>Entrar no Sistema</span>
                      </>
                    ) : (
                      <>
                        <UserPlus className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                        <span>Criar Conta</span>
                      </>
                    )}
                  </span>
                </button>

                {/* Mode toggle */}
                <div className="mt-3 sm:mt-4 text-center">
                  <button
                    type="button"
                    onClick={toggleMode}
                    className="text-indigo-300/70 text-xs sm:text-sm hover:text-indigo-200 transition-colors"
                  >
                    {mode === 'login'
                      ? 'Não tem uma conta? Criar agora'
                      : 'Já possui uma conta? Entrar'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </main>
    </MaintenanceWrapper>
  );
};

export default AuthPage; 