import { NextRequest, NextResponse } from 'next/server';
import { httpsCallable } from 'firebase/functions';
import { functions, auth } from '@/lib/firebase';
import { signInWithCustomToken } from 'firebase/auth';

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ChatRequest {
  messages: ChatMessage[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // Parse request body
    const body: ChatRequest = await request.json();

    // Validate request
    if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
      return NextResponse.json(
        { error: 'Mensagens são obrigatórias' },
        { status: 400 }
      );
    }

    // Call Firebase Function
    const chatCompletion = httpsCallable(functions, 'chatCompletion');
    
    const result = await chatCompletion({
      messages: body.messages,
      model: body.model,
      temperature: body.temperature,
      max_tokens: body.max_tokens,
    });

    return NextResponse.json(result.data);

  } catch (error) {
    console.error('Erro na API de chat completion:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('unauthenticated')) {
        return NextResponse.json(
          { error: 'Usuário não autenticado' },
          { status: 401 }
        );
      }
      
      if (error.message.includes('failed-precondition')) {
        return NextResponse.json(
          { error: 'Nenhum endpoint de IA configurado' },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
