'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useFavorites } from '@/hooks/useFavorites';
import { FavoriteMessage } from '@/lib/types/chat';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeHighlight from 'rehype-highlight';
import rehypeKatex from 'rehype-katex';
import 'highlight.js/styles/github-dark.css';
import 'katex/dist/katex.min.css';

const FavoritesPage = () => {
  const router = useRouter();
  const { user } = useAuth();
  const {
    favorites,
    loading,
    error,
    getFavorites,
    removeFromFavorites
  } = useFavorites({
    userId: user?.uid || null
  });

  // Função para pré-processar conteúdo LaTeX (igual ao MessageBubble)
  const preprocessLatex = (content: string): string => {
    // Remove comandos de documento LaTeX não suportados pelo KaTeX
    const unsupportedCommands = [
      /\\documentclass\{[^}]*\}/g,
      /\\usepackage\{[^}]*\}/g,
      /\\begin\{document\}/g,
      /\\end\{document\}/g,
      /\\maketitle/g,
      /\\tableofcontents/g,
      /\\newpage/g,
      /\\clearpage/g,
      /\\pagebreak/g,
    ];

    let processedContent = content;

    // Remove comandos não suportados
    unsupportedCommands.forEach(regex => {
      processedContent = processedContent.replace(regex, '');
    });

    // Converte comandos de seção para markdown
    processedContent = processedContent
      .replace(/\\section\*?\{([^}]+)\}/g, '## $1')
      .replace(/\\subsection\*?\{([^}]+)\}/g, '### $1')
      .replace(/\\subsubsection\*?\{([^}]+)\}/g, '#### $1')
      .replace(/\\paragraph\{([^}]+)\}/g, '##### $1')
      .replace(/\\subparagraph\{([^}]+)\}/g, '###### $1');

    // Converte comandos de formatação básica
    processedContent = processedContent
      .replace(/\\textbf\{([^}]+)\}/g, '**$1**')
      .replace(/\\textit\{([^}]+)\}/g, '*$1*')
      .replace(/\\emph\{([^}]+)\}/g, '*$1*')
      .replace(/\\underline\{([^}]+)\}/g, '<u>$1</u>')
      .replace(/\\texttt\{([^}]+)\}/g, '`$1`');

    // Converte listas LaTeX para markdown
    processedContent = processedContent
      .replace(/\\begin\{itemize\}/g, '')
      .replace(/\\end\{itemize\}/g, '')
      .replace(/\\begin\{enumerate\}/g, '')
      .replace(/\\end\{enumerate\}/g, '')
      .replace(/\\item\s+/g, '- ');

    // Converte ambientes matemáticos especiais
    const convertMathEnvironments = (text: string) => {
      const environments = [
        { start: '\\begin{theorem}', end: '\\end{theorem}', label: '**Teorema:**' },
        { start: '\\begin{definition}', end: '\\end{definition}', label: '**Definição:**' },
        { start: '\\begin{lemma}', end: '\\end{lemma}', label: '**Lema:**' },
        { start: '\\begin{proof}', end: '\\end{proof}', label: '**Demonstração:**' },
        { start: '\\begin{example}', end: '\\end{example}', label: '**Exemplo:**' },
        { start: '\\begin{remark}', end: '\\end{remark}', label: '**Observação:**' }
      ];

      let result = text;
      environments.forEach(env => {
        const startIndex = result.indexOf(env.start);
        const endIndex = result.indexOf(env.end);
        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
          const content = result.substring(startIndex + env.start.length, endIndex).trim();
          const replacement = `> ${env.label} ${content}`;
          result = result.substring(0, startIndex) + replacement + result.substring(endIndex + env.end.length);
        }
      });
      return result;
    };

    processedContent = convertMathEnvironments(processedContent);

    // Converte comandos matemáticos específicos
    processedContent = processedContent
      .replace(/\\qed/g, '∎')
      .replace(/\\therefore/g, '∴')
      .replace(/\\because/g, '∵')
      .replace(/\\implies/g, '⟹')
      .replace(/\\iff/g, '⟺');

    // Remove linhas vazias excessivas
    processedContent = processedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

    return processedContent.trim();
  };

  const [filteredFavorites, setFilteredFavorites] = useState<FavoriteMessage[]>([]);
  const [filters, setFilters] = useState({
    chatId: '',
    role: '' as '' | 'user' | 'assistant',
    dateRange: '' as '' | 'today' | 'week' | 'month' | 'all',
    searchTerm: ''
  });

  // Aplicar filtros
  useEffect(() => {
    let filtered = [...favorites];

    // Filtro por chat
    if (filters.chatId) {
      filtered = filtered.filter(fav => fav.chatId === filters.chatId);
    }

    // Filtro por role
    if (filters.role) {
      filtered = filtered.filter(fav => fav.role === filters.role);
    }

    // Filtro por data
    if (filters.dateRange && filters.dateRange !== 'all') {
      const now = Date.now();
      let startDate = 0;

      switch (filters.dateRange) {
        case 'today':
          startDate = now - (24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = now - (7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = now - (30 * 24 * 60 * 60 * 1000);
          break;
      }

      filtered = filtered.filter(fav => fav.favoritedAt >= startDate);
    }

    // Filtro por termo de busca
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(fav => 
        fav.content.toLowerCase().includes(searchLower) ||
        fav.chatName.toLowerCase().includes(searchLower)
      );
    }

    setFilteredFavorites(filtered);
  }, [favorites, filters]);

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleRemoveFavorite = async (messageId: string, chatId: string) => {
    try {
      await removeFromFavorites(messageId, chatId);
    } catch (error) {
      console.error('Erro ao remover favorito:', error);
    }
  };

  const getUniqueChats = () => {
    const chats = favorites.reduce((acc, fav) => {
      if (!acc.find(chat => chat.id === fav.chatId)) {
        acc.push({ id: fav.chatId, name: fav.chatName });
      }
      return acc;
    }, [] as { id: string; name: string }[]);
    
    return chats.sort((a, b) => a.name.localeCompare(b.name));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-lg">Carregando favoritos...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Mensagens Favoritas</h1>
              <p className="text-blue-300/70">
                Gerencie suas mensagens favoritas de todos os chats
              </p>
            </div>

            {/* Botão Voltar */}
            <button
              onClick={() => router.push('/dashboard')}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-900/20 hover:bg-blue-900/40 border border-blue-700/30 hover:border-blue-600/50 rounded-xl text-blue-300 hover:text-blue-200 transition-all duration-200 backdrop-blur-sm"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              <span>Voltar ao Dashboard</span>
            </button>
          </div>
        </div>

        {/* Filtros */}
        <div className="bg-blue-900/20 backdrop-blur-sm border border-blue-700/30 rounded-2xl p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Filtros</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Filtro por Chat */}
            <div>
              <label className="block text-blue-300 text-sm font-medium mb-2">
                Chat
              </label>
              <select
                value={filters.chatId}
                onChange={(e) => setFilters(prev => ({ ...prev, chatId: e.target.value }))}
                className="w-full bg-blue-800/50 border border-blue-600/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos os chats</option>
                {getUniqueChats().map(chat => (
                  <option key={chat.id} value={chat.id}>
                    {chat.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro por Tipo */}
            <div>
              <label className="block text-blue-300 text-sm font-medium mb-2">
                Tipo
              </label>
              <select
                value={filters.role}
                onChange={(e) => setFilters(prev => ({ ...prev, role: e.target.value as '' | 'user' | 'assistant' }))}
                className="w-full bg-blue-800/50 border border-blue-600/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos os tipos</option>
                <option value="user">Minhas mensagens</option>
                <option value="assistant">Respostas da IA</option>
              </select>
            </div>

            {/* Filtro por Data */}
            <div>
              <label className="block text-blue-300 text-sm font-medium mb-2">
                Período
              </label>
              <select
                value={filters.dateRange}
                onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value as '' | 'today' | 'week' | 'month' | 'all' }))}
                className="w-full bg-blue-800/50 border border-blue-600/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Todos os períodos</option>
                <option value="today">Hoje</option>
                <option value="week">Última semana</option>
                <option value="month">Último mês</option>
              </select>
            </div>

            {/* Busca */}
            <div>
              <label className="block text-blue-300 text-sm font-medium mb-2">
                Buscar
              </label>
              <input
                type="text"
                value={filters.searchTerm}
                onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                placeholder="Buscar nas mensagens..."
                className="w-full bg-blue-800/50 border border-blue-600/30 rounded-lg px-3 py-2 text-white placeholder-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-blue-900/20 backdrop-blur-sm border border-blue-700/30 rounded-xl p-4">
            <div className="text-2xl font-bold text-white">{favorites.length}</div>
            <div className="text-blue-300/70 text-sm">Total de favoritos</div>
          </div>
          <div className="bg-blue-900/20 backdrop-blur-sm border border-blue-700/30 rounded-xl p-4">
            <div className="text-2xl font-bold text-white">{filteredFavorites.length}</div>
            <div className="text-blue-300/70 text-sm">Favoritos filtrados</div>
          </div>
          <div className="bg-blue-900/20 backdrop-blur-sm border border-blue-700/30 rounded-xl p-4">
            <div className="text-2xl font-bold text-white">{getUniqueChats().length}</div>
            <div className="text-blue-300/70 text-sm">Chats com favoritos</div>
          </div>
        </div>

        {/* Lista de Favoritos */}
        {error && (
          <div className="bg-red-900/20 border border-red-700/30 rounded-xl p-4 mb-6">
            <p className="text-red-300">Erro: {error}</p>
          </div>
        )}

        {filteredFavorites.length === 0 ? (
          <div className="bg-blue-900/20 backdrop-blur-sm border border-blue-700/30 rounded-2xl p-8 text-center">
            <div className="text-blue-300/50 text-lg">
              {favorites.length === 0 
                ? 'Nenhuma mensagem favoritada ainda'
                : 'Nenhuma mensagem encontrada com os filtros aplicados'
              }
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredFavorites.map((favorite) => (
              <div
                key={favorite.id}
                className="bg-blue-900/20 backdrop-blur-sm border border-blue-700/30 rounded-2xl p-6 group hover:bg-blue-900/30 transition-all duration-200"
              >
                {/* Header da mensagem */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      favorite.role === 'user'
                        ? 'bg-gradient-to-br from-green-500 to-emerald-600'
                        : 'bg-gradient-to-br from-blue-500 to-cyan-600'
                    }`}>
                      {favorite.role === 'user' ? (
                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <div className="text-white font-medium">
                        {favorite.role === 'user' ? 'Você' : 'IA'}
                      </div>
                      <div className="text-blue-300/70 text-sm">
                        {favorite.chatName}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className="text-blue-300/70 text-sm">
                      {formatTime(favorite.timestamp)}
                    </div>
                    <button
                      onClick={() => handleRemoveFavorite(favorite.messageId, favorite.chatId)}
                      className="p-2 rounded-lg bg-red-900/40 hover:bg-red-800/60 text-red-400 hover:text-red-300 transition-all duration-200 opacity-0 group-hover:opacity-100"
                      title="Remover dos favoritos"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Conteúdo da mensagem */}
                <div className="text-white">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm, remarkMath]}
                    rehypePlugins={[rehypeHighlight, rehypeKatex]}
                    components={{
                      // Headings com estilo matemático aprimorado
                      h1: ({ children }) => (
                        <h1 className="text-2xl font-bold mb-4 text-blue-100 border-b border-blue-600/50 pb-2">
                          {children}
                        </h1>
                      ),
                      h2: ({ children }) => (
                        <h2 className="text-xl font-semibold mb-3 text-blue-100 border-l-4 border-blue-500 pl-3">
                          {children}
                        </h2>
                      ),
                      h3: ({ children }) => (
                        <h3 className="text-lg font-medium mb-2 text-blue-200 border-l-2 border-cyan-500 pl-2">
                          {children}
                        </h3>
                      ),
                      h4: ({ children }) => (
                        <h4 className="text-base font-medium mb-2 text-blue-200">
                          {children}
                        </h4>
                      ),
                      h5: ({ children }) => (
                        <h5 className="text-sm font-medium mb-1 text-blue-300">
                          {children}
                        </h5>
                      ),
                      h6: ({ children }) => (
                        <h6 className="text-sm font-medium mb-1 text-blue-300">
                          {children}
                        </h6>
                      ),

                      // Paragraphs - usando div para evitar problemas de nesting
                      p: ({ children }) => (
                        <div className="mb-3 last:mb-0 whitespace-pre-wrap">
                          {children}
                        </div>
                      ),

                      // Lists
                      ul: ({ children }) => (
                        <ul className="list-disc list-inside mb-3 space-y-1 ml-4">
                          {children}
                        </ul>
                      ),
                      ol: ({ children }) => (
                        <ol className="list-decimal list-inside mb-3 space-y-1 ml-4">
                          {children}
                        </ol>
                      ),
                      li: ({ children }) => (
                        <li className="text-blue-200">
                          {children}
                        </li>
                      ),

                      // Code blocks
                      code: ({ inline, className, children, ...props }: any) => {
                        const match = /language-(\w+)/.exec(className || '');
                        return !inline ? (
                          <div className="relative mb-4">
                            <div className="bg-blue-950/80 rounded-lg border border-blue-700/50 overflow-hidden">
                              {match && (
                                <div className="bg-blue-900/80 px-4 py-2 text-xs text-blue-300 border-b border-blue-700/50 font-mono">
                                  {match[1]}
                                </div>
                              )}
                              <pre className="p-4 overflow-x-auto">
                                <code className={className} {...props}>
                                  {children}
                                </code>
                              </pre>
                            </div>
                          </div>
                        ) : (
                          <code className="bg-blue-900/60 text-blue-200 px-2 py-1 rounded text-sm font-mono border border-blue-700/30" {...props}>
                            {children}
                          </code>
                        );
                      },

                      // Blockquotes com estilo matemático
                      blockquote: ({ children }) => (
                        <blockquote className="border-l-4 border-blue-500/50 pl-4 py-2 mb-3 bg-blue-900/30 rounded-r-lg italic text-blue-200 relative">
                          <div className="absolute -left-2 top-2 w-4 h-4 bg-blue-500/20 rounded-full"></div>
                          {children}
                        </blockquote>
                      ),

                      // Links
                      a: ({ href, children }) => (
                        <a
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 underline decoration-blue-500/50 hover:decoration-blue-400 transition-colors"
                        >
                          {children}
                        </a>
                      ),

                      // Tables
                      table: ({ children }) => (
                        <div className="overflow-x-auto mb-4">
                          <table className="min-w-full border border-blue-700/50 rounded-lg overflow-hidden">
                            {children}
                          </table>
                        </div>
                      ),
                      thead: ({ children }) => (
                        <thead className="bg-blue-900/60">
                          {children}
                        </thead>
                      ),
                      tbody: ({ children }) => (
                        <tbody className="bg-blue-950/20">
                          {children}
                        </tbody>
                      ),
                      tr: ({ children }) => (
                        <tr className="border-b border-blue-700/30 hover:bg-blue-900/30 transition-colors">
                          {children}
                        </tr>
                      ),
                      th: ({ children }) => (
                        <th className="px-4 py-2 text-left text-blue-200 font-medium">
                          {children}
                        </th>
                      ),
                      td: ({ children }) => (
                        <td className="px-4 py-2 text-blue-200">
                          {children}
                        </td>
                      ),

                      // Horizontal rule
                      hr: () => (
                        <hr className="my-6 border-slate-600/50" />
                      ),

                      // Strong and emphasis
                      strong: ({ children }) => (
                        <strong className="font-semibold text-slate-100">
                          {children}
                        </strong>
                      ),
                      em: ({ children }) => (
                        <em className="italic text-slate-200">
                          {children}
                        </em>
                      ),

                      // Strikethrough
                      del: ({ children }) => (
                        <del className="line-through text-slate-400">
                          {children}
                        </del>
                      ),
                    }}
                  >
                    {preprocessLatex(favorite.content)}
                  </ReactMarkdown>
                </div>

                {/* Anexos */}
                {favorite.attachments && favorite.attachments.length > 0 && (
                  <div className="mt-4 space-y-3">
                    <div className="text-blue-300/70 text-sm font-medium">Anexos:</div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {favorite.attachments.map((attachment) => (
                        <div key={attachment.id} className="bg-blue-800/30 rounded-lg p-3 hover:bg-blue-800/40 transition-colors">
                          {attachment.type === 'image' ? (
                            <div className="space-y-2">
                              <div className="relative group">
                                <img
                                  src={attachment.url}
                                  alt={attachment.filename}
                                  className="w-full h-32 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                                  onClick={() => window.open(attachment.url, '_blank')}
                                />
                                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 rounded transition-colors flex items-center justify-center">
                                  <svg className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                  </svg>
                                </div>
                              </div>
                              <div className="text-blue-300 text-xs truncate" title={attachment.filename}>
                                {attachment.filename}
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-blue-300 text-sm truncate" title={attachment.filename}>
                                  {attachment.filename}
                                </div>
                                <div className="text-blue-400/60 text-xs">
                                  {(attachment.size / 1024).toFixed(1)} KB
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FavoritesPage;
