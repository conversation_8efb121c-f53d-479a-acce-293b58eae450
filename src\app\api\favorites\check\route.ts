import { NextRequest, NextResponse } from 'next/server';
import serverFavoritesService from '@/lib/serverFavoritesService';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// GET - Verificar se uma mensagem está nos favoritos
export async function GET(request: NextRequest) {
  try {
    const userId = request.nextUrl.searchParams.get('userId');
    const messageId = request.nextUrl.searchParams.get('messageId');
    const chatId = request.nextUrl.searchParams.get('chatId');

    if (!userId || !messageId || !chatId) {
      return NextResponse.json(
        { error: 'userId, messageId e chatId são obrigatórios' },
        { status: 400 }
      );
    }

    const isFavorite = await serverFavoritesService.isFavorite(userId, messageId, chatId);

    return NextResponse.json({
      success: true,
      isFavorite
    });

  } catch (error) {
    console.error('Erro ao verificar favorito:', error);
    // Return false instead of error to avoid breaking the UI
    return NextResponse.json({
      success: true,
      isFavorite: false
    });
  }
}
