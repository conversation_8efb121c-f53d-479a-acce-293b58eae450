import { chatStatisticsService } from '@/lib/chatStatisticsService';
import { generalStatisticsService } from '@/lib/generalStatisticsService';

/**
 * Função de teste para verificar se a contagem de palavras está funcionando
 */
export const testWordCountSystem = async (userId: string, chatId: string) => {
  console.log('🧪 Testing word count system...');
  
  try {
    // 1. Verificar estatísticas do chat
    const chatStats = await chatStatisticsService.getChatStatistics(userId, chatId);
    console.log('📊 Chat Statistics:', {
      totalWords: chatStats?.totalWords || 0,
      userWords: chatStats?.userWords || 0,
      assistantWords: chatStats?.assistantWords || 0
    });

    // 2. Verificar estatísticas gerais
    const generalStats = await generalStatisticsService.getGeneralStatistics(userId);
    console.log('📈 General Statistics:', {
      totalWords: generalStats.totalWords || 0,
      totalUserWords: generalStats.totalUserWords || 0,
      totalAssistantWords: generalStats.totalAssistantWords || 0
    });

    // 3. Executar migração se necessário
    if (!chatStats?.totalWords && !chatStats?.userWords && !chatStats?.assistantWords) {
      console.log('🔄 Running migration for word count...');
      await chatStatisticsService.migrateWordCountStatistics(userId, chatId);
      
      // Verificar novamente após migração
      const updatedStats = await chatStatisticsService.getChatStatistics(userId, chatId);
      console.log('📊 Updated Chat Statistics after migration:', {
        totalWords: updatedStats?.totalWords || 0,
        userWords: updatedStats?.userWords || 0,
        assistantWords: updatedStats?.assistantWords || 0
      });
    }

    return true;
  } catch (error) {
    console.error('❌ Error testing word count system:', error);
    return false;
  }
};

/**
 * Função para executar migração completa de um usuário
 */
export const migrateUserWordCount = async (userId: string) => {
  console.log('🔄 Starting complete word count migration for user...');
  
  try {
    await generalStatisticsService.migrateAllUserWordCountStatistics(userId);
    console.log('✅ Migration completed successfully');
    return true;
  } catch (error) {
    console.error('❌ Error during migration:', error);
    return false;
  }
};

// Para usar no console do navegador:
// window.testWordCount = testWordCountSystem;
// window.migrateWordCount = migrateUserWordCount;
if (typeof window !== 'undefined') {
  (window as any).testWordCount = testWordCountSystem;
  (window as any).migrateWordCount = migrateUserWordCount;
}
