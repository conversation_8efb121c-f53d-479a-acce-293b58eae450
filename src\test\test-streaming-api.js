// Teste completo da API de streaming
// Execute este código no console do navegador (F12)

async function testStreamingAPI() {
  console.log('🧪 Testando API de streaming...');

  try {
    // 1. <PERSON>iro, criar um stream
    console.log('📤 Criando stream...');
    
    const createResponse = await fetch('/api/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'Olá, como você está?' }
        ],
        model: 'google/gemini-2.5-flash',
        temperature: 0.7,
        max_tokens: 2048,
        streaming: true
      })
    });

    if (!createResponse.ok) {
      const error = await createResponse.json();
      console.error('❌ Erro ao criar stream:', error);
      return;
    }

    const { streamId } = await createResponse.json();
    console.log('✅ Stream criado com ID:', streamId);

    // 2. Conectar ao stream via SSE
    console.log('🔗 Conectando ao stream...');
    
    const eventSource = new EventSource(`/api/stream?streamId=${streamId}&token=test-token`);
    
    let receivedData = [];
    let fullContent = '';

    eventSource.onopen = () => {
      console.log('✅ Conexão SSE estabelecida');
    };

    eventSource.onmessage = (event) => {
      console.log('📨 Dados recebidos:', event.data);
      
      try {
        const data = JSON.parse(event.data);
        receivedData.push(data);
        
        if (data.type === 'connected') {
          console.log('🔗 Conectado ao stream');
        } else if (data.content) {
          fullContent += data.content;
          console.log('📝 Conteúdo parcial:', fullContent);
        } else if (data.isComplete) {
          console.log('✅ Stream completo!');
          console.log('📄 Conteúdo final:', fullContent);
          eventSource.close();
        }
      } catch (error) {
        console.error('❌ Erro ao processar dados:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('❌ Erro no EventSource:', error);
      eventSource.close();
    };

    // 3. Testar cancelamento após 3 segundos
    setTimeout(async () => {
      console.log('🛑 Testando cancelamento...');
      
      try {
        const cancelResponse = await fetch(`/api/stream/${streamId}`, {
          method: 'DELETE'
        });
        
        if (cancelResponse.ok) {
          console.log('✅ Stream cancelado com sucesso');
        } else {
          console.error('❌ Erro ao cancelar stream');
        }
      } catch (error) {
        console.error('❌ Erro na requisição de cancelamento:', error);
      }
      
      eventSource.close();
    }, 3000);

    return {
      streamId,
      success: true
    };

  } catch (error) {
    console.error('❌ Erro no teste:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Função para testar apenas a criação de stream
async function testStreamCreation() {
  console.log('🧪 Testando criação de stream...');
  
  try {
    const response = await fetch('/api/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'Teste simples' }
        ],
        model: 'google/gemini-2.5-flash',
        streaming: true
      })
    });

    const result = await response.json();
    console.log('📋 Resultado:', result);
    
    if (response.ok) {
      console.log('✅ Stream criado com sucesso:', result.streamId);
      return result.streamId;
    } else {
      console.error('❌ Erro:', result.error);
      return null;
    }
  } catch (error) {
    console.error('❌ Erro na requisição:', error);
    return null;
  }
}

// Executar teste
console.log('🚀 Iniciando teste da API de streaming...');
console.log('📝 Para executar o teste completo, digite: testStreamingAPI()');
console.log('📝 Para testar apenas criação, digite: testStreamCreation()');

// Exportar funções para uso global
window.testStreamingAPI = testStreamingAPI;
window.testStreamCreation = testStreamCreation;
