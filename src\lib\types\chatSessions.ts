import { ChatMessage } from './chat';

export interface ChatSession {
  id: string;
  startIndex: number; // índice da primeira mensagem na sessão
  endIndex: number; // índice da última mensagem na sessão
  wordCount: number; // total de palavras na sessão
  messages: ChatMessage[]; // mensagens da sessão
  isLoaded: boolean; // se a sessão está carregada na interface
}

export interface ChatSessionMetadata {
  totalSessions: number;
  currentSessionIndex: number;
  totalWords: number;
  wordsPerSession: number;
  sessionsEnabled: boolean;
}

export interface SessionNavigationState {
  loadedSessions: Set<number>; // índices das sessões carregadas
  currentSession: number; // sessão atual sendo visualizada
  allSessionsLoaded: boolean; // se todas as sessões estão carregadas
}
