import { chatStatisticsService } from '../lib/chatStatisticsService';
import { deleteConversation } from '../lib/conversationService';
import { chatCleanupService } from '../lib/chatCleanupService';
import { db } from '../lib/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';

/**
 * Script para testar se as estatísticas estão sendo deletadas corretamente
 */
export async function testStatisticsDeletion() {
  const testUserId = 'test-user-stats-deletion';
  const testChatId = 'test-chat-stats-deletion';
  
  console.log('🧪 Iniciando teste de exclusão de estatísticas...');
  
  try {
    // 1. Criar um chat de teste com estatísticas
    console.log('📝 Criando chat de teste com estatísticas...');
    
    // Criar documento principal do chat
    const chatRef = doc(db, 'usuarios', testUserId, 'conversas', testChatId);
    await setDoc(chatRef, {
      title: 'Chat de Teste para Exclusão de Estatísticas',
      createdAt: Date.now(),
      lastMessageAt: Date.now(),
      messageCount: 5
    });
    
    // Criar estatísticas para o chat
    const stats = await chatStatisticsService.initializeChatStatistics(testUserId, testChatId);
    await chatStatisticsService.saveChatStatistics(testUserId, testChatId, {
      ...stats,
      totalMessages: 5,
      userMessages: 3,
      assistantMessages: 2
    });
    
    // 2. Verificar se as estatísticas foram criadas
    console.log('🔍 Verificando se as estatísticas foram criadas...');
    const createdStats = await chatStatisticsService.getChatStatistics(testUserId, testChatId);
    
    if (!createdStats) {
      console.error('❌ Erro: Estatísticas não foram criadas!');
      return false;
    }
    
    console.log('✅ Estatísticas criadas com sucesso:', {
      totalMessages: createdStats.totalMessages,
      userMessages: createdStats.userMessages,
      assistantMessages: createdStats.assistantMessages
    });
    
    // 3. Verificar se o documento de estatísticas existe no Firestore
    const statsDocRef = doc(db, 'usuarios', testUserId, 'conversas', testChatId, 'estatisticas', 'dados');
    const statsDocBefore = await getDoc(statsDocRef);
    
    if (!statsDocBefore.exists()) {
      console.error('❌ Erro: Documento de estatísticas não existe no Firestore!');
      return false;
    }
    
    console.log('✅ Documento de estatísticas confirmado no Firestore');
    
    // 4. Deletar o chat usando a função normal
    console.log('🗑️ Deletando chat usando deleteConversation...');
    await deleteConversation(testUserId, testChatId);
    
    // 5. Verificar se as estatísticas foram deletadas
    console.log('🔍 Verificando se as estatísticas foram deletadas...');
    
    // Verificar via service
    const deletedStats = await chatStatisticsService.getChatStatistics(testUserId, testChatId);
    
    if (deletedStats) {
      console.error('❌ Erro: Estatísticas ainda existem via service!');
      console.log('Estatísticas encontradas:', deletedStats);
      return false;
    }
    
    // Verificar diretamente no Firestore
    const statsDocAfter = await getDoc(statsDocRef);
    
    if (statsDocAfter.exists()) {
      console.error('❌ Erro: Documento de estatísticas ainda existe no Firestore!');
      console.log('Dados encontrados:', statsDocAfter.data());
      return false;
    }
    
    console.log('✅ Estatísticas deletadas com sucesso!');
    
    // 6. Teste adicional: Verificar limpeza de dados órfãos
    console.log('🧹 Testando limpeza de dados órfãos...');
    
    // Criar estatísticas órfãs (sem chat principal)
    const orphanChatId = 'orphan-chat-test';
    const orphanStats = await chatStatisticsService.initializeChatStatistics(testUserId, orphanChatId);
    await chatStatisticsService.saveChatStatistics(testUserId, orphanChatId, orphanStats);
    
    // Executar auditoria
    // const auditResult = await chatCleanupService.auditOrphanedData(testUserId);
    // console.log('📊 Resultado da auditoria:', auditResult);

    // Executar limpeza
    // const cleanupResult = await chatCleanupService.cleanupOrphanedChat(testUserId, orphanChatId);
    // console.log('🧹 Resultado da limpeza:', cleanupResult);
    
    // Verificar se as estatísticas órfãs foram removidas
    const orphanStatsAfterCleanup = await chatStatisticsService.getChatStatistics(testUserId, orphanChatId);
    
    if (orphanStatsAfterCleanup) {
      console.error('❌ Erro: Estatísticas órfãs não foram removidas!');
      return false;
    }
    
    console.log('✅ Estatísticas órfãs removidas com sucesso!');
    
    console.log('🎉 Todos os testes de exclusão de estatísticas passaram!');
    return true;
    
  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
    return false;
  }
}

/**
 * Função para limpar dados de teste
 */
export async function cleanupTestData() {
  const testUserId = 'test-user-stats-deletion';
  const testChatIds = ['test-chat-stats-deletion', 'orphan-chat-test'];
  
  console.log('🧹 Limpando dados de teste...');
  
  for (const chatId of testChatIds) {
    try {
      await deleteConversation(testUserId, chatId);
      await chatCleanupService.cleanupOrphanedChat(testUserId, chatId);
    } catch (error) {
      console.warn(`Erro ao limpar chat ${chatId}:`, error);
    }
  }
  
  console.log('✅ Limpeza de dados de teste concluída');
}

// Executar teste se chamado diretamente
if (require.main === module) {
  testStatisticsDeletion()
    .then(success => {
      if (success) {
        console.log('✅ Teste concluído com sucesso!');
      } else {
        console.log('❌ Teste falhou!');
      }
      return cleanupTestData();
    })
    .then(() => {
      console.log('🏁 Processo completo!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Erro fatal:', error);
      process.exit(1);
    });
}
