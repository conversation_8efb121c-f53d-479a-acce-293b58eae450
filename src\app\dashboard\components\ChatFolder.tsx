'use client';

import { useState } from 'react';
import { ChatFolder as ChatFolderType } from '@/lib/types/chatFolder';
import { Conversation } from '@/lib/conversationService';
import DraggableConversation from './DraggableConversation';
import DroppableFolder from './DroppableFolder';
import { Lock } from 'lucide-react';

interface ChatFolderProps {
  folder: ChatFolderType;
  conversations: Conversation[];
  isExpanded: boolean;
  onToggleExpansion: (folderId: string) => void;
  onEditFolder?: (folderId: string) => void;
  onDeleteFolder?: (folderId: string) => void;
  onSelectConversation: (conversation: Conversation) => void;
  activeConversationId: string | null;
  onEditConversation?: (conversationId: string) => void;
  onDeleteConversation?: (conversationId: string, conversationName: string) => void;
  onPinConversation?: (conversationId: string, e: React.MouseEvent) => void;
}

export default function ChatFolder({
  folder,
  conversations,
  isExpanded,
  onToggleExpansion,
  onEditFolder,
  onDeleteFolder,
  onSelectConversation,
  activeConversationId,
  onEditConversation,
  onDeleteConversation,
  onPinConversation
}: ChatFolderProps) {
  const [isHovered, setIsHovered] = useState(false);

  const handleToggleExpansion = () => {
    onToggleExpansion(folder.id);
  };

  const handleEditFolder = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEditFolder) {
      onEditFolder(folder.id);
    }
  };

  const handleDeleteFolder = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDeleteFolder) {
      onDeleteFolder(folder.id);
    }
  };

  const formatTimeAgo = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp.seconds ? new Date(timestamp.seconds * 1000) : new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}d`;
  };

  return (
    <DroppableFolder folder={folder} className="mb-2 relative">
      {/* Cabeçalho da Pasta */}
      <div
        className={`group relative rounded-lg transition-all duration-300 cursor-pointer ${
          isHovered ? 'bg-blue-800/40' : 'hover:bg-blue-800/30'
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleToggleExpansion}
      >
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            {/* Ícone de expansão */}
            <div className="flex-shrink-0">
              <svg
                className={`w-4 h-4 text-blue-300 transition-transform duration-200 ${
                  isExpanded ? 'rotate-90' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>

            {/* Ícone da pasta */}
            <div 
              className="w-6 h-6 rounded flex items-center justify-center flex-shrink-0"
              style={{ backgroundColor: folder.color + '40' }}
            >
              <svg
                className="w-4 h-4"
                style={{ color: folder.color }}
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z" />
              </svg>
            </div>

            {/* Nome da pasta e contador */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <h4 className="text-sm font-semibold text-blue-100 truncate">
                  {folder.name}
                </h4>
                <span className="text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full">
                  {conversations.length}
                </span>
              </div>
              {folder.description && (
                <p className="text-xs text-blue-300/60 truncate mt-0.5">
                  {folder.description}
                </p>
              )}
            </div>
          </div>

          {/* Ações da pasta (visíveis no hover) */}
          <div className={`flex items-center space-x-1 transition-all duration-300 ${
            isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'
          }`}>
            {onEditFolder && (
              <button
                className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200"
                title="Editar pasta"
                onClick={handleEditFolder}
              >
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
            )}
            {onDeleteFolder && (
              <button
                className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200"
                title="Deletar pasta"
                onClick={handleDeleteFolder}
              >
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Lista de conversas (quando expandida) */}
      {isExpanded && (
        <div className="ml-6 mt-2 space-y-1">
          {conversations.map((conversation) => {
            const isPinned = conversation.isFixed || false;
            return (
              <DraggableConversation
                key={conversation.id}
                conversation={conversation}
                isActive={activeConversationId === conversation.id}
              >
                <div
                  className={`group relative rounded-xl transition-all duration-300 ${
                    activeConversationId === conversation.id
                      ? 'bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg'
                      : 'hover:bg-blue-800/30 border border-transparent'
                  }`}
                >
                <button
                  className="w-full text-left p-3 flex items-start space-x-3"
                  onClick={() => onSelectConversation(conversation)}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 transition-all duration-300 relative ${
                    activeConversationId === conversation.id
                      ? 'bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg'
                      : 'bg-blue-700/50 group-hover:bg-blue-600/70'
                  }`}>
                    {isPinned && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border border-blue-900"></div>
                    )}

                    {/* Ícone de chat protegido por senha */}
                    {conversation.password && (
                      <div className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                        <Lock className="w-2 h-2 text-white" />
                      </div>
                    )}
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                    </svg>
                  </div>

                  <div className="overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20">
                    <h4 className={`truncate text-sm font-semibold mb-1 ${
                      activeConversationId === conversation.id
                        ? 'text-white'
                        : 'text-blue-100 group-hover:text-white'
                    }`}>
                      {conversation.name}
                    </h4>
                    <p className={`truncate text-xs leading-relaxed transition-all duration-300 ${
                      activeConversationId === conversation.id
                        ? 'text-blue-200/80'
                        : 'text-blue-300/70 group-hover:text-blue-200'
                    }`}>
                      {conversation.ultimaMensagem || 'Nenhuma mensagem ainda...'}
                    </p>
                    {conversation.ultimaMensagemEm && (
                      <span className={`text-xs mt-1 block ${
                        activeConversationId === conversation.id
                          ? 'text-blue-300/60'
                          : 'text-blue-400/50 group-hover:text-blue-300/70'
                      }`}>
                        {formatTimeAgo(conversation.ultimaMensagemEm)}
                      </span>
                    )}
                  </div>
                </button>

                {/* Ações da conversa */}
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  {onPinConversation && (
                    <button
                      className={`p-1.5 rounded-lg transition-all duration-200 backdrop-blur-sm ${
                        isPinned
                          ? 'bg-yellow-600/90 hover:bg-yellow-500/90 text-yellow-200 hover:text-white'
                          : 'bg-blue-700/90 hover:bg-yellow-600/90 text-blue-300 hover:text-white'
                      }`}
                      title={isPinned ? "Desafixar conversa" : "Fixar conversa"}
                      onClick={(e) => onPinConversation(conversation.id, e)}
                    >
                      <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                      </svg>
                    </button>
                  )}
                  {onEditConversation && (
                    <button
                      className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm"
                      title="Editar"
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditConversation(conversation.id);
                      }}
                    >
                      <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                  )}
                  {onDeleteConversation && (
                    <button
                      className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm"
                      title="Deletar"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteConversation(conversation.id, conversation.name);
                      }}
                    >
                      <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
              </DraggableConversation>
            );
          })}
        </div>
      )}
    </DroppableFolder>
  );
}
