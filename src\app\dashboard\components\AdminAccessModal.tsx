'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { adminService } from '@/lib/adminService';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Lock, Eye, EyeOff, Shield, Settings } from 'lucide-react';

interface AdminAccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdminAccessModal = ({ isOpen, onClose }: AdminAccessModalProps) => {
  const [mounted, setMounted] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFirstTime, setIsFirstTime] = useState(false);
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    setMounted(true);
    if (isOpen) {
      checkAdminPassword();
    }
  }, [isOpen]);

  const checkAdminPassword = async () => {
    try {
      const hasPassword = await adminService.hasAdminPassword();
      setIsFirstTime(!hasPassword);
    } catch (error) {
      console.error('Erro ao verificar senha de admin:', error);
      setError('Erro ao verificar configurações de admin');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!password.trim()) {
      setError('Por favor, digite a senha');
      return;
    }

    if (isFirstTime) {
      if (password.length < 6) {
        setError('A senha deve ter pelo menos 6 caracteres');
        return;
      }
      if (password !== confirmPassword) {
        setError('As senhas não coincidem');
        return;
      }
    }

    try {
      setIsLoading(true);
      if (isFirstTime) {
        // Primeira vez - definir senha
        await adminService.setAdminPassword(password);

        // Criar sessão de admin
        sessionStorage.setItem('admin_session', 'active');
        sessionStorage.setItem('admin_session_time', Date.now().toString());

        // Fechar modal e redirecionar
        handleClose();

        // Usar window.location para garantir navegação em produção
        window.location.href = '/admin';
      } else {
        // Verificar senha existente
        const isValid = await adminService.verifyAdminPassword(password);
        
        if (isValid) {
          // Criar sessão de admin
          sessionStorage.setItem('admin_session', 'active');
          sessionStorage.setItem('admin_session_time', Date.now().toString());

          // Fechar modal e redirecionar
          handleClose();

          // Usar window.location para garantir navegação em produção
          window.location.href = '/admin';
        } else {
          setError('Senha incorreta');
        }
      }
    } catch (error) {
      console.error('Erro ao processar senha de admin:', error);
      setError('Erro interno. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setPassword('');
    setConfirmPassword('');
    setError(null);
    setShowPassword(false);
    setShowConfirmPassword(false);
    onClose();
  };

  if (!mounted || !isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-[99999] flex items-center justify-center">
      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black/70 backdrop-blur-sm"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="relative z-[100000] w-full max-w-md mx-4">
        <div className="backdrop-blur-sm bg-slate-900/90 rounded-2xl border border-purple-500/20 shadow-2xl shadow-purple-500/10 p-8 overflow-hidden">
          {/* Glow effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-xl blur-xl"></div>
          
          <div className="relative z-10">
            {/* Header */}
            <div className="text-center mb-6">
              <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center mb-4">
                <Shield className="w-8 h-8 text-purple-400" />
              </div>
              <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-300 to-indigo-300 mb-2">
                {isFirstTime ? 'Configurar Admin' : 'Acesso Admin'}
              </h2>
              <p className="text-sm text-purple-300/70">
                {isFirstTime 
                  ? 'Defina uma senha para acessar a área administrativa'
                  : 'Digite a senha de administrador para continuar'
                }
              </p>
            </div>

            {/* Error */}
            {error && (
              <div className="mb-4 p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-300 text-sm">
                {error}
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Password field */}
              <div className="space-y-1">
                <label htmlFor="admin-password" className="block text-xs font-medium text-indigo-300/80">
                  {isFirstTime ? 'Nova senha de admin' : 'Senha de admin'}
                </label>
                <div className="relative">
                  <input
                    id="admin-password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="block w-full px-4 py-3 pl-10 pr-10 bg-slate-800/60 text-indigo-100 rounded-lg border border-indigo-500/20 focus:outline-none focus:ring-2 focus:ring-purple-500/40 focus:border-transparent transition-all placeholder:text-indigo-300/30"
                    placeholder={isFirstTime ? 'Crie uma senha (mín. 6 caracteres)' : 'Digite a senha'}
                    required
                  />
                  <Lock className="absolute left-3 top-3.5 h-4 w-4 text-indigo-400/60" />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3.5 text-indigo-400/60 hover:text-indigo-300 transition-colors"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              {/* Confirm password field (only for first time) */}
              {isFirstTime && (
                <div className="space-y-1">
                  <label htmlFor="confirm-password" className="block text-xs font-medium text-indigo-300/80">
                    Confirmar senha
                  </label>
                  <div className="relative">
                    <input
                      id="confirm-password"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="block w-full px-4 py-3 pl-10 pr-10 bg-slate-800/60 text-indigo-100 rounded-lg border border-indigo-500/20 focus:outline-none focus:ring-2 focus:ring-purple-500/40 focus:border-transparent transition-all placeholder:text-indigo-300/30"
                      placeholder="Digite a senha novamente"
                      required
                    />
                    <Lock className="absolute left-3 top-3.5 h-4 w-4 text-indigo-400/60" />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-3.5 text-indigo-400/60 hover:text-indigo-300 transition-colors"
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>
              )}

              {/* Buttons */}
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={handleClose}
                  className="flex-1 px-4 py-3 text-indigo-300 bg-slate-800/60 rounded-lg border border-indigo-500/20 hover:bg-slate-700/60 transition-all"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Processando...</span>
                    </>
                  ) : (
                    <>
                      <Settings className="h-4 w-4" />
                      <span>{isFirstTime ? 'Configurar' : 'Acessar'}</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default AdminAccessModal;
