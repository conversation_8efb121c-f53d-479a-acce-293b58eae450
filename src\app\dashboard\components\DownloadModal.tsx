'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { ChatMessage } from '@/lib/types/chat';

interface DownloadModalProps {
  isOpen: boolean;
  onClose: () => void;
  messages: ChatMessage[];
  chatName: string;
}

type DownloadType = 'all' | 'user' | 'ai';

const DownloadModal = ({ isOpen, onClose, messages, chatName }: DownloadModalProps) => {
  const [selectedType, setSelectedType] = useState<DownloadType>('all');
  const [isGenerating, setIsGenerating] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleDownload = async () => {
    setIsGenerating(true);
    
    try {
      // Filter messages based on selected type
      let filteredMessages = messages;
      if (selectedType === 'user') {
        filteredMessages = messages.filter(msg => msg.role === 'user');
      } else if (selectedType === 'ai') {
        filteredMessages = messages.filter(msg => msg.role === 'assistant');
      }

      // Generate HTML content
      const htmlContent = generateStyledHTML(filteredMessages, chatName, selectedType);
      
      // Create and download file
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${chatName}_${selectedType}_messages.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      onClose();
    } catch (error) {
      console.error('Error generating download:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const generateStyledHTML = (messages: ChatMessage[], chatName: string, type: DownloadType): string => {
    const formatTime = (timestamp: number) => {
      return new Date(timestamp).toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    const getTypeTitle = () => {
      switch (type) {
        case 'user': return 'Mensagens do Usuário';
        case 'ai': return 'Mensagens da IA';
        default: return 'Todas as Mensagens';
      }
    };

    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const renderAttachments = (attachments: any[]): string => {
      if (!attachments || attachments.length === 0) return '';

      return `
        <div class="attachments-container">
          ${attachments.map(attachment => `
            <div class="attachment-item ${attachment.type}">
              <div class="attachment-icon">
                ${attachment.type === 'image'
                  ? `<svg class="attachment-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                       <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                       <circle cx="8.5" cy="8.5" r="1.5"/>
                       <polyline points="21,15 16,10 5,21"/>
                     </svg>`
                  : `<svg class="attachment-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                       <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                     </svg>`
                }
              </div>
              <div class="attachment-info">
                <div class="attachment-name">${attachment.filename}</div>
                <div class="attachment-meta">
                  ${attachment.type === 'image' ? 'Imagem' : 'PDF'} • ${formatFileSize(attachment.size)}
                </div>
              </div>
              <div class="attachment-badge">
                ${attachment.type === 'image' ? '🖼️' : '📄'}
              </div>
            </div>
          `).join('')}
        </div>
      `;
    };

    // Função para pré-processar conteúdo LaTeX - EXATAMENTE igual ao MessageBubble
    const preprocessLatex = (content: string): string => {
      // Remove comandos de documento LaTeX não suportados pelo KaTeX
      const unsupportedCommands = [
        /\\documentclass\{[^}]*\}/g,
        /\\usepackage\{[^}]*\}/g,
        /\\begin\{document\}/g,
        /\\end\{document\}/g,
        /\\maketitle/g,
        /\\tableofcontents/g,
        /\\newpage/g,
        /\\clearpage/g,
        /\\pagebreak/g,
      ];

      let processedContent = content;

      // Remove comandos não suportados
      unsupportedCommands.forEach(regex => {
        processedContent = processedContent.replace(regex, '');
      });

      // Converte comandos de seção para markdown
      processedContent = processedContent
        .replace(/\\section\*?\{([^}]+)\}/g, '## $1')
        .replace(/\\subsection\*?\{([^}]+)\}/g, '### $1')
        .replace(/\\subsubsection\*?\{([^}]+)\}/g, '#### $1')
        .replace(/\\paragraph\{([^}]+)\}/g, '##### $1')
        .replace(/\\subparagraph\{([^}]+)\}/g, '###### $1');

      // Converte comandos de formatação básica
      processedContent = processedContent
        .replace(/\\textbf\{([^}]+)\}/g, '**$1**')
        .replace(/\\textit\{([^}]+)\}/g, '*$1*')
        .replace(/\\emph\{([^}]+)\}/g, '*$1*')
        .replace(/\\underline\{([^}]+)\}/g, '<u>$1</u>')
        .replace(/\\texttt\{([^}]+)\}/g, '`$1`');

      // Converte listas LaTeX para markdown
      processedContent = processedContent
        .replace(/\\begin\{itemize\}/g, '')
        .replace(/\\end\{itemize\}/g, '')
        .replace(/\\begin\{enumerate\}/g, '')
        .replace(/\\end\{enumerate\}/g, '')
        .replace(/\\item\s+/g, '- ');

      // Converte ambientes matemáticos especiais
      const convertMathEnvironments = (text: string) => {
        const environments = [
          { start: '\\begin{theorem}', end: '\\end{theorem}', label: '**Teorema:**' },
          { start: '\\begin{definition}', end: '\\end{definition}', label: '**Definição:**' },
          { start: '\\begin{lemma}', end: '\\end{lemma}', label: '**Lema:**' },
          { start: '\\begin{proof}', end: '\\end{proof}', label: '**Demonstração:**' },
          { start: '\\begin{example}', end: '\\end{example}', label: '**Exemplo:**' },
          { start: '\\begin{remark}', end: '\\end{remark}', label: '**Observação:**' }
        ];

        let result = text;
        environments.forEach(env => {
          const startIndex = result.indexOf(env.start);
          const endIndex = result.indexOf(env.end);
          if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
            const content = result.substring(startIndex + env.start.length, endIndex).trim();
            const replacement = `> ${env.label} ${content}`;
            result = result.substring(0, startIndex) + replacement + result.substring(endIndex + env.end.length);
          }
        });
        return result;
      };

      processedContent = convertMathEnvironments(processedContent);

      // Converte comandos matemáticos específicos
      processedContent = processedContent
        .replace(/\\qed/g, '∎')
        .replace(/\\therefore/g, '∴')
        .replace(/\\because/g, '∵')
        .replace(/\\implies/g, '⟹')
        .replace(/\\iff/g, '⟺');

      // Remove linhas vazias excessivas
      processedContent = processedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

      return processedContent.trim();
    };

    // Função para processar Markdown e converter para HTML
    const processMarkdown = (content: string): string => {
      // Primeiro aplicar preprocessamento LaTeX
      let processed = preprocessLatex(content);

      // Preservar expressões LaTeX para evitar interferência do markdown
      const mathExpressions: string[] = [];

      // Preservar expressões display math ($$...$$)
      processed = processed.replace(/\$\$([^]*?)\$\$/g, (match, equation) => {
        const placeholder = `__MATH_DISPLAY_${mathExpressions.length}__`;
        mathExpressions.push(`$$${equation}$$`);
        return placeholder;
      });

      // Preservar expressões inline math ($...$) - mais cuidadoso para evitar falsos positivos
      processed = processed.replace(/\$([^$\n]+?)\$/g, (match, equation) => {
        const placeholder = `__MATH_INLINE_${mathExpressions.length}__`;
        mathExpressions.push(`$${equation}$`);
        return placeholder;
      });

      // Processar código em bloco primeiro (para evitar conflitos)
      processed = processed.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
        return `<pre><code class="language-${lang || 'text'}">${escapeHtml(code.trim())}</code></pre>`;
      });

      // Processar código inline (evitar conflito com $)
      processed = processed.replace(/`([^`]+)`/g, '<code>$1</code>');

      // Processar cabeçalhos e seções numeradas
      processed = processed.replace(/^### (.+)$/gm, '<h3>$1</h3>');
      processed = processed.replace(/^## (.+)$/gm, '<h2>$1</h2>');
      processed = processed.replace(/^# (.+)$/gm, '<h1>$1</h1>');

      // Detectar seções numeradas (ex: "4. Igualando a zero e ligação com as raízes")
      processed = processed.replace(/^(\d+)\.\s+(.+)$/gm, '<div class="numbered-section"><h3>$1. $2</h3></div>');

      // Processar negrito e itálico (ordem importante)
      processed = processed.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>');
      processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      processed = processed.replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '<em>$1</em>');

      // Processar links
      processed = processed.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

      // Processar listas numeradas
      processed = processed.replace(/^\d+\. (.+)$/gm, '<li>$1</li>');

      // Processar listas com marcadores
      processed = processed.replace(/^[-*+] (.+)$/gm, '<li>$1</li>');

      // Agrupar itens de lista consecutivos
      processed = processed.replace(/(<li>.*?<\/li>)(\s*<li>.*?<\/li>)*/g, (match) => {
        return `<ul>${match}</ul>`;
      });

      // Processar citações
      processed = processed.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>');

      // Processar linha horizontal
      processed = processed.replace(/^---$/gm, '<hr>');

      // Processar quebras de linha (manter parágrafos)
      processed = processed.replace(/\n\n/g, '</p><p>');
      processed = processed.replace(/\n/g, '<br>');

      // Envolver em parágrafo se não começar com tag HTML
      if (!processed.startsWith('<')) {
        processed = `<p>${processed}</p>`;
      }

      // Restaurar expressões LaTeX com tratamento especial para display math
      mathExpressions.forEach((expr, index) => {
        if (expr.startsWith('$$')) {
          // Para equações display, envolver em div centralizada
          processed = processed.replace(`__MATH_DISPLAY_${index}__`, `<div class="math-display-wrapper">${expr}</div>`);
        } else {
          processed = processed.replace(`__MATH_INLINE_${index}__`, expr);
        }
      });

      return processed;
    };

    const escapeHtml = (text: string): string => {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    };

    return `<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${chatName} - ${getTypeTitle()}</title>

    <!-- KaTeX CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">

    <!-- Highlight.js CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #0f172a 50%, #1e293b 75%, #0f172a 100%);
            background-attachment: fixed;
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            color: #e2e8f0;
            min-height: 100vh;
            padding: 2rem;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(ellipse at top, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                radial-gradient(ellipse at bottom right, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
                radial-gradient(ellipse at center, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(59, 130, 246, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 0 60px rgba(59, 130, 246, 0.15);
            overflow: hidden;
            position: relative;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
        }

        .header {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(168, 85, 247, 0.15) 100%);
            padding: 4rem 2rem;
            text-align: center;
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center top, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
            pointer-events: none;
        }

        .header > * {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-family: 'Poppins', sans-serif;
            font-size: 3.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 30%, #a855f7 70%, #06b6d4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
            letter-spacing: -0.02em;
            position: relative;
        }

        .header h1::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120%;
            height: 120%;
            background: radial-gradient(ellipse, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
            z-index: -1;
            border-radius: 50%;
        }

        .header p {
            color: #cbd5e1;
            font-size: 1.4rem;
            font-weight: 500;
            opacity: 0.9;
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }
        
        .stat {
            background: rgba(59, 130, 246, 0.1);
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .stat:hover {
            background: rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #3b82f6, #a855f7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #94a3b8;
        }
        
        .messages {
            padding: 2rem;
        }
        
        .message {
            margin-bottom: 3rem;
            display: flex;
            align-items: flex-start;
            gap: 1.5rem;
            position: relative;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message::before {
            content: '';
            position: absolute;
            left: 24px;
            top: 60px;
            bottom: -24px;
            width: 2px;
            background: linear-gradient(180deg, rgba(139, 92, 246, 0.3) 0%, transparent 100%);
            border-radius: 1px;
        }

        .message.user::before {
            left: auto;
            right: 24px;
            background: linear-gradient(180deg, rgba(16, 185, 129, 0.3) 0%, transparent 100%);
        }

        .message:last-child::before {
            display: none;
        }

        .avatar {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow:
                0 8px 25px -8px rgba(0, 0, 0, 0.4),
                0 0 0 3px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }

        .avatar.user {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            box-shadow:
                0 8px 25px -8px rgba(5, 150, 105, 0.4),
                0 0 0 3px rgba(5, 150, 105, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .avatar.ai {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow:
                0 8px 25px -8px rgba(59, 130, 246, 0.4),
                0 0 0 3px rgba(59, 130, 246, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        .message-content {
            flex: 1;
            max-width: 75%;
        }
        
        .message-bubble {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(148, 163, 184, 0.15);
            border-radius: 24px;
            padding: 2rem;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            backdrop-filter: blur(20px);
        }

        .message-bubble::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 24px;
            padding: 1px;
            background: linear-gradient(135deg, rgba(148, 163, 184, 0.2), rgba(148, 163, 184, 0.05));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
        }

        .message.user .message-bubble {
            background: rgba(30, 41, 59, 0.9);
            box-shadow:
                0 8px 32px rgba(16, 185, 129, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .message.user .message-bubble::before {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(16, 185, 129, 0.1));
        }

        .message.ai .message-bubble {
            background: rgba(30, 41, 59, 0.8);
            box-shadow:
                0 8px 32px rgba(139, 92, 246, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .message.ai .message-bubble::before {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(139, 92, 246, 0.1));
        }
        
        .message-text {
            line-height: 1.7;
            white-space: pre-wrap;
            word-wrap: break-word;
            position: relative;
            z-index: 1;
            font-size: 1rem;
            letter-spacing: 0.01em;
        }
        
        .message-time {
            font-size: 0.75rem;
            color: #64748b;
            margin-top: 0.5rem;
            text-align: right;
        }
        
        .message.user .message-time {
            text-align: left;
            color: #10b981;
        }
        
        .footer {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
            padding: 2rem;
            text-align: center;
            border-top: 1px solid rgba(59, 130, 246, 0.2);
            color: #94a3b8;
            font-size: 0.875rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), rgba(168, 85, 247, 0.5), transparent);
        }

        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .footer > * {
            position: relative;
            z-index: 1;
            font-weight: 500;
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
        }
        
        .sparkle {
            color: #fbbf24;
        }

        /* Code styling */
        code {
            background: rgba(15, 23, 42, 0.9);
            color: #e2e8f0;
            padding: 0.375rem 0.75rem;
            border-radius: 8px;
            font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
            font-size: 0.875rem;
            border: 1px solid rgba(148, 163, 184, 0.2);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }

        pre {
            background: rgba(15, 23, 42, 0.95);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            overflow-x: auto;
            position: relative;
            box-shadow:
                0 4px 16px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.5), transparent);
        }

        pre code {
            background: none;
            border: none;
            padding: 0;
            color: inherit;
            box-shadow: none;
        }

        /* Links */
        a {
            color: #60a5fa;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.2s ease;
        }

        a:hover {
            color: #93c5fd;
            border-bottom-color: #60a5fa;
        }

        /* Lists - com estilo igual ao site */
        ul {
            margin: 1rem 0;
            padding-left: 0;
            list-style: none;
        }

        ol {
            margin: 1rem 0;
            padding-left: 0;
            list-style: none;
            counter-reset: list-counter;
        }

        li {
            margin: 0.5rem 0;
            color: #e2e8f0;
            position: relative;
            padding-left: 2rem;
            line-height: 1.6;
        }

        /* Bullet points para ul */
        ul li::before {
            content: '•';
            color: #8b5cf6;
            font-weight: bold;
            position: absolute;
            left: 0.5rem;
        }

        /* Números para ol */
        ol li {
            counter-increment: list-counter;
        }

        ol li::before {
            content: counter(list-counter) '.';
            color: #8b5cf6;
            font-weight: bold;
            position: absolute;
            left: 0;
            min-width: 1.5rem;
        }

        /* Barra lateral para listas aninhadas ou especiais */
        .numbered-section {
            position: relative;
            padding-left: 1.5rem;
            margin: 1rem 0;
        }

        .numbered-section::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(180deg, #8b5cf6 0%, #3b82f6 100%);
            border-radius: 2px;
        }

        /* Typography - igual ao site */
        h1, h2, h3, h4, h5, h6 {
            color: #e2e8f0;
            margin: 1rem 0 0.5rem 0;
            font-weight: 600;
            line-height: 1.4;
        }

        h1 {
            font-size: 1.5rem;
            border-bottom: 2px solid rgba(148, 163, 184, 0.3);
            padding-bottom: 0.5rem;
        }

        h2 {
            font-size: 1.25rem;
            border-left: 4px solid #6366f1;
            padding-left: 0.75rem;
        }

        h3 {
            font-size: 1.125rem;
            border-left: 2px solid #8b5cf6;
            padding-left: 0.5rem;
        }

        h4, h5, h6 {
            font-size: 1rem;
            color: #cbd5e1;
        }

        /* Seções numeradas especiais */
        .numbered-section h3 {
            border-left: none;
            padding-left: 0;
            color: #e2e8f0;
            font-weight: 600;
        }

        p {
            margin: 0.5rem 0;
            line-height: 1.6;
            color: #e2e8f0;
            text-align: left;
        }

        /* Garantir que equações display sempre sejam centralizadas */
        * .katex-display {
            text-align: center !important;
            margin: 1rem auto !important;
            display: block !important;
        }

        strong {
            color: #f1f5f9;
            font-weight: 600;
        }

        em {
            color: #cbd5e1;
            font-style: italic;
        }

        blockquote {
            border-left: 4px solid #8b5cf6;
            padding: 1.5rem;
            margin: 1.5rem 0;
            color: #e2e8f0;
            background: rgba(139, 92, 246, 0.15);
            border-radius: 0 16px 16px 0;
            position: relative;
            box-shadow:
                0 4px 16px rgba(139, 92, 246, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }

        blockquote::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 10px;
            font-size: 3rem;
            color: rgba(139, 92, 246, 0.3);
            font-family: Georgia, serif;
            line-height: 1;
        }

        hr {
            border: none;
            height: 1px;
            background: linear-gradient(90deg, transparent, #64748b, transparent);
            margin: 1.5rem 0;
        }

        /* Math styling - Forçar centralização */
        .katex {
            font-size: 1.1em;
            color: #e2e8f0 !important;
        }

        .katex-display {
            margin: 1rem 0 !important;
            text-align: center !important;
            padding: 0 !important;
            background: none !important;
            border: none !important;
            box-shadow: none !important;
            position: relative;
            display: block !important;
            width: 100% !important;
        }

        .katex-display > .katex {
            font-size: 1.1em;
            color: #e2e8f0 !important;
            display: inline-block !important;
            text-align: center !important;
        }

        .katex-display > .katex > .katex-html {
            display: inline-block !important;
        }

        /* Inline math */
        .katex-inline {
            color: #e2e8f0 !important;
        }

        /* Math environments styling */
        .math-environment {
            margin: 1rem 0;
            text-align: center;
        }

        /* Wrapper especial para equações display */
        .math-display-wrapper {
            text-align: center !important;
            margin: 1.5rem 0 !important;
            display: block !important;
            width: 100% !important;
            clear: both;
        }

        .math-display-wrapper .katex-display {
            text-align: center !important;
            margin: 0 auto !important;
            display: block !important;
        }

        /* Forçar centralização de todas as equações display - máxima prioridade */
        .message-text .katex-display,
        .katex-display,
        div.katex-display,
        span.katex-display {
            text-align: center !important;
            margin-left: auto !important;
            margin-right: auto !important;
            display: block !important;
            width: 100% !important;
        }

        /* Centralizar o conteúdo interno do KaTeX */
        .katex-display .katex-html {
            text-align: center !important;
        }

        /* Override qualquer estilo que possa estar interferindo */
        body .katex-display {
            text-align: center !important;
        }

        /* Attachments styling */
        .attachments-container {
            margin: 1rem 0;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .attachment-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .attachment-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .attachment-item:hover::before {
            opacity: 1;
        }

        .attachment-item.image {
            border-color: rgba(34, 197, 94, 0.3);
            background: rgba(34, 197, 94, 0.1);
        }

        .attachment-item.pdf {
            border-color: rgba(239, 68, 68, 0.3);
            background: rgba(239, 68, 68, 0.1);
        }

        .attachment-icon {
            flex-shrink: 0;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            position: relative;
            z-index: 1;
        }

        .attachment-item.image .attachment-icon {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
        }

        .attachment-item.pdf .attachment-icon {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .attachment-svg {
            width: 24px;
            height: 24px;
            stroke-width: 2;
        }

        .attachment-info {
            flex: 1;
            min-width: 0;
            position: relative;
            z-index: 1;
        }

        .attachment-name {
            font-weight: 600;
            color: #e2e8f0;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
            word-break: break-word;
        }

        .attachment-meta {
            font-size: 0.8rem;
            color: #94a3b8;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .attachment-badge {
            flex-shrink: 0;
            font-size: 1.5rem;
            opacity: 0.7;
            position: relative;
            z-index: 1;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message {
            animation: fadeInUp 0.6s ease-out;
        }

        .message:nth-child(even) {
            animation-delay: 0.1s;
        }

        .message:nth-child(odd) {
            animation-delay: 0.2s;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(139, 92, 246, 0.5);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(139, 92, 246, 0.7);
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .header {
                padding: 2rem 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .stats {
                gap: 1rem;
                flex-direction: column;
            }

            .messages {
                padding: 1rem;
            }

            .message-content {
                max-width: 85%;
            }

            .message-bubble {
                padding: 1.5rem;
            }

            .avatar {
                width: 48px;
                height: 48px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${chatName}</h1>
            <p>${getTypeTitle()}</p>
            <div class="stats">
                <div class="stat">
                    <div class="stat-number">${messages.length}</div>
                    <div class="stat-label">Mensagens</div>
                </div>
                <div class="stat">
                    <div class="stat-number">${new Date().toLocaleDateString('pt-BR')}</div>
                    <div class="stat-label">Exportado em</div>
                </div>
            </div>
        </div>
        
        <div class="messages">
            ${messages.map(message => `
                <div class="message ${message.role === 'user' ? 'user' : 'ai'}">
                    <div class="avatar ${message.role === 'user' ? 'user' : 'ai'}">
                        ${message.role === 'user'
                          ? '<svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/></svg>'
                          : '<span class="sparkle">✨</span>'
                        }
                    </div>
                    <div class="message-content">
                        <div class="message-bubble">
                            ${message.attachments && message.attachments.length > 0 ? renderAttachments(message.attachments) : ''}
                            <div class="message-text">${processMarkdown(message.content)}</div>
                            <div class="message-time">${formatTime(message.timestamp)}</div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
        
        <div class="footer">
            Exportado do Rafthoria • ${new Date().toLocaleString('pt-BR')}
        </div>
    </div>

    <!-- KaTeX JavaScript -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js" integrity="sha384-cpW21h6RZv/phavutF+AuVYrr+dA8xD9zs6FwLpaCct6O9ctzYFfFr4dgmgccOTx" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>

    <!-- Highlight.js JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize syntax highlighting
            hljs.highlightAll();

            // Wait a bit for DOM to be fully ready, then initialize KaTeX
            setTimeout(function() {
                // Initialize KaTeX rendering - configuração exata do site
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\\\[', right: '\\\\]', display: true},
                        {left: '\\\\(', right: '\\\\)', display: false}
                    ],
                    throwOnError: false,
                    errorColor: '#ef4444',
                    strict: 'warn',
                    trust: false,
                    ignoredTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code'],
                    ignoredClasses: ['no-math'],
                    macros: {
                        "\\\\f": "#1f(#2)",
                        "\\\\RR": "\\\\mathbb{R}",
                        "\\\\NN": "\\\\mathbb{N}",
                        "\\\\ZZ": "\\\\mathbb{Z}",
                        "\\\\QQ": "\\\\mathbb{Q}",
                        "\\\\CC": "\\\\mathbb{C}",
                        "\\\\implies": "\\\\Rightarrow",
                        "\\\\iff": "\\\\Leftrightarrow"
                    },
                    fleqn: false,
                    leqno: false,
                    minRuleThickness: 0.04
                });

                // Garantir que equações display estejam centralizadas - forçar estilos
                const displayMath = document.querySelectorAll('.katex-display');
                displayMath.forEach(function(element) {
                    element.style.textAlign = 'center';
                    element.style.margin = '0';
                    element.style.display = 'block';
                    element.style.width = '100%';

                    // Também aplicar aos elementos internos do KaTeX
                    const katexElements = element.querySelectorAll('.katex');
                    katexElements.forEach(function(katex) {
                        katex.style.display = 'inline-block';
                        katex.style.textAlign = 'center';
                    });
                });

                // Aplicar estilos aos wrappers de matemática
                const mathWrappers = document.querySelectorAll('.math-display-wrapper');
                mathWrappers.forEach(function(wrapper) {
                    wrapper.style.textAlign = 'center';
                    wrapper.style.margin = '1.5rem 0';
                    wrapper.style.display = 'block';
                    wrapper.style.width = '100%';
                    wrapper.style.clear = 'both';
                });

                // Aplicar centralização a qualquer nova equação que possa aparecer
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            const newDisplayMath = document.querySelectorAll('.katex-display');
                            newDisplayMath.forEach(function(element) {
                                element.style.textAlign = 'center';
                                element.style.margin = '0';
                                element.style.display = 'block';
                                element.style.width = '100%';
                            });

                            const newMathWrappers = document.querySelectorAll('.math-display-wrapper');
                            newMathWrappers.forEach(function(wrapper) {
                                wrapper.style.textAlign = 'center';
                                wrapper.style.margin = '1.5rem 0';
                                wrapper.style.display = 'block';
                                wrapper.style.width = '100%';
                                wrapper.style.clear = 'both';
                            });
                        }
                    });
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                console.log('KaTeX rendering completed');
            }, 200);
        });
    </script>
</body>
</html>`;
  };

  if (!isOpen || !mounted) return null;

  const modalContent = (
    <div
      className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center transition-opacity duration-300"
      style={{
        zIndex: 99999,
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
      }}
    >
      <div
        className="border border-blue-600/30 rounded-2xl shadow-2xl p-0 w-full max-w-lg transform transition-all duration-300 relative"
        style={{
          background: 'linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(29, 78, 216, 0.95) 50%, rgba(30, 58, 138, 0.95) 100%)',
          backdropFilter: 'blur(20px)',
          position: 'relative',
          zIndex: 100000
        }}
      >
        {/* Efeito de brilho sutil */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-cyan-500/10 pointer-events-none rounded-2xl"></div>
        {/* Modal Header */}
        <div className="flex justify-between items-center p-6 border-b border-blue-600/30 relative z-10">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-blue-100">Download do Chat</h2>
              <p className="text-sm text-blue-300/70">Exporte suas conversas em HTML</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-blue-300 hover:text-blue-100 transition-all duration-200 p-2 rounded-xl hover:bg-blue-800/40 hover:scale-105"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Modal Content */}
        <div className="p-6 relative z-10">
          <div className="mb-6">
            <p className="text-blue-200 font-medium mb-2">
              Escolha quais mensagens incluir no arquivo HTML:
            </p>
            <p className="text-blue-300/70 text-sm">
              O arquivo será exportado com formatação completa, incluindo LaTeX e código
            </p>
          </div>

          <div className="space-y-4 mb-8">
            {/* All Messages Option */}
            <label className={`flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ${
              selectedType === 'all'
                ? 'border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20'
                : 'border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40'
            }`}>
              <div className="flex items-center">
                <input
                  type="radio"
                  name="downloadType"
                  value="all"
                  checked={selectedType === 'all'}
                  onChange={(e) => setSelectedType(e.target.value as DownloadType)}
                  className="w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"
                />
                <div className="ml-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <div className="text-blue-100 font-semibold">Todas as mensagens</div>
                  </div>
                  <div className="text-blue-300/70 text-sm mt-1">Inclui mensagens do usuário e da IA</div>
                </div>
              </div>
            </label>

            {/* User Messages Option */}
            <label className={`flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ${
              selectedType === 'user'
                ? 'border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20'
                : 'border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40'
            }`}>
              <div className="flex items-center">
                <input
                  type="radio"
                  name="downloadType"
                  value="user"
                  checked={selectedType === 'user'}
                  onChange={(e) => setSelectedType(e.target.value as DownloadType)}
                  className="w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"
                />
                <div className="ml-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <div className="text-blue-100 font-semibold">Apenas mensagens do usuário</div>
                  </div>
                  <div className="text-blue-300/70 text-sm mt-1">Somente suas perguntas e comentários</div>
                </div>
              </div>
            </label>

            {/* AI Messages Option */}
            <label className={`flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ${
              selectedType === 'ai'
                ? 'border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20'
                : 'border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40'
            }`}>
              <div className="flex items-center">
                <input
                  type="radio"
                  name="downloadType"
                  value="ai"
                  checked={selectedType === 'ai'}
                  onChange={(e) => setSelectedType(e.target.value as DownloadType)}
                  className="w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"
                />
                <div className="ml-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-cyan-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    <div className="text-blue-100 font-semibold">Apenas mensagens da IA</div>
                  </div>
                  <div className="text-blue-300/70 text-sm mt-1">Somente as respostas da inteligência artificial</div>
                </div>
              </div>
            </label>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              onClick={onClose}
              className="px-6 py-3 text-blue-300 hover:text-blue-100 transition-all duration-200 rounded-xl hover:bg-blue-800/30 font-medium"
            >
              Cancelar
            </button>
            <button
              onClick={handleDownload}
              disabled={isGenerating}
              className="px-8 py-3 bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-semibold shadow-lg hover:shadow-blue-500/30 hover:scale-105 disabled:hover:scale-100"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-3"></div>
                  Gerando arquivo...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Baixar HTML
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default DownloadModal;
