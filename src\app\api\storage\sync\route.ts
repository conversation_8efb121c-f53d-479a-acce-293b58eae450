import { NextRequest, NextResponse } from 'next/server';
import { syncService } from '@/lib/syncService';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, chatId, direction, lastUsedService, lastUsedModel } = body;

    // Validar parâmetros obrigatórios
    if (!userId || !chatId) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, chatId' },
        { status: 400 }
      );
    }

    // Validar direção da sincronização
    if (direction && direction !== 'toFirestore' && direction !== 'fromFirestore') {
      return NextResponse.json(
        { error: 'Invalid direction. Must be "toFirestore" or "fromFirestore"' },
        { status: 400 }
      );
    }

    let result;

    switch (direction) {
      case 'toFirestore':
        await syncService.syncChatToFirestore(userId, chatId);
        result = 'Chat synced to Firestore successfully';
        break;
      
      case 'fromFirestore':
        await syncService.syncChatFromFirestore(userId, chatId);
        result = 'Chat synced from Firestore successfully';
        break;
      
      default:
        // Se não especificado, fazer sincronização bidirecional
        await syncService.syncChatFromFirestore(userId, chatId);
        await syncService.syncChatToFirestore(userId, chatId);
        result = 'Chat synced bidirectionally';
        break;
    }

    // Atualizar metadados se fornecidos
    if (lastUsedService || lastUsedModel) {
      await syncService.updateChatMetadata(userId, chatId, lastUsedService, lastUsedModel);
    }

    return NextResponse.json({ 
      success: true, 
      message: result 
    });

  } catch (error) {
    console.error('Error syncing chat:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const userId = request.nextUrl.searchParams.get('userId');
    const chatId = request.nextUrl.searchParams.get('chatId');

    // Validar parâmetros obrigatórios
    if (!userId || !chatId) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, chatId' },
        { status: 400 }
      );
    }

    // Verificar consistência do chat
    const consistency = await syncService.validateChatConsistency(userId, chatId);

    return NextResponse.json({ 
      success: true, 
      consistency 
    });

  } catch (error) {
    console.error('Error checking chat consistency:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
