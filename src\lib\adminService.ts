import { db, auth } from './firebase';
import {
  doc,
  setDoc,
  getDoc,
  updateDoc,
  collection,
  getDocs,
  deleteDoc,
  query,
  orderBy
} from 'firebase/firestore';

export interface AdminCredentials {
  password: string;
  createdAt: number;
  updatedAt: number;
}

export interface SiteMaintenanceSettings {
  isActive: boolean;
  password: string;
  message?: string;
  createdAt: number;
  updatedAt: number;
}

export interface UserInfo {
  id: string;
  username: string;
  email: string;
  profilePhotoURL?: string;
  createdAt?: number;
  lastLoginAt?: number;
  isActive: boolean;
}

class AdminService {

  // Lista de emails que têm acesso de administrador
  private readonly ADMIN_EMAILS = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>', // Seu email
    // Adicione outros emails de admin aqui
  ];

  /**
   * Verifica se o usuário atual tem permissões de administrador
   */
  async isCurrentUserAdmin(): Promise<boolean> {
    try {
      console.log('🔐 Verificando permissões de admin...');
      const currentUser = auth.currentUser;

      if (!currentUser || !currentUser.email) {
        console.log('🔐 Usuário não logado ou sem email');
        return false;
      }

      console.log('🔐 Email do usuário:', currentUser.email);

      // Verificar se o email está na lista de admins
      const isAdminEmail = this.ADMIN_EMAILS.includes(currentUser.email.toLowerCase());
      console.log('🔐 Email está na lista de admins:', isAdminEmail);

      // Se o email está na lista de admins, permitir acesso
      if (isAdminEmail) {
        console.log('🔐 Acesso liberado - email na lista de admins');
        return true;
      }

      // Se não existe senha de admin ainda, permitir configuração inicial
      const hasAdminPassword = await this.hasAdminPassword();
      console.log('🔐 Tem senha de admin configurada:', hasAdminPassword);

      if (!hasAdminPassword) {
        console.log('🔐 Acesso liberado - primeira configuração');
        return true;
      }

      // Para outros casos, negar acesso (será validado via senha no modal)
      console.log('🔐 Acesso negado - requer validação de senha');
      return false;
    } catch (error) {
      console.error('🔐 Erro ao verificar permissões de admin:', error);
      return false;
    }
  }

  // ==================== CREDENCIAIS DE ADMIN ====================
  
  /**
   * Verifica se existe senha de admin configurada
   */
  async hasAdminPassword(): Promise<boolean> {
    try {
      const adminRef = doc(db, 'admin', 'credenciais');
      const adminDoc = await getDoc(adminRef);
      return adminDoc.exists() && !!adminDoc.data()?.password;
    } catch (error) {
      console.error('Erro ao verificar senha de admin:', error);
      return false;
    }
  }

  /**
   * Define a senha de admin (primeira vez ou atualização)
   */
  async setAdminPassword(password: string): Promise<void> {
    try {
      console.log('🔐 Definindo senha de admin...');
      const adminRef = doc(db, 'admin', 'credenciais');
      const adminDoc = await getDoc(adminRef);

      const now = Date.now();
      const credentials: AdminCredentials = {
        password,
        createdAt: adminDoc.exists() ? adminDoc.data()?.createdAt || now : now,
        updatedAt: now
      };

      console.log('🔐 Salvando credenciais no Firestore...');
      await setDoc(adminRef, credentials);
      console.log('🔐 Senha de admin definida com sucesso');
    } catch (error) {
      console.error('🔐 Erro ao definir senha de admin:', error);
      throw error;
    }
  }

  /**
   * Verifica se a senha de admin está correta
   */
  async verifyAdminPassword(password: string): Promise<boolean> {
    try {
      console.log('🔐 Verificando senha de admin...');
      const adminRef = doc(db, 'admin', 'credenciais');
      const adminDoc = await getDoc(adminRef);

      if (!adminDoc.exists()) {
        console.log('🔐 Documento de credenciais não existe');
        return false;
      }

      const credentials = adminDoc.data() as AdminCredentials;
      const isValid = credentials.password === password;
      console.log('🔐 Senha válida:', isValid);
      return isValid;
    } catch (error) {
      console.error('🔐 Erro ao verificar senha de admin:', error);
      return false;
    }
  }

  // ==================== MANUTENÇÃO DO SITE ====================
  
  /**
   * Obtém as configurações de manutenção do site
   */
  async getMaintenanceSettings(): Promise<SiteMaintenanceSettings | null> {
    try {
      const maintenanceRef = doc(db, 'admin', 'manutencao');
      const maintenanceDoc = await getDoc(maintenanceRef);
      
      if (maintenanceDoc.exists()) {
        return maintenanceDoc.data() as SiteMaintenanceSettings;
      }
      return null;
    } catch (error) {
      console.error('Erro ao obter configurações de manutenção:', error);
      throw error;
    }
  }

  /**
   * Ativa ou desativa o modo de manutenção
   */
  async setMaintenanceMode(isActive: boolean, password: string, message?: string): Promise<void> {
    try {
      const maintenanceRef = doc(db, 'admin', 'manutencao');
      const maintenanceDoc = await getDoc(maintenanceRef);
      
      const now = Date.now();
      const settings: SiteMaintenanceSettings = {
        isActive,
        password,
        message: message || 'Site em manutenção. Tente novamente mais tarde.',
        createdAt: maintenanceDoc.exists() ? maintenanceDoc.data()?.createdAt || now : now,
        updatedAt: now
      };

      await setDoc(maintenanceRef, settings);
      console.log(`Modo de manutenção ${isActive ? 'ativado' : 'desativado'}`);
    } catch (error) {
      console.error('Erro ao configurar modo de manutenção:', error);
      throw error;
    }
  }

  /**
   * Verifica se a senha de manutenção está correta
   */
  async verifyMaintenancePassword(password: string): Promise<boolean> {
    try {
      const settings = await this.getMaintenanceSettings();
      if (!settings) return false;
      
      return settings.password === password;
    } catch (error) {
      console.error('Erro ao verificar senha de manutenção:', error);
      return false;
    }
  }

  // ==================== GERENCIAMENTO DE USUÁRIOS ====================
  
  /**
   * Obtém lista de todos os usuários
   */
  async getAllUsers(): Promise<UserInfo[]> {
    try {
      const usersRef = collection(db, 'usuarios');
      const usersQuery = query(usersRef, orderBy('username'));
      const snapshot = await getDocs(usersQuery);
      
      const users: UserInfo[] = [];
      snapshot.forEach(doc => {
        const data = doc.data();
        users.push({
          id: doc.id,
          username: data.username || 'Usuário sem nome',
          email: data.email || '',
          profilePhotoURL: data.profilePhotoURL,
          createdAt: data.createdAt,
          lastLoginAt: data.lastLoginAt,
          isActive: data.isActive !== false // padrão é ativo
        });
      });

      return users;
    } catch (error) {
      console.error('Erro ao obter lista de usuários:', error);
      throw error;
    }
  }

  /**
   * Ativa ou desativa um usuário
   */
  async toggleUserStatus(userId: string, isActive: boolean): Promise<void> {
    try {
      const userRef = doc(db, 'usuarios', userId);
      await updateDoc(userRef, {
        isActive,
        updatedAt: Date.now()
      });
      
      console.log(`Usuário ${userId} ${isActive ? 'ativado' : 'desativado'}`);
    } catch (error) {
      console.error('Erro ao alterar status do usuário:', error);
      throw error;
    }
  }

  /**
   * Remove um usuário (soft delete)
   */
  async deleteUser(userId: string): Promise<void> {
    try {
      const userRef = doc(db, 'usuarios', userId);
      await updateDoc(userRef, {
        isActive: false,
        deletedAt: Date.now(),
        updatedAt: Date.now()
      });
      
      console.log(`Usuário ${userId} removido`);
    } catch (error) {
      console.error('Erro ao remover usuário:', error);
      throw error;
    }
  }
}

export const adminService = new AdminService();
