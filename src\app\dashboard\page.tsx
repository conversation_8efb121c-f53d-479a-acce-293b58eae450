'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { ConversationProvider, useConversations } from '@/contexts/ConversationContext';
import { ChatFolderProvider, useChatFolders } from '@/contexts/ChatFolderContext';
import { deleteConversation, Conversation } from '@/lib/conversationService';
import { ChatFolder } from '@/lib/types/chatFolder';
import Sidebar from './components/Sidebar';
import MainContent from './components/MainContent';
import NewConversationModal from './components/NewConversationModal';
import ConfirmationModal from './components/ConfirmationModal';
import FolderManagementModal from './components/FolderManagementModal';
import ChatPasswordModal from './components/ChatPasswordModal';
import DeleteChatPasswordModal from './components/DeleteChatPasswordModal';

// Componente interno que tem acesso ao ConversationContext
const DashboardContent = () => {
  const { user, loading } = useAuth();
  const { conversations, removeConversation, refreshConversations, selectConversationWithPassword, selectConversation } = useConversations();
  const {
    folders,
    createFolder,
    updateFolder,
    deleteFolder,
    refreshFolders
  } = useChatFolders();
  const router = useRouter();
  const [modalOpen, setModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingConversationId, setEditingConversationId] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isSidebarVisible, setIsSidebarVisible] = useState(true);
  const [deleteConfirmModal, setDeleteConfirmModal] = useState<{
    isOpen: boolean;
    conversationId: string;
    conversationName: string;
  }>({
    isOpen: false,
    conversationId: '',
    conversationName: ''
  });

  // Estados para o modal de pastas
  const [showFolderModal, setShowFolderModal] = useState(false);
  const [editingFolder, setEditingFolder] = useState<ChatFolder | null>(null);

  // Estados para o modal de senha
  const [passwordModal, setPasswordModal] = useState<{
    isOpen: boolean;
    conversation: Conversation | null;
    error?: string;
  }>({
    isOpen: false,
    conversation: null
  });

  // Estados para o modal de senha de exclusão
  const [deletePasswordModal, setDeletePasswordModal] = useState<{
    isOpen: boolean;
    conversation: Conversation | null;
    error?: string;
  }>({
    isOpen: false,
    conversation: null
  });

  // Estados para o modal de senha das configurações
  const [settingsPasswordModal, setSettingsPasswordModal] = useState<{
    isOpen: boolean;
    conversation: Conversation | null;
    error?: string;
  }>({
    isOpen: false,
    conversation: null
  });


  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth');
    }
    
    // Aplica um pequeno delay para os efeitos de fade-in
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, [user, loading, router]);

  const handleNewConversation = () => {
    setModalOpen(true);
  };

  const handleModalClose = () => {
    setModalOpen(false);
  };

  const handleEditConversation = (conversationId: string) => {
    // Encontrar a conversa para verificar se tem senha
    const conversation = conversations.find(c => c.id === conversationId);

    if (conversation?.password) {
      // Se tem senha, abrir modal de senha para configurações
      setSettingsPasswordModal({
        isOpen: true,
        conversation,
        error: undefined
      });
    } else {
      // Se não tem senha, abrir modal de configurações diretamente
      setEditingConversationId(conversationId);
      setEditModalOpen(true);
    }
  };

  const handleEditModalClose = () => {
    setEditModalOpen(false);
    setEditingConversationId(null);
  };

  const toggleSidebar = () => {
    setIsSidebarVisible(!isSidebarVisible);
  };

  const handleDeleteConversation = (conversationId: string, conversationName: string) => {
    // Encontrar a conversa para verificar se tem senha
    const conversation = conversations.find(c => c.id === conversationId);

    if (conversation?.password) {
      // Se tem senha, abrir modal de senha para exclusão
      setDeletePasswordModal({
        isOpen: true,
        conversation,
        error: undefined
      });
    } else {
      // Se não tem senha, usar o modal de confirmação normal
      setDeleteConfirmModal({
        isOpen: true,
        conversationId,
        conversationName
      });
    }
  };

  const confirmDeleteConversation = async () => {
    if (!user || !deleteConfirmModal.conversationId) return;

    const conversationId = deleteConfirmModal.conversationId;
    console.log(`🗑️ Iniciando exclusão da conversa: ${conversationId}`);

    try {
      // 1. Primeiro deletar do backend (Firestore + Storage)
      console.log(`🔥 Deletando do backend...`);
      await deleteConversation(user.uid, conversationId);

      // 2. Remover do estado local imediatamente para UI responsiva
      console.log(`🔄 Removendo do estado local...`);
      removeConversation(conversationId);

      // 3. Forçar refresh das conversas para garantir sincronização
      console.log(`🔄 Forçando refresh das conversas...`);
      await refreshConversations();

      // 4. Também atualizar as pastas se necessário
      await refreshFolders();

      console.log(`✅ Conversa ${conversationId} deletada e UI atualizada completamente`);
    } catch (error) {
      console.error('❌ Erro ao deletar conversa:', error);
      alert('Erro ao deletar conversa. Tente novamente.');

      // Em caso de erro, forçar refresh para garantir consistência
      console.log(`🔄 Erro detectado, forçando refresh para consistência...`);
      await refreshConversations();
      await refreshFolders();
    } finally {
      setDeleteConfirmModal({
        isOpen: false,
        conversationId: '',
        conversationName: ''
      });
    }
  };

  // Funções para gerenciar pastas
  const handleCreateFolder = () => {
    setEditingFolder(null);
    setShowFolderModal(true);
  };

  const handleEditFolder = (folderId: string) => {
    const folder = folders.find(f => f.id === folderId);
    if (folder) {
      setEditingFolder(folder);
      setShowFolderModal(true);
    }
  };

  const handleDeleteFolder = async (folderId: string) => {
    try {
      await deleteFolder(folderId);
      await refreshConversations();
    } catch (error) {
      console.error('Error deleting folder:', error);
    }
  };

  const handleSaveFolder = async (folderData: any) => {
    try {
      if (editingFolder) {
        await updateFolder(editingFolder.id, folderData);
      } else {
        await createFolder(folderData);
      }
      await refreshFolders();
      setShowFolderModal(false);
      setEditingFolder(null);
    } catch (error) {
      console.error('Error saving folder:', error);
    }
  };

  // Funções para lidar com o modal de senha
  const handlePasswordRequired = (conversation: Conversation) => {
    setPasswordModal({
      isOpen: true,
      conversation,
      error: undefined
    });
  };

  const handleGoToTemporaryChat = () => {
    // Limpar seleção de conversa para mostrar o chat temporário
    selectConversation(null);
    // Fechar sidebar no mobile
    if (window.innerWidth < 640) {
      setIsSidebarVisible(false);
    }
  };

  const handlePasswordSubmit = async (password: string) => {
    if (!passwordModal.conversation) return;

    const success = await selectConversationWithPassword(passwordModal.conversation, password);
    if (success) {
      // Senha correta - fechar modal
      setPasswordModal({
        isOpen: false,
        conversation: null
      });
    } else {
      // Senha incorreta - mostrar erro
      setPasswordModal(prev => ({
        ...prev,
        error: 'Senha incorreta. Tente novamente.'
      }));
    }
  };

  const handlePasswordCancel = () => {
    setPasswordModal({
      isOpen: false,
      conversation: null
    });
  };

  // Funções para lidar com o modal de senha de exclusão
  const handleDeletePasswordSubmit = async (password: string) => {
    if (!deletePasswordModal.conversation) return;

    const conversationId = deletePasswordModal.conversation.id;

    // Verificar se a senha está correta
    if (password === deletePasswordModal.conversation.password) {
      // Senha correta - proceder com a exclusão
      console.log(`🔐 Senha correta, deletando conversa protegida: ${conversationId}`);

      try {
        // 1. Deletar do backend
        await deleteConversation(user!.uid, conversationId);

        // 2. Remover do estado local
        removeConversation(conversationId);

        // 3. Forçar refresh
        await refreshConversations();
        await refreshFolders();

        // Fechar modal
        setDeletePasswordModal({
          isOpen: false,
          conversation: null
        });

        console.log(`✅ Conversa protegida ${conversationId} deletada com sucesso`);
      } catch (error) {
        console.error('❌ Erro ao deletar conversa protegida:', error);
        setDeletePasswordModal(prev => ({
          ...prev,
          error: 'Erro ao deletar conversa. Tente novamente.'
        }));

        // Forçar refresh em caso de erro
        await refreshConversations();
        await refreshFolders();
      }
    } else {
      // Senha incorreta - mostrar erro
      setDeletePasswordModal(prev => ({
        ...prev,
        error: 'Senha incorreta. Tente novamente.'
      }));
    }
  };

  const handleDeletePasswordCancel = () => {
    setDeletePasswordModal({
      isOpen: false,
      conversation: null
    });
  };

  // Funções para lidar com o modal de senha das configurações
  const handleSettingsPasswordSubmit = async (password: string) => {
    if (!settingsPasswordModal.conversation) return;

    // Verificar se a senha está correta
    if (password === settingsPasswordModal.conversation.password) {
      // Senha correta - fechar modal de senha e abrir modal de configurações
      setSettingsPasswordModal({
        isOpen: false,
        conversation: null
      });
      setEditingConversationId(settingsPasswordModal.conversation.id);
      setEditModalOpen(true);
    } else {
      // Senha incorreta - mostrar erro
      setSettingsPasswordModal(prev => ({
        ...prev,
        error: 'Senha incorreta. Tente novamente.'
      }));
    }
  };

  const handleSettingsPasswordCancel = () => {
    setSettingsPasswordModal({
      isOpen: false,
      conversation: null
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
          <p className="mt-4 text-indigo-300">Carregando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
      <div
        className={`flex h-screen overflow-hidden bg-gradient-to-br from-gray-900 via-indigo-950/80 to-gray-900 transition-opacity duration-1000 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
      >
      {/* Partículas decorativas */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 left-1/4 w-1 h-1 rounded-full bg-purple-500 opacity-50 animate-pulse"></div>
        <div className="absolute top-1/3 right-1/4 w-2 h-2 rounded-full bg-blue-400 opacity-30 animate-pulse"></div>
        <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 rounded-full bg-indigo-500 opacity-40 animate-pulse"></div>
        <div className="absolute top-2/3 right-1/5 w-1 h-1 rounded-full bg-purple-400 opacity-50 animate-pulse"></div>
        
        {/* Estrela cadente */}
        <div className="absolute top-20 right-40 w-[150px] h-[1px] bg-gradient-to-r from-transparent via-indigo-400 to-transparent transform rotate-[30deg] opacity-0 animate-[meteor_5s_ease-in_infinite_7s]"></div>
        <div className="absolute top-60 left-1/4 w-[100px] h-[1px] bg-gradient-to-r from-transparent via-purple-400 to-transparent transform -rotate-[15deg] opacity-0 animate-[meteor_4s_ease-in_infinite_3s]"></div>
      </div>
      
      {/* Background effet */}
      <div className="fixed inset-0 -z-10">
        {/* Gradiente primário */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_rgba(120,_50,_255,_0.15),_transparent_50%),radial-gradient(ellipse_at_bottom_left,_rgba(59,_130,_246,_0.1),_transparent_70%)]"></div>
        
        {/* Círculo luminoso no canto superior direito */}
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-purple-600/10 rounded-full blur-3xl"></div>
        
        {/* Círculo luminoso no canto inferior esquerdo */}
        <div className="absolute -bottom-20 -left-20 w-72 h-72 bg-blue-600/10 rounded-full blur-3xl"></div>
      </div>
      
      {/* Sidebar */}
      <div className={`fixed top-0 left-0 h-full z-50 transition-all duration-500 ease-in-out ${
        isSidebarVisible ? 'translate-x-0' : '-translate-x-full'
      } ${isLoaded ? 'translate-y-0' : '-translate-y-20'} w-full sm:w-auto`}>
        <Sidebar
          onNewConversationClick={handleNewConversation}
          onEditConversationClick={handleEditConversation}
          onDeleteConversationClick={handleDeleteConversation}
          onCreateFolderClick={handleCreateFolder}
          onEditFolderClick={handleEditFolder}
          onDeleteFolderClick={handleDeleteFolder}
          onPasswordRequired={handlePasswordRequired}
          onGoToTemporaryChat={handleGoToTemporaryChat}
        />
      </div>

      {/* Toggle Buttons */}
      {/* Botão para fechar sidebar - posicionado na borda direita da sidebar */}
      {isSidebarVisible && (
        <div className="fixed left-[280px] sm:left-[320px] top-1/2 transform -translate-y-1/2 z-50 hidden sm:block">
          <button
            onClick={toggleSidebar}
            className="group bg-gradient-to-r from-blue-600/90 to-blue-700/90 hover:from-blue-500/90 hover:to-blue-600/90 text-white p-2 rounded-r-lg rounded-l-none shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-500/30 border-l-0 backdrop-blur-sm"
            title="Fechar sidebar"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 transform group-hover:scale-110 transition-transform duration-200"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        </div>
      )}

      {/* Botão para fechar sidebar no mobile - posicionado no topo direito */}
      {isSidebarVisible && (
        <div className="fixed right-4 top-4 z-50 sm:hidden">
          <button
            onClick={toggleSidebar}
            className="group bg-gradient-to-r from-blue-600/90 to-blue-700/90 hover:from-blue-500/90 hover:to-blue-600/90 text-white p-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-500/30 backdrop-blur-sm"
            title="Fechar sidebar"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 transform group-hover:scale-110 transition-transform duration-200"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      )}

      {/* Botão para abrir sidebar - posicionado quando sidebar está fechada */}
      {!isSidebarVisible && (
        <div className="fixed left-2 sm:left-4 top-4 sm:top-1/2 sm:transform sm:-translate-y-1/2 z-50">
          <button
            onClick={toggleSidebar}
            className="group bg-gradient-to-r from-blue-600/90 to-blue-700/90 hover:from-blue-500/90 hover:to-blue-600/90 text-white p-2 sm:p-3 rounded-lg sm:rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-500/30 backdrop-blur-sm"
            title="Mostrar sidebar"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 sm:h-5 sm:w-5 transform group-hover:scale-110 transition-transform duration-200"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      )}

      {/* Main content area */}
      <div className={`transition-all duration-500 ease-in-out relative z-10 ${
        isSidebarVisible ? 'sm:ml-80 ml-0 sm:w-[calc(100vw-320px)]' : 'ml-0 w-full'
      } ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} h-screen overflow-hidden`}>
        <MainContent onNewConversation={handleNewConversation} />
      </div>
      
        {/* Modals */}
        <NewConversationModal isOpen={modalOpen} onClose={handleModalClose} />
        {editingConversationId && (
          <NewConversationModal
            isOpen={editModalOpen}
            onClose={handleEditModalClose}
            editMode={true}
            conversationId={editingConversationId}
          />
        )}

        {/* Modal de confirmação para deletar conversa */}
        <ConfirmationModal
          isOpen={deleteConfirmModal.isOpen}
          title="Deletar Conversa"
          message={`Tem certeza que deseja deletar "${deleteConfirmModal.conversationName}"? Esta ação não pode ser desfeita.`}
          onConfirm={confirmDeleteConversation}
          onCancel={() => setDeleteConfirmModal({
            isOpen: false,
            conversationId: '',
            conversationName: ''
          })}
          confirmText="Deletar"
          cancelText="Cancelar"
          destructive={true}
        />

        {/* Modal de Gerenciamento de Pastas */}
        <FolderManagementModal
          isOpen={showFolderModal}
          onClose={() => {
            setShowFolderModal(false);
            setEditingFolder(null);
          }}
          onCreateFolder={handleSaveFolder}
          onUpdateFolder={async (folderId, updates) => {
            await updateFolder(folderId, updates);
            await refreshFolders();
          }}
          onDeleteFolder={async (folderId) => {
            await deleteFolder(folderId);
            await refreshConversations();
          }}
          editingFolder={editingFolder}
          folders={folders}
        />

        {/* Modal de Senha do Chat */}
        <ChatPasswordModal
          isOpen={passwordModal.isOpen}
          chatName={passwordModal.conversation?.name || ''}
          onPasswordSubmit={handlePasswordSubmit}
          onCancel={handlePasswordCancel}
          error={passwordModal.error}
        />

        {/* Modal de Senha para Exclusão de Chat */}
        <DeleteChatPasswordModal
          isOpen={deletePasswordModal.isOpen}
          chatName={deletePasswordModal.conversation?.name || ''}
          onPasswordSubmit={handleDeletePasswordSubmit}
          onCancel={handleDeletePasswordCancel}
          error={deletePasswordModal.error}
        />

        {/* Modal de Senha para Configurações de Chat */}
        <ChatPasswordModal
          isOpen={settingsPasswordModal.isOpen}
          chatName={settingsPasswordModal.conversation?.name || ''}
          onPasswordSubmit={handleSettingsPasswordSubmit}
          onCancel={handleSettingsPasswordCancel}
          error={settingsPasswordModal.error}
          purpose="settings"
        />
      </div>
  );
};

// Componente principal - agora os providers estão no layout
export default DashboardContent;