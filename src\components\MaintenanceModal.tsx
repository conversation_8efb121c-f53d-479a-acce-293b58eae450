'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { adminService } from '@/lib/adminService';
import { Lock, Eye, EyeOff, AlertTriangle, Settings } from 'lucide-react';

interface MaintenanceModalProps {
  isOpen: boolean;
  message?: string;
  onSuccess: () => void;
}

const MaintenanceModal = ({ isOpen, message, onSuccess }: MaintenanceModalProps) => {
  const [mounted, setMounted] = useState(false);
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!password.trim()) {
      setError('Por favor, digite a senha');
      return;
    }

    try {
      setIsLoading(true);

      const isValid = await adminService.verifyMaintenancePassword(password);
      if (isValid) {
        onSuccess();
      } else {
        setError('Senha incorreta');
      }
    } catch (error) {
      console.error('Erro ao verificar senha de manutenção:', error);
      setError('Erro interno. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!mounted || !isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-[99999] flex items-center justify-center">
      {/* Overlay - não permite fechar clicando fora */}
      <div className="absolute inset-0 bg-black/80 backdrop-blur-sm" />
      
      {/* Modal */}
      <div className="relative z-[100000] w-full max-w-md mx-4">
        <div className="backdrop-blur-sm bg-slate-900/95 rounded-2xl border border-orange-500/30 shadow-2xl shadow-orange-500/20 p-8 overflow-hidden">
          {/* Glow effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-orange-500/30 to-red-500/30 rounded-xl blur-xl"></div>
          
          <div className="relative z-10">
            {/* Header */}
            <div className="text-center mb-6">
              <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-r from-orange-500/20 to-red-500/20 flex items-center justify-center mb-4">
                <AlertTriangle className="w-8 h-8 text-orange-400" />
              </div>
              <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-300 to-red-300 mb-2">
                Site em Manutenção
              </h2>
              <p className="text-sm text-orange-300/70 mb-4">
                {message || 'O site está temporariamente indisponível para manutenção.'}
              </p>
              <div className="p-3 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                <p className="text-xs text-orange-300/80">
                  Se você possui acesso autorizado, digite a senha para continuar
                </p>
              </div>
            </div>

            {/* Error */}
            {error && (
              <div className="mb-4 p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-300 text-sm">
                {error}
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Password field */}
              <div className="space-y-1">
                <label htmlFor="maintenance-password" className="block text-xs font-medium text-orange-300/80">
                  Senha de Acesso
                </label>
                <div className="relative">
                  <input
                    id="maintenance-password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="block w-full px-4 py-3 pl-10 pr-10 bg-slate-800/60 text-orange-100 rounded-lg border border-orange-500/20 focus:outline-none focus:ring-2 focus:ring-orange-500/40 focus:border-transparent transition-all placeholder:text-orange-300/30"
                    placeholder="Digite a senha de manutenção"
                    required
                    autoFocus
                  />
                  <Lock className="absolute left-3 top-3.5 h-4 w-4 text-orange-400/60" />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3.5 text-orange-400/60 hover:text-orange-300 transition-colors"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              {/* Submit button */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full px-4 py-3 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-lg hover:from-orange-700 hover:to-red-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Verificando...</span>
                  </>
                ) : (
                  <>
                    <Settings className="h-4 w-4" />
                    <span>Acessar Site</span>
                  </>
                )}
              </button>
            </form>

            {/* Footer */}
            <div className="mt-6 pt-4 border-t border-orange-500/10 text-center">
              <p className="text-xs text-orange-400/60">
                Entre em contato com o administrador se precisar de ajuda
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default MaintenanceModal;
