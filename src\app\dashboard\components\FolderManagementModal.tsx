'use client';

import { useState, useEffect } from 'react';
import { ChatFolder, ChatFolderInput, FOLDER_COLORS } from '@/lib/types/chatFolder';

interface FolderManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateFolder: (folderData: ChatFolderInput) => Promise<void>;
  onUpdateFolder?: (folderId: string, updates: Partial<ChatFolder>) => Promise<void>;
  onDeleteFolder?: (folderId: string) => Promise<void>;
  editingFolder?: ChatFolder | null;
  folders: ChatFolder[];
}

export default function FolderManagementModal({
  isOpen,
  onClose,
  onCreateFolder,
  onUpdateFolder,
  onDeleteFolder,
  editingFolder,
  folders
}: FolderManagementModalProps) {
  const [formData, setFormData] = useState<ChatFolderInput>({
    name: '',
    description: '',
    color: FOLDER_COLORS[0],
    isExpanded: true
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Resetar formulário quando o modal abrir/fechar ou quando mudar o folder sendo editado
  useEffect(() => {
    if (isOpen) {
      if (editingFolder) {
        setFormData({
          name: editingFolder.name,
          description: editingFolder.description || '',
          color: editingFolder.color,
          isExpanded: editingFolder.isExpanded
        });
      } else {
        setFormData({
          name: '',
          description: '',
          color: FOLDER_COLORS[0],
          isExpanded: true
        });
      }
    }
    setShowDeleteConfirm(false);
  }, [isOpen, editingFolder]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) return;

    setIsLoading(true);
    try {
      if (editingFolder && onUpdateFolder) {
        await onUpdateFolder(editingFolder.id, formData);
      } else {
        await onCreateFolder(formData);
      }
      onClose();
    } catch (error) {
      console.error('Error saving folder:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!editingFolder || !onDeleteFolder) return;

    setIsLoading(true);
    try {
      await onDeleteFolder(editingFolder.id);
      onClose();
    } catch (error) {
      console.error('Error deleting folder:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-blue-950 to-blue-900 rounded-2xl shadow-2xl border border-blue-700/50 w-full max-w-md">
        {/* Header */}
        <div className="p-6 border-b border-blue-700/30">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-white">
              {editingFolder ? 'Editar Pasta' : 'Nova Pasta'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-blue-800/50 rounded-lg transition-colors"
            >
              <svg className="w-5 h-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Nome da pasta */}
          <div>
            <label className="block text-sm font-medium text-blue-200 mb-2">
              Nome da pasta *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H9L7 5H3a2 2 0 00-2 2z" />
                </svg>
              </div>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full pl-10 pr-4 py-3 bg-blue-900/50 border border-blue-700/50 rounded-lg text-white placeholder-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Digite o nome da pasta"
                required
                maxLength={50}
              />
            </div>
            <p className="text-xs text-blue-400/70 mt-1">
              Nome para identificar a pasta de chats
            </p>
          </div>

          {/* Descrição */}
          <div>
            <label className="block text-sm font-medium text-blue-200 mb-2">
              Descrição
            </label>
            <div className="relative">
              <div className="absolute top-3 left-3 pointer-events-none">
                <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                </svg>
              </div>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full pl-10 pr-4 py-3 bg-blue-900/50 border border-blue-700/50 rounded-lg text-white placeholder-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Descrição opcional da pasta"
                rows={3}
                maxLength={200}
              />
            </div>
            <p className="text-xs text-blue-400/70 mt-1">
              Descrição opcional para a pasta
            </p>
          </div>

          {/* Cor da pasta */}
          <div>
            <label className="block text-sm font-medium text-blue-200 mb-3">
              Cor da pasta
            </label>
            <div className="grid grid-cols-5 gap-3">
              {FOLDER_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, color }))}
                  className={`w-10 h-10 rounded-lg transition-all duration-200 ${
                    formData.color === color
                      ? 'ring-2 ring-white ring-offset-2 ring-offset-blue-900 scale-110'
                      : 'hover:scale-105'
                  }`}
                  style={{ backgroundColor: color }}
                  title={`Cor ${color}`}
                >
                  {formData.color === color && (
                    <svg className="w-6 h-6 text-white mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </button>
              ))}
            </div>
            <p className="text-xs text-blue-400/70 mt-2">
              Escolha uma cor para identificar visualmente a pasta
            </p>
          </div>

          {/* Expandida por padrão */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-blue-200">
                Expandida por padrão
              </label>
              <p className="text-xs text-blue-400/70 mt-1">
                A pasta ficará aberta quando você acessar o chat
              </p>
            </div>
            <button
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, isExpanded: !prev.isExpanded }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                formData.isExpanded ? 'bg-blue-600' : 'bg-blue-800'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  formData.isExpanded ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </form>

        {/* Footer */}
        <div className="p-6 border-t border-blue-700/30 flex items-center justify-between">
          <div>
            {editingFolder && onDeleteFolder && (
              <button
                type="button"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={isLoading}
                className="px-4 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-400 hover:text-red-300 rounded-lg transition-colors disabled:opacity-50"
              >
                Deletar Pasta
              </button>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              type="button"
              onClick={onClose}
              disabled={isLoading}
              className="px-4 py-2 text-blue-300 hover:text-white transition-colors disabled:opacity-50"
            >
              Cancelar
            </button>
            <button
              onClick={handleSubmit}
              disabled={isLoading || !formData.name.trim()}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Salvando...' : editingFolder ? 'Salvar' : 'Criar Pasta'}
            </button>
          </div>
        </div>
      </div>

      {/* Modal de confirmação de exclusão */}
      {showDeleteConfirm && (
        <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
          <div className="bg-gradient-to-br from-red-950 to-red-900 rounded-xl shadow-2xl border border-red-700/50 p-6 max-w-sm mx-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">
                Deletar Pasta
              </h3>
              <p className="text-red-200/80 text-sm mb-6">
                Tem certeza que deseja deletar esta pasta? Os chats serão movidos para "Sem pasta".
              </p>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isLoading}
                  className="flex-1 px-4 py-2 text-red-300 hover:text-white transition-colors disabled:opacity-50"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleDelete}
                  disabled={isLoading}
                  className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  {isLoading ? 'Deletando...' : 'Deletar'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
