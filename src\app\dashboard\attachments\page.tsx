'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useConversations } from '@/contexts/ConversationContext';
import { attachmentService, AttachmentWithChat } from '@/lib/attachmentService';
import AttachmentCard from './components/AttachmentCard';
import AttachmentViewModal from './components/AttachmentViewModal';

type FilterType = 'all' | 'image' | 'pdf';
type SortType = 'date' | 'name' | 'size';

export default function AttachmentsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { conversations } = useConversations();

  const [attachments, setAttachments] = useState<AttachmentWithChat[]>([]);
  const [filteredAttachments, setFilteredAttachments] = useState<AttachmentWithChat[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filtros
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<FilterType>('all');
  const [chatFilter, setChatFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all'); // all, today, week, month, year
  const [sortBy, setSortBy] = useState<SortType>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Modal de visualização
  const [selectedAttachment, setSelectedAttachment] = useState<AttachmentWithChat | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // Carregar anexos
  useEffect(() => {
    if (!user) {
      router.push('/auth');
      return;
    }

    loadAttachments();
  }, [user, router]);

  // Aplicar filtros
  useEffect(() => {
    let filtered = [...attachments];

    // Filtro por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(attachment =>
        attachment.filename.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtro por tipo
    if (typeFilter !== 'all') {
      filtered = filtered.filter(attachment => attachment.type === typeFilter);
    }

    // Filtro por chat
    if (chatFilter !== 'all') {
      filtered = filtered.filter(attachment => attachment.chatId === chatFilter);
    }

    // Filtro por data
    if (dateFilter !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      filtered = filtered.filter(attachment => {
        const attachmentDate = new Date(attachment.uploadedAt);

        switch (dateFilter) {
          case 'today':
            return attachmentDate >= today;
          case 'week':
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            return attachmentDate >= weekAgo;
          case 'month':
            const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            return attachmentDate >= monthAgo;
          case 'year':
            const yearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
            return attachmentDate >= yearAgo;
          default:
            return true;
        }
      });
    }

    // Ordenação
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = a.uploadedAt - b.uploadedAt;
          break;
        case 'name':
          comparison = a.filename.localeCompare(b.filename);
          break;
        case 'size':
          comparison = a.size - b.size;
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredAttachments(filtered);
  }, [attachments, searchTerm, typeFilter, chatFilter, dateFilter, sortBy, sortOrder]);

  const loadAttachments = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const result = await attachmentService.getUserAttachments(user.uid);

      if (result.success && result.attachments) {
        // Os anexos já vêm com o nome do chat salvo nos metadados
        setAttachments(result.attachments);
      } else {
        setError(result.error || 'Erro ao carregar anexos');
      }
    } catch (err) {
      console.error('Error loading attachments:', err);
      setError('Erro ao carregar anexos');
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Ações dos anexos
  const handleViewAttachment = (attachment: AttachmentWithChat) => {
    setSelectedAttachment(attachment);
    setIsViewModalOpen(true);
  };

  const handleDownloadAttachment = (attachment: AttachmentWithChat) => {
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleGoToChat = (chatId: string) => {
    router.push(`/dashboard?chat=${chatId}`);
  };

  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setSelectedAttachment(null);
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setTypeFilter('all');
    setChatFilter('all');
    setDateFilter('all');
    setSortBy('date');
    setSortOrder('desc');
  };

  const getFileIcon = (type: 'image' | 'pdf') => {
    if (type === 'image') {
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95">
      {/* Efeito de brilho sutil */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"></div>

      <div className="relative z-10 p-3 sm:p-6">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
            <button
              onClick={() => router.push('/dashboard')}
              className="p-1.5 sm:p-2 rounded-lg sm:rounded-xl bg-blue-700/50 hover:bg-blue-600/70 transition-all duration-300 text-blue-300 hover:text-white"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div>
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white">Anexos</h1>
              <p className="text-blue-300/70 mt-1 text-sm sm:text-base">Gerencie todos os seus arquivos enviados</p>
            </div>
          </div>

          {/* Filtros */}
          <div className="bg-blue-900/30 border border-blue-700/30 rounded-xl p-4 sm:p-6 backdrop-blur-sm">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3 sm:gap-4">
              {/* Busca */}
              <div>
                <label className="block text-xs sm:text-sm font-medium text-blue-200 mb-1.5 sm:mb-2">Buscar</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Nome do arquivo..."
                  className="w-full px-3 sm:px-4 py-2 bg-blue-800/50 border border-blue-600/30 rounded-lg text-white placeholder-blue-300/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-sm sm:text-base"
                />
              </div>

              {/* Tipo */}
              <div>
                <label className="block text-sm font-medium text-blue-200 mb-2">Tipo</label>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value as FilterType)}
                  className="w-full px-4 py-2 bg-blue-800/50 border border-blue-600/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                >
                  <option value="all">Todos</option>
                  <option value="image">Imagens</option>
                  <option value="pdf">PDFs</option>
                </select>
              </div>

              {/* Chat */}
              <div>
                <label className="block text-sm font-medium text-blue-200 mb-2">Chat</label>
                <select
                  value={chatFilter}
                  onChange={(e) => setChatFilter(e.target.value)}
                  className="w-full px-4 py-2 bg-blue-800/50 border border-blue-600/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                >
                  <option value="all">Todos os chats</option>
                  {conversations.map(conv => (
                    <option key={conv.id} value={conv.id}>{conv.name}</option>
                  ))}
                </select>
              </div>

              {/* Data */}
              <div>
                <label className="block text-sm font-medium text-blue-200 mb-2">Período</label>
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="w-full px-4 py-2 bg-blue-800/50 border border-blue-600/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                >
                  <option value="all">Todos os períodos</option>
                  <option value="today">Hoje</option>
                  <option value="week">Última semana</option>
                  <option value="month">Último mês</option>
                  <option value="year">Último ano</option>
                </select>
              </div>

              {/* Ordenação */}
              <div>
                <label className="block text-sm font-medium text-blue-200 mb-2">Ordenar por</label>
                <div className="flex gap-2">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as SortType)}
                    className="flex-1 px-4 py-2 bg-blue-800/50 border border-blue-600/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                  >
                    <option value="date">Data</option>
                    <option value="name">Nome</option>
                    <option value="size">Tamanho</option>
                  </select>
                  <button
                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                    className="px-3 py-2 bg-blue-700/50 hover:bg-blue-600/70 border border-blue-600/30 rounded-lg text-blue-300 hover:text-white transition-all duration-200"
                  >
                    {sortOrder === 'asc' ? '↑' : '↓'}
                  </button>
                </div>
              </div>
            </div>

            {/* Botão para limpar filtros */}
            {(searchTerm || typeFilter !== 'all' || chatFilter !== 'all' || dateFilter !== 'all' || sortBy !== 'date' || sortOrder !== 'desc') && (
              <div className="mt-4 flex justify-end">
                <button
                  onClick={clearAllFilters}
                  className="px-4 py-2 bg-blue-700/50 hover:bg-blue-600/70 text-blue-300 hover:text-white rounded-lg transition-all duration-200 text-sm flex items-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Limpar filtros
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Conteúdo */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
            <span className="ml-3 text-blue-300">Carregando anexos...</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-400 mb-4">
              <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-lg font-medium">Erro ao carregar anexos</p>
              <p className="text-sm text-red-300/70 mt-1">{error}</p>
            </div>
            <button
              onClick={loadAttachments}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Tentar novamente
            </button>
          </div>
        ) : filteredAttachments.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-blue-300/70 mb-4">
              <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-lg font-medium">Nenhum anexo encontrado</p>
              <p className="text-sm mt-1">
                {attachments.length === 0 
                  ? 'Você ainda não enviou nenhum arquivo.'
                  : 'Nenhum anexo corresponde aos filtros aplicados.'
                }
              </p>
            </div>
          </div>
        ) : (
          <>
            {/* Estatísticas */}
            <div className="mb-4 sm:mb-6 grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
              <div className="bg-blue-900/30 border border-blue-700/30 rounded-xl p-3 sm:p-4 backdrop-blur-sm">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="p-1.5 sm:p-2 bg-blue-600/30 rounded-lg">
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xl sm:text-2xl font-bold text-white">{filteredAttachments.length}</p>
                    <p className="text-xs sm:text-sm text-blue-300/70">Anexos encontrados</p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-900/30 border border-blue-700/30 rounded-xl p-4 backdrop-blur-sm">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-600/30 rounded-lg">
                    <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">
                      {filteredAttachments.filter(a => a.type === 'image').length}
                    </p>
                    <p className="text-sm text-blue-300/70">Imagens</p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-900/30 border border-blue-700/30 rounded-xl p-4 backdrop-blur-sm">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-red-600/30 rounded-lg">
                    <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">
                      {filteredAttachments.filter(a => a.type === 'pdf').length}
                    </p>
                    <p className="text-sm text-blue-300/70">PDFs</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Grid de anexos */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredAttachments.map((attachment) => (
                <AttachmentCard
                  key={attachment.id}
                  attachment={attachment}
                  onView={handleViewAttachment}
                  onDownload={handleDownloadAttachment}
                  onGoToChat={handleGoToChat}
                />
              ))}
            </div>
          </>
        )}
      </div>

      {/* Modal de visualização */}
      <AttachmentViewModal
        attachment={selectedAttachment}
        isOpen={isViewModalOpen}
        onClose={closeViewModal}
        onDownload={handleDownloadAttachment}
        onGoToChat={handleGoToChat}
      />
    </div>
  );
}
