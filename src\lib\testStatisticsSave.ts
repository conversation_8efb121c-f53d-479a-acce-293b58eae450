import { chatStatisticsService } from './chatStatisticsService';
import { ChatStatistics } from './types/chat';

/**
 * Função de teste para verificar se todas as estatísticas estão sendo salvas no Firestore
 */
export async function testStatisticsSave(userId: string, chatId: string): Promise<void> {
  console.log('🧪 Testando salvamento de estatísticas no Firestore...');
  
  try {
    // 1. Inicializar estatísticas
    console.log('📝 Inicializando estatísticas...');
    const initialStats = await chatStatisticsService.initializeChatStatistics(userId, chatId);
    
    // 2. Verificar se foi salvo corretamente
    console.log('🔍 Verificando salvamento inicial...');
    const retrievedStats = await chatStatisticsService.getChatStatistics(userId, chatId);
    
    if (!retrievedStats) {
      console.error('❌ Erro: Estatísticas não foram salvas!');
      return;
    }
    
    // 3. Verificar todas as propriedades das novas estatísticas
    console.log('🔍 Verificando propriedades das novas estatísticas...');
    
    const checks = [
      // Estatísticas de edição
      { name: 'messageEdits', exists: !!retrievedStats.messageEdits },
      { name: 'messageEdits.totalEdits', exists: typeof retrievedStats.messageEdits?.totalEdits === 'number' },
      { name: 'messageEdits.userMessageEdits', exists: typeof retrievedStats.messageEdits?.userMessageEdits === 'number' },
      { name: 'messageEdits.averageEditsPerMessage', exists: typeof retrievedStats.messageEdits?.averageEditsPerMessage === 'number' },
      { name: 'messageEdits.mostEditedMessageLength', exists: typeof retrievedStats.messageEdits?.mostEditedMessageLength === 'number' },
      
      // Estatísticas de regeneração
      { name: 'messageRegenerations', exists: !!retrievedStats.messageRegenerations },
      { name: 'messageRegenerations.totalRegenerations', exists: typeof retrievedStats.messageRegenerations?.totalRegenerations === 'number' },
      { name: 'messageRegenerations.averageRegenerationsPerAssistantMessage', exists: typeof retrievedStats.messageRegenerations?.averageRegenerationsPerAssistantMessage === 'number' },
      { name: 'messageRegenerations.regenerationReasons', exists: Array.isArray(retrievedStats.messageRegenerations?.regenerationReasons) },
      
      // Análise de sentimento
      { name: 'sentimentAnalysis', exists: !!retrievedStats.sentimentAnalysis },
      { name: 'sentimentAnalysis.positive', exists: typeof retrievedStats.sentimentAnalysis?.positive === 'number' },
      { name: 'sentimentAnalysis.neutral', exists: typeof retrievedStats.sentimentAnalysis?.neutral === 'number' },
      { name: 'sentimentAnalysis.negative', exists: typeof retrievedStats.sentimentAnalysis?.negative === 'number' },
      { name: 'sentimentAnalysis.averageSentimentScore', exists: typeof retrievedStats.sentimentAnalysis?.averageSentimentScore === 'number' },
      { name: 'sentimentAnalysis.sentimentTrend', exists: Array.isArray(retrievedStats.sentimentAnalysis?.sentimentTrend) },
      
      // Padrões de conversa
      { name: 'conversationPatterns', exists: !!retrievedStats.conversationPatterns },
      { name: 'conversationPatterns.questionsAsked', exists: typeof retrievedStats.conversationPatterns?.questionsAsked === 'number' },
      { name: 'conversationPatterns.codeBlocksShared', exists: typeof retrievedStats.conversationPatterns?.codeBlocksShared === 'number' },
      { name: 'conversationPatterns.mathExpressionsUsed', exists: typeof retrievedStats.conversationPatterns?.mathExpressionsUsed === 'number' },
      
      // Complexidade das mensagens
      { name: 'messageComplexity', exists: !!retrievedStats.messageComplexity },
      { name: 'messageComplexity.averageWordsPerMessage', exists: typeof retrievedStats.messageComplexity?.averageWordsPerMessage === 'number' },
      { name: 'messageComplexity.averageSentencesPerMessage', exists: typeof retrievedStats.messageComplexity?.averageSentencesPerMessage === 'number' },
      
      // Métricas de leitura
      { name: 'readingMetrics', exists: !!retrievedStats.readingMetrics },
      { name: 'readingMetrics.quickResponses', exists: typeof retrievedStats.readingMetrics?.quickResponses === 'number' },
      { name: 'readingMetrics.thoughtfulResponses', exists: typeof retrievedStats.readingMetrics?.thoughtfulResponses === 'number' },
      { name: 'readingMetrics.averageTimeToRespond', exists: typeof retrievedStats.readingMetrics?.averageTimeToRespond === 'number' },
      
      // Uso de recursos
      { name: 'featureUsage', exists: !!retrievedStats.featureUsage },
      { name: 'featureUsage.attachmentsUsed', exists: !!retrievedStats.featureUsage?.attachmentsUsed },
      { name: 'featureUsage.codeLanguages', exists: Array.isArray(retrievedStats.featureUsage?.codeLanguages) },
      { name: 'featureUsage.latexUsage', exists: typeof retrievedStats.featureUsage?.latexUsage === 'number' },
      
      // Eficiência da conversa
      { name: 'conversationEfficiency', exists: !!retrievedStats.conversationEfficiency },
      { name: 'conversationEfficiency.satisfactionIndicators', exists: typeof retrievedStats.conversationEfficiency?.satisfactionIndicators === 'number' },
      { name: 'conversationEfficiency.clarificationRequests', exists: typeof retrievedStats.conversationEfficiency?.clarificationRequests === 'number' },
    ];
    
    // Verificar cada propriedade
    let allPassed = true;
    checks.forEach(check => {
      if (check.exists) {
        console.log(`✅ ${check.name}: OK`);
      } else {
        console.error(`❌ ${check.name}: FALTANDO`);
        allPassed = false;
      }
    });
    
    if (allPassed) {
      console.log('🎉 Todas as estatísticas estão sendo salvas corretamente!');
    } else {
      console.error('⚠️ Algumas estatísticas não estão sendo salvas!');
    }
    
    // 4. Testar atualização de uma estatística específica
    console.log('🔄 Testando atualização de estatística...');
    await chatStatisticsService.updateStatisticsOnMessageEdit(userId, chatId, 'user', 100, 150);
    
    const updatedStats = await chatStatisticsService.getChatStatistics(userId, chatId);
    if (updatedStats?.messageEdits.totalEdits === 1) {
      console.log('✅ Atualização de estatística funcionando!');
    } else {
      console.error('❌ Atualização de estatística falhou!');
    }
    
    // 5. Mostrar estrutura completa salva
    console.log('📊 Estrutura completa salva no Firestore:');
    console.log(JSON.stringify(retrievedStats, null, 2));
    
  } catch (error) {
    console.error('❌ Erro durante teste:', error);
  }
}

/**
 * Função para verificar se uma estatística específica está sendo salva
 */
export async function checkSpecificStatistic(
  userId: string, 
  chatId: string, 
  propertyPath: string
): Promise<boolean> {
  try {
    const stats = await chatStatisticsService.getChatStatistics(userId, chatId);
    if (!stats) return false;
    
    // Navegar pelo caminho da propriedade (ex: "messageEdits.totalEdits")
    const pathParts = propertyPath.split('.');
    let current: any = stats;
    
    for (const part of pathParts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return false;
      }
    }
    
    return current !== undefined && current !== null;
  } catch (error) {
    console.error('Erro ao verificar estatística:', error);
    return false;
  }
}

/**
 * Função para listar todas as propriedades salvas
 */
export async function listAllSavedProperties(userId: string, chatId: string): Promise<string[]> {
  try {
    const stats = await chatStatisticsService.getChatStatistics(userId, chatId);
    if (!stats) return [];
    
    const properties: string[] = [];
    
    function extractProperties(obj: any, prefix = ''): void {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          properties.push(fullKey);
          
          if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
            extractProperties(obj[key], fullKey);
          }
        }
      }
    }
    
    extractProperties(stats);
    return properties.sort();
  } catch (error) {
    console.error('Erro ao listar propriedades:', error);
    return [];
  }
}
