import { NextRequest, NextResponse } from 'next/server';
import { syncService } from '@/lib/syncService';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId,
      chatId,
      role,
      content,
      attachments,
      usage,
      responseTime,
      typingSpeed
    } = body;

    // Validar parâmetros obrigatórios
    if (!userId || !chatId || !role || !content) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, chatId, role, content' },
        { status: 400 }
      );
    }

    // Validar role
    if (role !== 'user' && role !== 'assistant') {
      return NextResponse.json(
        { error: 'Invalid role. Must be "user" or "assistant"' },
        { status: 400 }
      );
    }

    // Adicionar mensagem ao chat com dados de usage
    await syncService.addMessageToChat(
      userId,
      chatId,
      role,
      content,
      attachments,
      usage,
      responseTime,
      typingSpeed
    );

    return NextResponse.json({
      success: true,
      message: 'Message added successfully'
    });

  } catch (error) {
    console.error('Error adding message:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const userId = request.nextUrl.searchParams.get('userId');
    const chatId = request.nextUrl.searchParams.get('chatId');

    // Validar parâmetros obrigatórios
    if (!userId || !chatId) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, chatId' },
        { status: 400 }
      );
    }

    // Obter mensagens do chat
    const messages = await syncService.getChatMessages(userId, chatId);

    return NextResponse.json({ 
      success: true, 
      messages 
    });

  } catch (error) {
    console.error('Error getting messages:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
