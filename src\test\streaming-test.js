// Teste básico do sistema de streaming
// Execute este arquivo no console do navegador para testar

async function testStreamingSystem() {
  console.log('🧪 Iniciando teste do sistema de streaming...');

  try {
    // 1. Testar configurações de streaming
    console.log('📋 Testando configurações de streaming...');
    
    // Simular carregamento das configurações
    const mockUserSettings = {
      username: 'Teste',
      email: '<EMAIL>',
      streaming: { enabled: true }
    };
    
    console.log('✅ Configurações carregadas:', mockUserSettings);

    // 2. Testar criação de stream
    console.log('🚀 Testando criação de stream...');
    
    const streamRequest = {
      messages: [
        { role: 'user', content: 'Olá, como você está?' }
      ],
      model: 'google/gemini-2.5-flash',
      temperature: 0.7,
      max_tokens: 2048,
      streaming: true
    };

    // Simular requisição de stream
    console.log('📤 Enviando requisição de stream:', streamRequest);
    
    // 3. Testar conexão SSE (simulada)
    console.log('🔗 Testando conexão Server-Sent Events...');
    
    const mockStreamData = [
      { content: 'Olá! ', isComplete: false },
      { content: 'Eu ', isComplete: false },
      { content: 'estou ', isComplete: false },
      { content: 'muito ', isComplete: false },
      { content: 'bem, ', isComplete: false },
      { content: 'obrigado ', isComplete: false },
      { content: 'por ', isComplete: false },
      { content: 'perguntar!', isComplete: false },
      { content: '', isComplete: true, metadata: { processingTime: 1500 } }
    ];

    // Simular streaming
    let fullContent = '';
    for (let i = 0; i < mockStreamData.length; i++) {
      const chunk = mockStreamData[i];
      
      if (chunk.content) {
        fullContent += chunk.content;
        console.log(`📝 Chunk ${i + 1}: "${chunk.content}" | Total: "${fullContent}"`);
      }
      
      if (chunk.isComplete) {
        console.log('✅ Stream completo!');
        console.log('📊 Metadata:', chunk.metadata);
        break;
      }
      
      // Simular delay entre chunks
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 4. Testar cancelamento
    console.log('❌ Testando cancelamento de stream...');
    
    let cancelled = false;
    const cancelTest = () => {
      cancelled = true;
      console.log('🛑 Stream cancelado pelo usuário');
    };

    // Simular cancelamento após 3 chunks
    setTimeout(cancelTest, 300);

    console.log('🎉 Teste do sistema de streaming concluído com sucesso!');
    
    return {
      success: true,
      finalContent: fullContent,
      chunksProcessed: mockStreamData.length - 1
    };

  } catch (error) {
    console.error('❌ Erro no teste de streaming:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Função para testar componentes React (simulada)
function testReactComponents() {
  console.log('⚛️ Testando componentes React...');
  
  // Simular estados do hook de streaming
  const mockHookState = {
    loading: false,
    error: null,
    streamingContent: 'Olá! Eu estou muito bem, obrigado por perguntar!',
    isStreaming: false
  };
  
  console.log('🎯 Estado do hook de streaming:', mockHookState);
  
  // Simular props do StreamingBubble
  const mockBubbleProps = {
    content: mockHookState.streamingContent,
    model: 'google/gemini-2.5-flash',
    isStreaming: mockHookState.isStreaming,
    onCancel: () => console.log('🛑 Cancelamento solicitado')
  };
  
  console.log('💬 Props do StreamingBubble:', mockBubbleProps);
  
  return {
    hookState: mockHookState,
    bubbleProps: mockBubbleProps
  };
}

// Executar testes
console.log('🔬 Iniciando bateria de testes do sistema de streaming...');

testStreamingSystem().then(result => {
  console.log('📋 Resultado do teste de streaming:', result);
  
  const componentTest = testReactComponents();
  console.log('📋 Resultado do teste de componentes:', componentTest);
  
  console.log('✨ Todos os testes concluídos!');
});

// Exportar para uso em outros arquivos
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testStreamingSystem,
    testReactComponents
  };
}
