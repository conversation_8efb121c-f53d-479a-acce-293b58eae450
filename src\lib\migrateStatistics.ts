import { chatStatisticsService } from './chatStatisticsService';
import { getUserConversations } from './conversationService';

/**
 * Função para migrar todas as estatísticas existentes de um usuário
 * Adiciona as novas propriedades às estatísticas que não as possuem
 */
export async function migrateUserStatistics(userId: string): Promise<void> {
  try {
    console.log('🔄 Iniciando migração de estatísticas para usuário:', userId);
    
    // Obter todas as conversas do usuário
    const conversations = await getUserConversations(userId, false);
    console.log(`📊 Encontradas ${conversations.length} conversas para migrar`);
    
    let migratedCount = 0;
    let errorCount = 0;
    
    for (const conversation of conversations) {
      try {
        // Obter estatísticas existentes
        const existingStats = await chatStatisticsService.getChatStatistics(userId, conversation.id);
        
        if (existingStats) {
          // Forçar salvamento para aplicar migração
          await chatStatisticsService.saveChatStatistics(userId, conversation.id, existingStats);
          migratedCount++;
          console.log(`✅ Migrado: ${conversation.name}`);
        } else {
          console.log(`⚠️ Sem estatísticas: ${conversation.name}`);
        }
      } catch (error) {
        console.error(`❌ Erro ao migrar ${conversation.name}:`, error);
        errorCount++;
      }
    }
    
    console.log(`🎉 Migração concluída! ${migratedCount} migradas, ${errorCount} erros`);
  } catch (error) {
    console.error('❌ Erro durante migração:', error);
    throw error;
  }
}

/**
 * Função para verificar se uma conversa precisa de migração
 */
export async function checkIfNeedsMigration(userId: string, chatId: string): Promise<boolean> {
  try {
    const stats = await chatStatisticsService.getChatStatistics(userId, chatId);
    
    if (!stats) return false;
    
    // Verificar se tem as novas propriedades
    const hasNewProperties = !!(
      stats.messageEdits &&
      stats.sentimentAnalysis &&
      stats.conversationPatterns &&
      stats.messageComplexity &&
      stats.readingMetrics &&
      stats.featureUsage &&
      stats.conversationEfficiency
    );
    
    return !hasNewProperties;
  } catch (error) {
    console.error('Erro ao verificar migração:', error);
    return false;
  }
}

/**
 * Função para migrar uma conversa específica
 */
export async function migrateSingleChat(userId: string, chatId: string): Promise<boolean> {
  try {
    const stats = await chatStatisticsService.getChatStatistics(userId, chatId);
    
    if (stats) {
      await chatStatisticsService.saveChatStatistics(userId, chatId, stats);
      console.log(`✅ Chat ${chatId} migrado com sucesso`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Erro ao migrar chat ${chatId}:`, error);
    return false;
  }
}
