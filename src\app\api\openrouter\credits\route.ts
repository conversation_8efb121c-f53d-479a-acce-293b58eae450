import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Get the API key from the request headers
    const apiKey = request.headers.get('x-api-key');
    
    if (!apiKey) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'API key is required' 
        },
        { status: 400 }
      );
    }

    // Fetch credits from OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/credits', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.statusText}`);
    }

    const data = await response.json();

    // Calculate balance (total_credits - total_usage)
    const balance = data.data.total_credits - data.data.total_usage;

    return NextResponse.json({
      success: true,
      data: {
        total_credits: data.data.total_credits,
        total_usage: data.data.total_usage,
        balance: balance
      }
    });

  } catch (error) {
    console.error('Error fetching OpenRouter credits:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch credits from OpenRouter',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
