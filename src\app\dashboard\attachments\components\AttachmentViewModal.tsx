'use client';

import { useEffect } from 'react';
import { AttachmentWithChat } from '@/lib/attachmentService';

interface AttachmentViewModalProps {
  attachment: AttachmentWithChat | null;
  isOpen: boolean;
  onClose: () => void;
  onDownload: (attachment: AttachmentWithChat) => void;
  onGoToChat: (chatId: string) => void;
}

export default function AttachmentViewModal({
  attachment,
  isOpen,
  onClose,
  onDownload,
  onGoToChat
}: AttachmentViewModalProps) {
  // Fechar modal com ESC
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !attachment) {
    return null;
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/70 backdrop-blur-sm" />
      
      {/* Modal */}
      <div className="relative bg-blue-900/95 border border-blue-700/30 rounded-xl shadow-2xl max-w-4xl max-h-[90vh] w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-blue-700/30">
          <div className="flex-1 min-w-0">
            <h2 className="text-xl font-semibold text-white truncate" title={attachment.filename}>
              {attachment.filename}
            </h2>
            <div className="flex items-center gap-4 mt-2 text-sm text-blue-300/70">
              <span>{formatFileSize(attachment.size)}</span>
              <span>•</span>
              <span>{formatDate(attachment.uploadedAt)}</span>
              <span>•</span>
              <button
                onClick={() => onGoToChat(attachment.chatId)}
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                {attachment.chatName}
              </button>
            </div>
          </div>
          
          {/* Ações do header */}
          <div className="flex items-center gap-2 ml-4">
            <button
              onClick={() => onDownload(attachment)}
              className="p-2 bg-green-600/50 hover:bg-green-600/70 text-green-200 hover:text-white rounded-lg transition-all duration-200"
              title="Download"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </button>
            <button
              onClick={onClose}
              className="p-2 bg-blue-700/50 hover:bg-blue-600/70 text-blue-300 hover:text-white rounded-lg transition-all duration-200"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Conteúdo */}
        <div className="p-6 overflow-auto max-h-[calc(90vh-120px)]">
          {attachment.type === 'image' ? (
            <div className="flex items-center justify-center">
              <img
                src={attachment.url}
                alt={attachment.filename}
                className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
              />
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="bg-blue-800/50 p-8 rounded-xl border border-blue-600/30 text-center">
                <svg className="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="text-lg font-medium text-white mb-2">Arquivo PDF</h3>
                <p className="text-blue-300/70 mb-6">
                  Visualização de PDFs não está disponível no navegador.
                  <br />
                  Faça o download para visualizar o arquivo.
                </p>
                <button
                  onClick={() => onDownload(attachment)}
                  className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors font-medium"
                >
                  <svg className="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Fazer Download
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer com informações adicionais */}
        <div className="border-t border-blue-700/30 p-4 bg-blue-800/30">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-4 text-blue-300/70">
              <span className="flex items-center gap-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                </svg>
                {attachment.type.toUpperCase()}
              </span>
              <span className="flex items-center gap-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
                {formatFileSize(attachment.size)}
              </span>
            </div>
            <div className="text-blue-300/70">
              Pressione ESC para fechar
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
