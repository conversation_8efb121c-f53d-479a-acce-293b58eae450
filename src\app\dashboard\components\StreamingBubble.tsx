'use client';

import { useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeHighlight from 'rehype-highlight';

interface StreamingBubbleProps {
  content: string;
  model: string;
  isStreaming: boolean;
}

const StreamingBubble = ({ content, model, isStreaming }: StreamingBubbleProps) => {
  const [displayContent, setDisplayContent] = useState('');

  useEffect(() => {
    setDisplayContent(content);
  }, [content]);

  return (
    <div className="flex mb-4 sm:mb-6 group justify-start w-full max-w-full">
      <div className="flex items-start space-x-2 sm:space-x-4 max-w-[90%] sm:max-w-[75%] relative break-words overflow-hidden">
        {/* Avatar da IA */}
        <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg bg-gradient-to-br from-blue-500 to-cyan-600 shadow-blue-500/30">
          <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"/>
          </svg>
        </div>

        {/* Message Content */}
        <div className="flex flex-col space-y-1.5 sm:space-y-2 flex-1 min-w-0 overflow-hidden">
          <div className="relative px-3 sm:px-6 py-3 sm:py-4 rounded-xl sm:rounded-2xl backdrop-blur-sm border shadow-lg overflow-hidden bg-gradient-to-br from-blue-900/80 to-blue-800/80 border-blue-600/30 shadow-blue-900/50">
            {/* Neumorphic glow effect */}
            <div className="absolute inset-0 rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]"></div>
            {/* Conteúdo */}
            <div className="relative z-10">
              <div className="text-white text-sm sm:text-base leading-relaxed">
                {displayContent ? (
                  <div className="prose prose-invert prose-sm max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm, remarkMath]}
                      rehypePlugins={[rehypeKatex, rehypeHighlight]}
                      components={{
                        // Customizar componentes para o tema
                        p: ({ children }) => <p className="text-white mb-2 last:mb-0">{children}</p>,
                        code: ({ children, className }) => {
                          const isInline = !className;
                          return isInline ? (
                            <code className="bg-blue-800/50 text-blue-200 px-1 py-0.5 rounded text-xs">
                              {children}
                            </code>
                          ) : (
                            <code className={className}>{children}</code>
                          );
                        },
                        pre: ({ children }) => (
                          <pre className="bg-blue-950/50 border border-blue-700/30 rounded-lg p-3 overflow-x-auto">
                            {children}
                          </pre>
                        ),
                      }}
                    >
                      {displayContent}
                    </ReactMarkdown>
                    {isStreaming && (
                      <span className="inline-block w-2 h-4 bg-blue-400 ml-1 animate-pulse"></span>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-blue-300/70">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                    </div>
                    <span className="text-xs">Gerando resposta...</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Timestamp - apenas quando não está streaming */}
          {!isStreaming && (
            <div className="text-xs text-blue-400/50 mt-1 ml-1">
              {new Date().toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StreamingBubble;
