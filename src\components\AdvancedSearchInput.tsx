'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { SearchSuggestion } from '@/lib/advancedSearchService';

interface AdvancedSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void;
  suggestions?: SearchSuggestion[];
  isSearching?: boolean;
  placeholder?: string;
  className?: string;
  showSuggestions?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
}

export const AdvancedSearchInput = ({
  value,
  onChange,
  onSuggestionSelect,
  suggestions = [],
  isSearching = false,
  placeholder = "Buscar modelos...",
  className = "",
  showSuggestions = true,
  onFocus,
  onBlur,
  onKeyDown
}: AdvancedSearchInputProps) => {
  const [isFocused, setIsFocused] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [showSuggestionsList, setShowSuggestionsList] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Mostrar sugestões quando há foco e sugestões disponíveis
  useEffect(() => {
    setShowSuggestionsList(
      isFocused && 
      showSuggestions && 
      suggestions.length > 0 && 
      value.length > 0
    );
  }, [isFocused, showSuggestions, suggestions.length, value.length]);

  // Reset do índice selecionado quando as sugestões mudam
  useEffect(() => {
    setSelectedSuggestionIndex(-1);
  }, [suggestions]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
    setSelectedSuggestionIndex(-1);
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleInputBlur = () => {
    // Delay para permitir clique nas sugestões
    setTimeout(() => {
      setIsFocused(false);
      setShowSuggestionsList(false);
      onBlur?.();
    }, 150);
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    onChange(suggestion.text);
    onSuggestionSelect?.(suggestion);
    setShowSuggestionsList(false);
    inputRef.current?.focus();
  };

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!showSuggestionsList || suggestions.length === 0) {
      onKeyDown?.(e);
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0 && selectedSuggestionIndex < suggestions.length) {
          const suggestion = suggestions[selectedSuggestionIndex];
          handleSuggestionClick(suggestion);
        }
        break;
      
      case 'Escape':
        e.preventDefault();
        setShowSuggestionsList(false);
        setSelectedSuggestionIndex(-1);
        inputRef.current?.blur();
        break;
      
      default:
        onKeyDown?.(e);
        break;
    }
  }, [showSuggestionsList, suggestions, selectedSuggestionIndex, onKeyDown]);

  const getSuggestionIcon = (type: SearchSuggestion['type']) => {
    switch (type) {
      case 'model':
        return '🤖';
      case 'provider':
        return '🏢';
      case 'category':
        return '📁';
      case 'tag':
        return '🏷️';
      case 'feature':
        return '⚡';
      case 'correction':
        return '✨';
      default:
        return '🔍';
    }
  };

  const getSuggestionTypeLabel = (type: SearchSuggestion['type']) => {
    switch (type) {
      case 'model':
        return 'Modelo';
      case 'provider':
        return 'Provedor';
      case 'category':
        return 'Categoria';
      case 'tag':
        return 'Tag';
      case 'feature':
        return 'Funcionalidade';
      case 'correction':
        return 'Correção';
      default:
        return 'Busca';
    }
  };

  return (
    <div className="relative w-full">
      {/* Input de busca */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          {isSearching ? (
            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-400"></div>
          ) : (
            <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          )}
        </div>
        
        <input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          className={`w-full bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl pl-10 pr-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200 ${className}`}
          autoComplete="off"
          spellCheck="false"
        />
        
        {/* Indicador de sugestões disponíveis */}
        {suggestions.length > 0 && !showSuggestionsList && isFocused && value.length > 0 && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-blue-400/60">
                {suggestions.length} sugestões
              </span>
              <span className="text-xs text-blue-500/60">↓</span>
            </div>
          </div>
        )}

        {/* Indicador de busca vazia */}
        {value.length === 0 && !isFocused && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className="text-xs text-blue-400/40">
              Digite para buscar...
            </span>
          </div>
        )}
      </div>

      {/* Lista de sugestões */}
      {showSuggestionsList && (
        <div 
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-blue-900/95 backdrop-blur-sm border border-blue-600/30 rounded-xl shadow-xl max-h-80 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.type}-${suggestion.text}-${index}`}
              className={`px-4 py-3 cursor-pointer transition-all duration-150 flex items-center space-x-3 ${
                index === selectedSuggestionIndex
                  ? 'bg-blue-600/50 text-blue-100'
                  : 'text-blue-200 hover:bg-blue-700/30 hover:text-blue-100'
              } ${index === 0 ? 'rounded-t-xl' : ''} ${
                index === suggestions.length - 1 ? 'rounded-b-xl' : 'border-b border-blue-700/30'
              }`}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              {/* Ícone da sugestão */}
              <span className="text-lg flex-shrink-0">
                {getSuggestionIcon(suggestion.type)}
              </span>
              
              {/* Conteúdo da sugestão */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="font-medium truncate">
                    {suggestion.text}
                  </span>
                  <span className={`text-xs px-2 py-0.5 rounded-full flex-shrink-0 ${
                    suggestion.type === 'correction' 
                      ? 'bg-yellow-500/20 text-yellow-300'
                      : 'bg-blue-500/20 text-blue-300'
                  }`}>
                    {suggestion.description || getSuggestionTypeLabel(suggestion.type)}
                  </span>
                </div>
              </div>
              
              {/* Score visual (opcional) */}
              {suggestion.score && suggestion.score < 1 && (
                <div className="flex-shrink-0 w-12 h-1 bg-blue-800/50 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-blue-400 transition-all duration-300"
                    style={{ width: `${suggestion.score * 100}%` }}
                  />
                </div>
              )}
            </div>
          ))}
          
          {/* Footer com dicas */}
          <div className="px-4 py-2 border-t border-blue-700/30 bg-blue-900/50">
            <div className="flex items-center justify-between text-xs text-blue-400/70">
              <span>Use ↑↓ para navegar, Enter para selecionar</span>
              <span>ESC para fechar</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedSearchInput;

// Componente para destacar texto com matches
export const HighlightedText = ({
  text,
  highlight,
  className = ""
}: {
  text: string;
  highlight: string;
  className?: string;
}) => {
  if (!highlight.trim()) {
    return <span className={className}>{text}</span>;
  }

  const regex = new RegExp(`(${highlight.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return (
    <span className={className}>
      {parts.map((part, index) =>
        regex.test(part) ? (
          <mark key={index} className="bg-yellow-300/30 text-yellow-200 px-0.5 rounded">
            {part}
          </mark>
        ) : (
          <span key={index}>{part}</span>
        )
      )}
    </span>
  );
};
