import { db, storage } from './firebase';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  getDocs,
  deleteDoc 
} from 'firebase/firestore';
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage';

// Interfaces
export interface ChatAppearanceSettings {
  fontSize: number; // em pixels, padrão 14
  fontFamily: string; // nome da fonte, padrão 'Inter'
}

export interface StreamingSettings {
  enabled: boolean; // se o streaming está habilitado, padrão false
}

export interface ChatSessionSettings {
  enabled: boolean; // se as sessões estão habilitadas, padrão false
  wordsPerSession: number; // limite de palavras por sessão, padrão 5000
}

export interface UserSettings {
  username: string;
  email: string;
  profilePhotoURL?: string;
  chatAppearance?: ChatAppearanceSettings;
  streaming?: StreamingSettings;
  chatSessions?: ChatSessionSettings;
}

export interface APIEndpoint {
  id: string;
  name: string;
  endpoint: string;
  apiKey: string;
  defaultModel: string;
  isActive: boolean;
  createdAt: number;
}

export interface EndpointTemplate {
  name: string;
  endpoint: string;
  description: string;
}

// Interfaces para o sistema de memórias
export interface Memory {
  id: string;
  title: string;
  content: string;
  backgroundColor: string;
  isActive: boolean;
  categoryId?: string; // ID da categoria (opcional)
  chatId?: string; // ID do chat específico (opcional - se não definido, é global)
  createdAt: number;
  updatedAt: number;
}

export interface MemoryCategory {
  id: string;
  name: string;
  description?: string;
  isActive: boolean; // Ativa/desativa todas as memórias da categoria
  backgroundColor: string;
  createdAt: number;
  updatedAt: number;
}

export interface MemoryFilters {
  categoryId?: string;
  chatId?: string;
  isActive?: boolean;
}

// Templates de endpoints padrão
export const DEFAULT_ENDPOINTS: EndpointTemplate[] = [
  {
    name: 'OpenRouter',
    endpoint: 'https://openrouter.ai/api/v1',
    description: 'Acesso a múltiplos modelos de IA'
  },
  {
    name: 'OpenAI',
    endpoint: 'https://api.openai.com/v1',
    description: 'GPT-4, GPT-3.5 e outros modelos OpenAI'
  },
  {
    name: 'Anthropic',
    endpoint: 'https://api.anthropic.com/v1',
    description: 'Claude e outros modelos Anthropic'
  },
  {
    name: 'Google',
    endpoint: 'https://generativelanguage.googleapis.com/v1',
    description: 'Gemini e outros modelos Google'
  },
  {
    name: 'DeepSeek',
    endpoint: 'https://api.deepseek.com/v1',
    description: 'Modelos DeepSeek'
  }
];

// Serviços para configurações do usuário
export const updateUserSettings = async (userId: string, settings: Partial<UserSettings>): Promise<void> => {
  try {
    const userRef = doc(db, 'usuarios', userId);
    // Usar setDoc com merge para criar o documento se não existir ou atualizar se existir
    await setDoc(userRef, settings, { merge: true });
    console.log('User settings updated successfully');
  } catch (error) {
    console.error('Error updating user settings:', error);
    throw error;
  }
};

export const getUserSettings = async (userId: string): Promise<UserSettings | null> => {
  try {
    const userRef = doc(db, 'usuarios', userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      const data = userDoc.data();
      return {
        username: data.username || '',
        email: data.email || '',
        profilePhotoURL: data.profilePhotoURL,
        chatAppearance: data.chatAppearance || {
          fontSize: 14,
          fontFamily: 'Inter'
        },
        streaming: data.streaming || {
          enabled: false
        },
        chatSessions: data.chatSessions || {
          enabled: false,
          wordsPerSession: 5000
        }
      };
    }
    return null;
  } catch (error) {
    console.error('Error getting user settings:', error);
    throw error;
  }
};

// Serviços para foto de perfil
export const uploadProfilePhoto = async (userId: string, file: File): Promise<string> => {
  try {
    // Validar arquivo
    if (!file.type.startsWith('image/')) {
      throw new Error('Arquivo deve ser uma imagem');
    }
    
    if (file.size > 5 * 1024 * 1024) { // 5MB
      throw new Error('Arquivo deve ter menos de 5MB');
    }

    // Upload para Storage
    const photoRef = ref(storage, `usuarios/${userId}/configuracoes/foto-perfil`);
    await uploadBytes(photoRef, file);
    
    // Obter URL de download
    const downloadURL = await getDownloadURL(photoRef);
    
    // Atualizar Firestore
    await updateUserSettings(userId, { profilePhotoURL: downloadURL });
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading profile photo:', error);
    throw error;
  }
};

export const deleteProfilePhoto = async (userId: string): Promise<void> => {
  try {
    // Deletar do Storage
    const photoRef = ref(storage, `usuarios/${userId}/configuracoes/foto-perfil`);
    await deleteObject(photoRef);
    
    // Remover URL do Firestore
    await updateUserSettings(userId, { profilePhotoURL: undefined });
  } catch (error) {
    console.error('Error deleting profile photo:', error);
    throw error;
  }
};

// Serviços para endpoints de API
export const addAPIEndpoint = async (userId: string, endpoint: Omit<APIEndpoint, 'id' | 'createdAt'>): Promise<string> => {
  try {
    const endpointId = `endpoint_${Date.now()}`;
    const endpointData: APIEndpoint = {
      ...endpoint,
      id: endpointId,
      createdAt: Date.now()
    };

    const endpointRef = doc(db, 'usuarios', userId, 'endpoints', endpointId);
    await setDoc(endpointRef, endpointData);
    
    console.log('API endpoint added successfully');
    return endpointId;
  } catch (error) {
    console.error('Error adding API endpoint:', error);
    throw error;
  }
};

export const updateAPIEndpoint = async (userId: string, endpointId: string, updates: Partial<APIEndpoint>): Promise<void> => {
  try {
    const endpointRef = doc(db, 'usuarios', userId, 'endpoints', endpointId);
    await updateDoc(endpointRef, updates);
    console.log('API endpoint updated successfully');
  } catch (error) {
    console.error('Error updating API endpoint:', error);
    throw error;
  }
};

export const deleteAPIEndpoint = async (userId: string, endpointId: string): Promise<void> => {
  try {
    const endpointRef = doc(db, 'usuarios', userId, 'endpoints', endpointId);
    await deleteDoc(endpointRef);
    console.log('API endpoint deleted successfully');
  } catch (error) {
    console.error('Error deleting API endpoint:', error);
    throw error;
  }
};

export const getUserAPIEndpoints = async (userId: string): Promise<APIEndpoint[]> => {
  try {
    const endpointsRef = collection(db, 'usuarios', userId, 'endpoints');
    const querySnapshot = await getDocs(endpointsRef);
    
    const endpoints: APIEndpoint[] = [];
    querySnapshot.forEach((doc) => {
      endpoints.push(doc.data() as APIEndpoint);
    });
    
    // Ordenar por data de criação
    endpoints.sort((a, b) => b.createdAt - a.createdAt);
    
    return endpoints;
  } catch (error) {
    console.error('Error getting user API endpoints:', error);
    throw error;
  }
};

export const getActiveAPIEndpoint = async (userId: string): Promise<APIEndpoint | null> => {
  try {
    const endpoints = await getUserAPIEndpoints(userId);
    return endpoints.find(endpoint => endpoint.isActive) || null;
  } catch (error) {
    console.error('Error getting active API endpoint:', error);
    throw error;
  }
};

export const setActiveAPIEndpoint = async (userId: string, endpointId: string, isActive: boolean): Promise<void> => {
  try {
    // Apenas ativar/desativar o endpoint específico
    await updateAPIEndpoint(userId, endpointId, { isActive });

    console.log(`API endpoint ${isActive ? 'activated' : 'deactivated'} successfully`);
  } catch (error) {
    console.error('Error setting active API endpoint:', error);
    throw error;
  }
};

export const getUserDefaultModel = async (userId: string): Promise<string> => {
  try {
    const endpoints = await getUserAPIEndpoints(userId);

    // Buscar o primeiro endpoint ativo
    const activeEndpoint = endpoints.find(endpoint => endpoint.isActive);

    if (activeEndpoint && activeEndpoint.defaultModel) {
      return activeEndpoint.defaultModel;
    }

    // Fallback para google/gemini-2.5-flash se não houver endpoint ativo ou modelo padrão
    return 'google/gemini-2.5-flash';
  } catch (error) {
    console.error('Error getting user default model:', error);
    // Fallback em caso de erro
    return 'google/gemini-2.5-flash';
  }
};

// Serviços para configurações de aparência do chat
export const updateChatAppearance = async (userId: string, appearance: ChatAppearanceSettings): Promise<void> => {
  try {
    const userRef = doc(db, 'usuarios', userId);
    // Usar setDoc com merge para criar o documento se não existir ou atualizar se existir
    await setDoc(userRef, { chatAppearance: appearance }, { merge: true });
    console.log('Chat appearance settings updated successfully');
  } catch (error) {
    console.error('Error updating chat appearance settings:', error);
    throw error;
  }
};

// Serviços para configurações de streaming
export const updateStreamingSettings = async (userId: string, streaming: StreamingSettings): Promise<void> => {
  try {
    const userRef = doc(db, 'usuarios', userId);
    await setDoc(userRef, { streaming }, { merge: true });
    console.log('Streaming settings updated successfully');
  } catch (error) {
    console.error('Error updating streaming settings:', error);
    throw error;
  }
};

export const getStreamingSettings = async (userId: string): Promise<StreamingSettings> => {
  try {
    const userSettings = await getUserSettings(userId);
    return userSettings?.streaming || { enabled: false };
  } catch (error) {
    console.error('Error getting streaming settings:', error);
    return { enabled: false };
  }
};

export const getChatAppearance = async (userId: string): Promise<ChatAppearanceSettings> => {
  try {
    const userSettings = await getUserSettings(userId);
    return userSettings?.chatAppearance || {
      fontSize: 14,
      fontFamily: 'Inter'
    };
  } catch (error) {
    console.error('Error getting chat appearance settings:', error);
    return {
      fontSize: 14,
      fontFamily: 'Inter'
    };
  }
};

// Serviços para configurações de sessões de chat
export const updateChatSessions = async (userId: string, sessions: ChatSessionSettings): Promise<void> => {
  try {
    const userRef = doc(db, 'usuarios', userId);
    await setDoc(userRef, { chatSessions: sessions }, { merge: true });
    console.log('Chat session settings updated successfully');
  } catch (error) {
    console.error('Error updating chat session settings:', error);
    throw error;
  }
};

export const getChatSessions = async (userId: string): Promise<ChatSessionSettings> => {
  try {
    const userSettings = await getUserSettings(userId);
    return userSettings?.chatSessions || {
      enabled: false,
      wordsPerSession: 5000
    };
  } catch (error) {
    console.error('Error getting chat session settings:', error);
    return {
      enabled: false,
      wordsPerSession: 5000
    };
  }
};
