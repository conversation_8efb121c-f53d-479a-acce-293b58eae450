'use client';

import { useDraggable } from '@dnd-kit/core';
import { Conversation } from '@/lib/conversationService';

interface DraggableConversationProps {
  conversation: Conversation;
  children: React.ReactNode;
  isActive: boolean;
}

export default function DraggableConversation({
  conversation,
  children,
  isActive
}: DraggableConversationProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: conversation.id,
    data: {
      type: 'conversation',
      conversation,
    },
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`transition-all duration-200 ${
        isDragging
          ? 'opacity-50 scale-105 z-50 shadow-2xl cursor-grabbing'
          : 'cursor-pointer hover:cursor-grab'
      }`}
    >
      {children}
    </div>
  );
}
