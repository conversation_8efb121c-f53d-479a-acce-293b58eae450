'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { generalStatisticsService, GeneralStatistics } from '@/lib/generalStatisticsService';
import StatsHeader from './components/StatsHeader';
import MetricsGrid from './components/MetricsGrid';
import ActivitySection from './components/ActivitySection';
import PerformanceSection from './components/PerformanceSection';
import WordAnalysisSection from './components/WordAnalysisSection';
import InsightsSection from './components/InsightsSection';
import LoadingState from './components/LoadingState';
import ErrorState from './components/ErrorState';
import EmptyState from './components/EmptyState';

export default function StatsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [statistics, setStatistics] = useState<GeneralStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Enable page scroll
  useEffect(() => {
    document.body.style.overflow = 'auto';
    return () => {
      document.body.style.overflow = 'hidden';
    };
  }, []);

  useEffect(() => {
    if (user) {
      loadStatistics();
    }
  }, [user]);

  const loadStatistics = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);
      
      const stats = await generalStatisticsService.getGeneralStatistics(user!.uid);
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading statistics:', error);
      setError('Erro ao carregar estatísticas');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadStatistics(true);
  };

  if (!user) {
    router.push('/auth');
    return null;
  }

  if (loading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={() => loadStatistics()} />;
  }

  if (!statistics || statistics.totalChats === 0) {
    return <EmptyState />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto p-4 sm:p-6 lg:p-8 space-y-8">
        {/* Header */}
        <StatsHeader 
          statistics={statistics} 
          onRefresh={handleRefresh} 
          refreshing={refreshing} 
        />

        {/* Main Metrics Grid */}
        <MetricsGrid statistics={statistics} />

        {/* Activity Section */}
        <ActivitySection statistics={statistics} />

        {/* Performance Section */}
        <PerformanceSection statistics={statistics} />

        {/* Word Analysis Section */}
        <WordAnalysisSection statistics={statistics} />

        {/* Insights Section */}
        <InsightsSection statistics={statistics} />
      </div>
    </div>
  );
}
