'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { 
  ChatFolder, 
  ChatFolderInput, 
  FolderContextState, 
  FolderActions 
} from '@/lib/types/chatFolder';
import {
  createChatFolder,
  getUserChatFolders,
  updateChatFolder,
  deleteChatFolder,
  reorderChatFolders,
  invalidateFolderCache
} from '@/lib/chatFolderService';
import { moveConversationToChatFolder } from '@/lib/conversationService';

interface ChatFolderContextType extends FolderContextState, FolderActions {}

const ChatFolderContext = createContext<ChatFolderContextType | undefined>(undefined);

export const useChatFolders = () => {
  const context = useContext(ChatFolderContext);
  if (context === undefined) {
    throw new Error('useChatFolders must be used within a ChatFolderProvider');
  }
  return context;
};

interface ChatFolderProviderProps {
  children: React.ReactNode;
}

export const ChatFolderProvider: React.FC<ChatFolderProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [folders, setFolders] = useState<ChatFolder[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());

  // Carregar pastas quando o usuário mudar
  const refreshFolders = useCallback(async () => {
    if (!user) {
      setFolders([]);
      setExpandedFolders(new Set());
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const userFolders = await getUserChatFolders(user.uid, false); // Sempre buscar dados frescos
      setFolders(userFolders);
      
      // Expandir pastas que estão marcadas como expandidas
      const expandedIds = new Set(
        userFolders
          .filter(folder => folder.isExpanded)
          .map(folder => folder.id)
      );
      setExpandedFolders(expandedIds);
    } catch (err) {
      console.error('Error loading folders:', err);
      setError('Erro ao carregar pastas');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Criar nova pasta
  const createFolder = useCallback(async (folderData: ChatFolderInput): Promise<string> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const folderId = await createChatFolder(user.uid, folderData);
      await refreshFolders(); // Recarregar lista
      return folderId;
    } catch (error) {
      console.error('Error creating folder:', error);
      throw error;
    }
  }, [user]);

  // Atualizar pasta
  const updateFolder = useCallback(async (folderId: string, updates: Partial<ChatFolder>): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      await updateChatFolder(user.uid, folderId, updates);
      await refreshFolders(); // Recarregar lista
    } catch (error) {
      console.error('Error updating folder:', error);
      throw error;
    }
  }, [user]);

  // Deletar pasta
  const deleteFolder = useCallback(async (folderId: string): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      await deleteChatFolder(user.uid, folderId);
      await refreshFolders(); // Recarregar lista
    } catch (error) {
      console.error('Error deleting folder:', error);
      throw error;
    }
  }, [user]);

  // Alternar expansão de pasta
  const toggleFolderExpansion = useCallback((folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      
      // Atualizar no Firestore também
      if (user) {
        const folder = folders.find(f => f.id === folderId);
        if (folder) {
          updateChatFolder(user.uid, folderId, { 
            isExpanded: newSet.has(folderId) 
          }).catch(console.error);
        }
      }
      
      return newSet;
    });
  }, [user, folders]);

  // Mover conversa para pasta
  const moveConversationToFolder = useCallback(async (conversationId: string, folderId: string | null): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      await moveConversationToChatFolder(user.uid, conversationId, folderId);
    } catch (error) {
      console.error('Error moving conversation to folder:', error);
      throw error;
    }
  }, [user]);

  // Reordenar pastas
  const reorderFolders = useCallback(async (folderIds: string[]): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      await reorderChatFolders(user.uid, folderIds);
      await refreshFolders(); // Recarregar lista
    } catch (error) {
      console.error('Error reordering folders:', error);
      throw error;
    }
  }, [user]);

  // Carregar pastas quando o usuário mudar
  useEffect(() => {
    if (user) {
      refreshFolders();
    } else {
      setFolders([]);
      setExpandedFolders(new Set());
      setError(null);
    }
  }, [user]);

  // Invalidar cache quando o componente for desmontado
  useEffect(() => {
    return () => {
      invalidateFolderCache();
    };
  }, []);

  const value: ChatFolderContextType = {
    // Estado
    folders,
    loading,
    error,
    expandedFolders,
    
    // Ações
    createFolder,
    updateFolder,
    deleteFolder,
    toggleFolderExpansion,
    moveConversationToFolder,
    reorderFolders,
    refreshFolders
  };

  return (
    <ChatFolderContext.Provider value={value}>
      {children}
    </ChatFolderContext.Provider>
  );
};
