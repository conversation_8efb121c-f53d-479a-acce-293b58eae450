import { useState, useEffect } from 'react';
import { openRouterService } from '@/lib/openRouterService';
import { deepSeekService } from '@/lib/deepSeekService';

interface UseModelNameReturn {
  modelName: string;
  loading: boolean;
}

export const useModelName = (modelId: string): UseModelNameReturn => {
  const [modelName, setModelName] = useState(modelId);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchModelName = async () => {
      if (!modelId) {
        setModelName('');
        return;
      }

      setLoading(true);
      
      try {
        // Check if it's a DeepSeek model
        if (modelId.includes('deepseek')) {
          const deepSeekModels = await deepSeekService.fetchModels();
          const model = deepSeekModels.find(m => m.id === modelId);
          setModelName(model ? model.name : modelId);
        } else {
          // Try OpenRouter
          const name = await openRouterService.getModelNameById(modelId);
          setModelName(name);
        }
      } catch (error) {
        console.error('Error fetching model name:', error);
        setModelName(modelId); // Fallback to ID
      } finally {
        setLoading(false);
      }
    };

    fetchModelName();
  }, [modelId]);

  return { modelName, loading };
};
