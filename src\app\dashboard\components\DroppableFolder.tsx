'use client';

import { useDroppable } from '@dnd-kit/core';
import { ChatFolder as ChatFolderType } from '@/lib/types/chatFolder';

interface DroppableFolderProps {
  folder?: ChatFolderType; // undefined para área "sem pasta"
  children: React.ReactNode;
  className?: string;
}

export default function DroppableFolder({
  folder,
  children,
  className = ''
}: DroppableFolderProps) {
  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id: folder ? `folder-${folder.id}` : 'unorganized',
    data: {
      type: 'folder',
      folderId: folder?.id || null,
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={`transition-all duration-200 ${
        isOver 
          ? 'bg-blue-600/20 border-2 border-blue-400/50 border-dashed rounded-lg' 
          : ''
      } ${className}`}
    >
      {children}
      
      {/* Indicador visual quando algo está sendo arrastado sobre a pasta */}
      {isOver && (
        <div className="absolute inset-0 bg-blue-500/10 border-2 border-blue-400/50 border-dashed rounded-lg pointer-events-none flex items-center justify-center">
          <div className="bg-blue-600/90 text-white px-3 py-1 rounded-lg text-sm font-medium">
            {folder ? `Soltar em "${folder.name}"` : 'Soltar em "Sem pasta"'}
          </div>
        </div>
      )}
    </div>
  );
}
