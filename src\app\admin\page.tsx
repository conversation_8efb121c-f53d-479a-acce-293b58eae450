'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { adminService, UserInfo, SiteMaintenanceSettings } from '@/lib/adminService';
import { 
  Users, 
  Settings, 
  Shield, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Eye,
  EyeOff,
  Save,
  Power,
  PowerOff
} from 'lucide-react';

const AdminPage = () => {
  const [activeTab, setActiveTab] = useState<'users' | 'maintenance'>('users');
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [maintenanceSettings, setMaintenanceSettings] = useState<SiteMaintenanceSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [hasAdminAccess, setHasAdminAccess] = useState(false);
  
  // Maintenance form state
  const [maintenancePassword, setMaintenancePassword] = useState('');
  const [maintenanceMessage, setMaintenanceMessage] = useState('');
  const [showMaintenancePassword, setShowMaintenancePassword] = useState(false);
  const [isUpdatingMaintenance, setIsUpdatingMaintenance] = useState(false);

  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  useEffect(() => {
    // Só verificar acesso quando o auth não estiver carregando
    if (!authLoading) {
      checkAdminAccess();
    }
  }, [user, authLoading]);

  useEffect(() => {
    if (hasAdminAccess) {
      loadData();
    }
  }, [hasAdminAccess]);

  const checkAdminAccess = async () => {
    try {
      console.log('🔐 Verificando acesso de admin...', { user: !!user, email: user?.email, authLoading });

      // Se ainda está carregando, não fazer nada
      if (authLoading) {
        console.log('🔐 Auth ainda carregando, aguardando...');
        return;
      }

      // Se não há usuário após carregar, redirecionar
      if (!user) {
        console.log('🔐 Usuário não logado, redirecionando para auth');
        router.push('/auth');
        return;
      }

      // Verificar se há sessão de admin ativa (usuário validou senha recentemente)
      const adminSession = sessionStorage.getItem('admin_session');
      const adminSessionTime = sessionStorage.getItem('admin_session_time');

      console.log('🔐 Verificando sessão de admin:', { adminSession, adminSessionTime });

      if (adminSession === 'active' && adminSessionTime) {
        const sessionTime = parseInt(adminSessionTime);
        const now = Date.now();
        const sessionDuration = 30 * 60 * 1000; // 30 minutos

        console.log('🔐 Verificando tempo de sessão:', { sessionTime, now, diff: now - sessionTime, duration: sessionDuration });

        if (now - sessionTime < sessionDuration) {
          console.log('🔐 Sessão de admin ativa - acesso liberado');
          setHasAdminAccess(true);
          return;
        } else {
          // Sessão expirada
          console.log('🔐 Sessão de admin expirada, removendo...');
          sessionStorage.removeItem('admin_session');
          sessionStorage.removeItem('admin_session_time');
        }
      }

      console.log('🔐 Verificando se usuário é admin...');
      const isAdmin = await adminService.isCurrentUserAdmin();
      console.log('🔐 Usuário é admin:', isAdmin);

      if (!isAdmin) {
        console.log('🔐 Acesso negado - usuário não é admin e sem sessão válida');
        setError('Acesso negado. Você não tem permissões de administrador.');
        setTimeout(() => router.push('/dashboard'), 3000);
        return;
      }

      console.log('🔐 Acesso liberado para admin');
      setHasAdminAccess(true);
    } catch (error) {
      console.error('🔐 Erro ao verificar acesso de admin:', error);
      setError('Erro ao verificar permissões de administrador');
      setTimeout(() => router.push('/auth'), 3000);
    }
  };

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [usersData, maintenanceData] = await Promise.all([
        adminService.getAllUsers(),
        adminService.getMaintenanceSettings()
      ]);

      setUsers(usersData);
      setMaintenanceSettings(maintenanceData);
      
      if (maintenanceData) {
        setMaintenancePassword(maintenanceData.password);
        setMaintenanceMessage(maintenanceData.message || '');
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      setError('Erro ao carregar dados da administração');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      await adminService.toggleUserStatus(userId, !currentStatus);
      setSuccess(`Usuário ${!currentStatus ? 'ativado' : 'desativado'} com sucesso`);
      loadData(); // Recarregar dados
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Erro ao alterar status do usuário:', error);
      setError('Erro ao alterar status do usuário');
      setTimeout(() => setError(null), 3000);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Tem certeza que deseja remover este usuário?')) return;

    try {
      await adminService.deleteUser(userId);
      setSuccess('Usuário removido com sucesso');
      loadData(); // Recarregar dados
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Erro ao remover usuário:', error);
      setError('Erro ao remover usuário');
      setTimeout(() => setError(null), 3000);
    }
  };

  const handleUpdateMaintenance = async (isActive: boolean) => {
    if (!maintenancePassword.trim()) {
      setError('Por favor, defina uma senha para o modo de manutenção');
      return;
    }

    try {
      setIsUpdatingMaintenance(true);
      setError(null);

      await adminService.setMaintenanceMode(
        isActive, 
        maintenancePassword, 
        maintenanceMessage || 'Site em manutenção. Tente novamente mais tarde.'
      );

      setSuccess(`Modo de manutenção ${isActive ? 'ativado' : 'desativado'} com sucesso`);
      loadData(); // Recarregar dados
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Erro ao atualizar modo de manutenção:', error);
      setError('Erro ao atualizar modo de manutenção');
      setTimeout(() => setError(null), 3000);
    } finally {
      setIsUpdatingMaintenance(false);
    }
  };

  if (authLoading || isLoading || !hasAdminAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto"></div>
          <h1 className="mt-6 text-2xl font-bold text-white">
            {authLoading
              ? 'Carregando Autenticação'
              : !hasAdminAccess
                ? 'Verificando Permissões'
                : 'Carregando Admin'
            }
          </h1>
          <p className="mt-2 text-purple-300">Aguarde...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <div className="bg-slate-900/50 backdrop-blur-sm border-b border-purple-500/10">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center">
                <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-purple-400" />
              </div>
              <div>
                <h1 className="text-lg sm:text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-300 to-indigo-300">
                  Painel Administrativo
                </h1>
                <p className="text-xs sm:text-sm text-purple-300/70">RafthorIA</p>
              </div>
            </div>
            <button
              onClick={() => router.push('/auth')}
              className="px-3 sm:px-4 py-2 text-xs sm:text-sm text-indigo-300 hover:text-indigo-200 transition-colors"
            >
              <span className="hidden sm:inline">Voltar ao Login</span>
              <span className="sm:hidden">Voltar</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-3 sm:px-6 py-4 sm:py-8">
        {/* Notifications */}
        {error && (
          <div className="mb-4 sm:mb-6 p-3 sm:p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-300 flex items-center gap-2 text-sm sm:text-base">
            <AlertTriangle className="w-4 h-4 sm:w-5 sm:h-5" />
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 sm:mb-6 p-3 sm:p-4 rounded-lg bg-green-500/10 border border-green-500/20 text-green-300 flex items-center gap-2 text-sm sm:text-base">
            <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5" />
            {success}
          </div>
        )}

        {/* Tabs */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 bg-slate-800/30 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('users')}
              className={`flex-1 flex items-center justify-center gap-1.5 sm:gap-2 px-3 sm:px-4 py-2.5 sm:py-3 rounded-md text-xs sm:text-sm font-medium transition-all ${
                activeTab === 'users'
                  ? 'bg-gradient-to-r from-purple-600/80 to-indigo-600/80 text-white shadow-lg'
                  : 'text-indigo-300 hover:text-indigo-200 hover:bg-slate-700/30'
              }`}
            >
              <Users className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
              <span className="hidden sm:inline">Gerenciar Usuários</span>
              <span className="sm:hidden">Usuários</span>
            </button>
            <button
              onClick={() => setActiveTab('maintenance')}
              className={`flex-1 flex items-center justify-center gap-1.5 sm:gap-2 px-3 sm:px-4 py-2.5 sm:py-3 rounded-md text-xs sm:text-sm font-medium transition-all ${
                activeTab === 'maintenance'
                  ? 'bg-gradient-to-r from-purple-600/80 to-indigo-600/80 text-white shadow-lg'
                  : 'text-indigo-300 hover:text-indigo-200 hover:bg-slate-700/30'
              }`}
            >
              <Settings className="w-4 h-4" />
              Modo de Manutenção
            </button>
          </div>
        </div>

        {/* Content */}
        {activeTab === 'users' && (
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-2xl border border-purple-500/10 shadow-xl shadow-purple-500/5 p-6">
            <div className="flex items-center gap-3 mb-6">
              <Users className="w-6 h-6 text-purple-400" />
              <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-purple-300 to-indigo-300">
                Usuários Cadastrados
              </h2>
              <span className="px-2 py-1 text-xs bg-purple-500/20 text-purple-300 rounded-full">
                {users.length} usuários
              </span>
            </div>

            <div className="space-y-3">
              {users.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-4 bg-slate-800/30 rounded-lg border border-indigo-500/10 hover:border-indigo-500/20 transition-all"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center">
                      <span className="text-sm font-medium text-purple-300">
                        {user.username.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-medium text-indigo-100">{user.username}</h3>
                      <p className="text-sm text-indigo-300/70">{user.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      user.isActive 
                        ? 'bg-green-500/20 text-green-300' 
                        : 'bg-red-500/20 text-red-300'
                    }`}>
                      {user.isActive ? 'Ativo' : 'Inativo'}
                    </span>
                    
                    <button
                      onClick={() => handleToggleUserStatus(user.id, user.isActive)}
                      className={`p-2 rounded-lg transition-all ${
                        user.isActive
                          ? 'text-red-400 hover:bg-red-500/10'
                          : 'text-green-400 hover:bg-green-500/10'
                      }`}
                      title={user.isActive ? 'Desativar usuário' : 'Ativar usuário'}
                    >
                      {user.isActive ? <XCircle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              ))}

              {users.length === 0 && (
                <div className="text-center py-8 text-indigo-300/70">
                  <Users className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>Nenhum usuário encontrado</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'maintenance' && (
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-2xl border border-purple-500/10 shadow-xl shadow-purple-500/5 p-6">
            <div className="flex items-center gap-3 mb-6">
              <Settings className="w-6 h-6 text-purple-400" />
              <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-purple-300 to-indigo-300">
                Modo de Manutenção
              </h2>
              <span className={`px-2 py-1 text-xs rounded-full ${
                maintenanceSettings?.isActive 
                  ? 'bg-red-500/20 text-red-300' 
                  : 'bg-green-500/20 text-green-300'
              }`}>
                {maintenanceSettings?.isActive ? 'Ativo' : 'Inativo'}
              </span>
            </div>

            <div className="space-y-6">
              {/* Status atual */}
              <div className="p-4 bg-slate-800/30 rounded-lg border border-indigo-500/10">
                <div className="flex items-center gap-3 mb-2">
                  <Activity className="w-5 h-5 text-indigo-400" />
                  <h3 className="font-medium text-indigo-100">Status Atual</h3>
                </div>
                <p className="text-sm text-indigo-300/70">
                  O modo de manutenção está atualmente{' '}
                  <span className={maintenanceSettings?.isActive ? 'text-red-300' : 'text-green-300'}>
                    {maintenanceSettings?.isActive ? 'ATIVO' : 'INATIVO'}
                  </span>
                </p>
              </div>

              {/* Configurações */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-indigo-300/80 mb-2">
                    Senha de Manutenção *
                  </label>
                  <div className="relative">
                    <input
                      type={showMaintenancePassword ? 'text' : 'password'}
                      value={maintenancePassword}
                      onChange={(e) => setMaintenancePassword(e.target.value)}
                      className="block w-full px-4 py-3 pr-10 bg-slate-800/60 text-indigo-100 rounded-lg border border-indigo-500/20 focus:outline-none focus:ring-2 focus:ring-purple-500/40 focus:border-transparent transition-all placeholder:text-indigo-300/30"
                      placeholder="Digite a senha para acesso durante manutenção"
                    />
                    <button
                      type="button"
                      onClick={() => setShowMaintenancePassword(!showMaintenancePassword)}
                      className="absolute right-3 top-3.5 text-indigo-400/60 hover:text-indigo-300 transition-colors"
                    >
                      {showMaintenancePassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                  <p className="text-xs text-indigo-400/60 mt-1">
                    Esta senha será solicitada aos usuários durante o modo de manutenção
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-indigo-300/80 mb-2">
                    Mensagem de Manutenção
                  </label>
                  <textarea
                    value={maintenanceMessage}
                    onChange={(e) => setMaintenanceMessage(e.target.value)}
                    rows={3}
                    className="block w-full px-4 py-3 bg-slate-800/60 text-indigo-100 rounded-lg border border-indigo-500/20 focus:outline-none focus:ring-2 focus:ring-purple-500/40 focus:border-transparent transition-all placeholder:text-indigo-300/30 resize-none"
                    placeholder="Mensagem que será exibida aos usuários (opcional)"
                  />
                </div>
              </div>

              {/* Ações */}
              <div className="flex gap-3 pt-4">
                <button
                  onClick={() => handleUpdateMaintenance(true)}
                  disabled={isUpdatingMaintenance || !maintenancePassword.trim()}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUpdatingMaintenance ? (
                    <>
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Processando...</span>
                    </>
                  ) : (
                    <>
                      <PowerOff className="w-4 h-4" />
                      <span>Ativar Manutenção</span>
                    </>
                  )}
                </button>

                <button
                  onClick={() => handleUpdateMaintenance(false)}
                  disabled={isUpdatingMaintenance}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUpdatingMaintenance ? (
                    <>
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Processando...</span>
                    </>
                  ) : (
                    <>
                      <Power className="w-4 h-4" />
                      <span>Desativar Manutenção</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminPage;
