import { useState, useEffect, useCallback, useMemo } from 'react';
import { ChatMessage } from '@/lib/types/chat';
import { ChatSession, ChatSessionMetadata, SessionNavigationState } from '@/lib/types/chatSessions';
import { useAppearance } from '@/contexts/AppearanceContext';
import {
  divideMessagesIntoSessions,
  calculateSessionMetadata,
  getDisplayMessages,
  getLatestSession,
  loadSession,
  loadAllSessions,
  loadPreviousSession
} from '@/lib/utils/chatSessionUtils';

interface UseChatSessionsProps {
  messages: ChatMessage[];
  enabled?: boolean; // Override para forçar ativação/desativação
}

interface UseChatSessionsReturn {
  // Estado das sessões
  sessions: ChatSession[];
  metadata: ChatSessionMetadata;
  navigationState: SessionNavigationState;
  
  // Mensagens para exibição
  displayMessages: ChatMessage[];
  
  // Ações
  loadNextSession: () => void;
  loadAllSessions: () => void;
  resetSessions: () => void;
  
  // Estado de carregamento
  canLoadMore: boolean;
  isAllLoaded: boolean;
}

export const useChatSessions = ({ 
  messages, 
  enabled 
}: UseChatSessionsProps): UseChatSessionsReturn => {
  const { chatSessions } = useAppearance();
  
  // Determinar se as sessões estão ativadas
  const sessionsEnabled = enabled !== undefined ? enabled : chatSessions.enabled;
  const wordsPerSession = chatSessions.wordsPerSession;

  // Estado das sessões
  const [loadedSessions, setLoadedSessions] = useState<Set<number>>(new Set());
  const [allSessionsLoaded, setAllSessionsLoaded] = useState(false);

  // Calcular sessões baseado nas mensagens atuais
  const sessions = useMemo(() => {
    if (!sessionsEnabled || !messages.length) return [];
    return divideMessagesIntoSessions(messages, wordsPerSession);
  }, [messages, wordsPerSession, sessionsEnabled]);

  // Calcular metadados
  const metadata = useMemo(() => {
    return calculateSessionMetadata(messages, wordsPerSession, sessionsEnabled);
  }, [messages, wordsPerSession, sessionsEnabled]);

  // Estado de navegação
  const navigationState = useMemo((): SessionNavigationState => ({
    loadedSessions,
    currentSession: sessions.length > 0 ? sessions.length - 1 : 0,
    allSessionsLoaded
  }), [loadedSessions, sessions.length, allSessionsLoaded]);

  // Mensagens para exibição
  const displayMessages = useMemo(() => {
    if (!sessionsEnabled) return messages;
    return getDisplayMessages(messages, sessions, loadedSessions);
  }, [messages, sessions, loadedSessions, sessionsEnabled]);

  // Inicializar com a última sessão carregada
  useEffect(() => {
    if (sessionsEnabled && sessions.length > 0 && loadedSessions.size === 0) {
      const latestSessionIndex = sessions.length - 1;
      setLoadedSessions(new Set([latestSessionIndex]));
      setAllSessionsLoaded(sessions.length === 1);
    } else if (!sessionsEnabled) {
      setLoadedSessions(new Set());
      setAllSessionsLoaded(true);
    }
  }, [sessions.length, sessionsEnabled, loadedSessions.size]);

  // Atualizar estado quando todas as sessões estão carregadas
  useEffect(() => {
    if (sessions.length > 0 && loadedSessions.size === sessions.length) {
      setAllSessionsLoaded(true);
    } else {
      setAllSessionsLoaded(false);
    }
  }, [loadedSessions.size, sessions.length]);

  // Carregar próxima sessão (mais antiga)
  const loadNextSession = useCallback(() => {
    const previousSessionIndex = loadPreviousSession(loadedSessions);
    if (previousSessionIndex !== null) {
      setLoadedSessions(prev => loadSession(previousSessionIndex, prev));
    }
  }, [loadedSessions]);

  // Carregar todas as sessões
  const loadAllSessionsAction = useCallback(() => {
    if (sessions.length > 0) {
      setLoadedSessions(loadAllSessions(sessions.length));
      setAllSessionsLoaded(true);
    }
  }, [sessions.length]);

  // Resetar sessões (carregar apenas a última)
  const resetSessions = useCallback(() => {
    if (sessions.length > 0) {
      const latestSessionIndex = sessions.length - 1;
      setLoadedSessions(new Set([latestSessionIndex]));
      setAllSessionsLoaded(sessions.length === 1);
    }
  }, [sessions.length]);

  // Verificar se pode carregar mais sessões
  const canLoadMore = useMemo(() => {
    if (!sessionsEnabled || sessions.length <= 1) return false;
    return loadedSessions.size < sessions.length;
  }, [sessionsEnabled, sessions.length, loadedSessions.size]);

  // Verificar se todas as sessões estão carregadas
  const isAllLoaded = useMemo(() => {
    if (!sessionsEnabled) return true;
    return allSessionsLoaded || sessions.length <= 1;
  }, [sessionsEnabled, allSessionsLoaded, sessions.length]);

  return {
    sessions,
    metadata,
    navigationState,
    displayMessages,
    loadNextSession,
    loadAllSessions: loadAllSessionsAction,
    resetSessions,
    canLoadMore,
    isAllLoaded
  };
};
