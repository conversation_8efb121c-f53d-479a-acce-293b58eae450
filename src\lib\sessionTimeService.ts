import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from './firebase';

class SessionTimeServiceImpl {
  private readonly UPDATE_INTERVAL = 60000; // Atualizar no Firestore a cada 1 minuto
  private timers: Map<string, ReturnType<typeof setInterval>> = new Map();
  private sessionData: Map<string, {
    startTime: number;
    totalTime: number;
    lastUpdate: number;
  }> = new Map();

  /**
   * Inicia uma nova sessão para um chat
   */
  async startSession(userId: string, chatId: string): Promise<void> {
    try {
      // Parar sessão anterior se existir
      this.stopSession(chatId);

      // Buscar dados existentes do chat
      const chatRef = doc(db, 'usuarios', userId, 'conversas', chatId);
      const chatDoc = await getDoc(chatRef);
      
      let totalTime = 0;
      if (chatDoc.exists()) {
        const data = chatDoc.data();
        totalTime = data.sessionTime?.totalTime || 0;
      }

      const now = Date.now();
      
      // Armazenar dados da sessão em memória
      this.sessionData.set(chatId, {
        startTime: now,
        totalTime: totalTime,
        lastUpdate: now
      });

      // Atualizar timestamp de início da sessão no Firestore
      await updateDoc(chatRef, {
        'sessionTime.lastSessionStart': now,
        'sessionTime.lastUpdated': now
      });

      // Configurar timer para atualizações periódicas
      const timer = setInterval(() => {
        this.updateSessionTime(userId, chatId);
      }, this.UPDATE_INTERVAL);

      this.timers.set(chatId, timer);
      
      console.log(`✅ Sessão iniciada para chat ${chatId}`);
    } catch (error) {
      console.error('❌ Erro ao iniciar sessão:', error);
    }
  }

  /**
   * Para a sessão de um chat e salva o tempo final
   */
  async stopSession(chatId: string, userId?: string): Promise<number | undefined> {
    const timer = this.timers.get(chatId);
    if (timer) {
      clearInterval(timer);
      this.timers.delete(chatId);
    }

    const sessionData = this.sessionData.get(chatId);
    if (sessionData) {
      // Calcular tempo final da sessão
      const now = Date.now();
      const sessionDuration = now - sessionData.startTime;
      const finalTotalTime = sessionData.totalTime + sessionDuration;

      // Salvar tempo final no Firestore se userId fornecido
      if (userId) {
        try {
          const chatRef = doc(db, 'usuarios', userId, 'conversas', chatId);
          await updateDoc(chatRef, {
            'sessionTime.totalTime': finalTotalTime,
            'sessionTime.lastUpdated': now
          });
          console.log(`💾 Tempo final salvo para chat ${chatId}: ${Math.round(finalTotalTime / 1000)}s`);
        } catch (error) {
          console.error('❌ Erro ao salvar tempo final:', error);
        }
      }

      // Remover da memória
      this.sessionData.delete(chatId);

      console.log(`✅ Sessão finalizada para chat ${chatId}. Duração: ${Math.round(sessionDuration / 1000)}s`);

      return finalTotalTime;
    }
  }

  /**
   * Atualiza o tempo de sessão no Firestore
   */
  private async updateSessionTime(userId: string, chatId: string): Promise<void> {
    try {
      const sessionData = this.sessionData.get(chatId);
      if (!sessionData) return;

      const now = Date.now();
      const sessionDuration = now - sessionData.startTime;
      const totalTime = sessionData.totalTime + sessionDuration;

      const chatRef = doc(db, 'usuarios', userId, 'conversas', chatId);
      await updateDoc(chatRef, {
        'sessionTime.totalTime': totalTime,
        'sessionTime.lastUpdated': now
      });

      // Atualizar dados em memória
      sessionData.lastUpdate = now;

      console.log(`⏱️ Tempo de sessão atualizado para chat ${chatId}: ${Math.round(totalTime / 1000)}s`);
    } catch (error) {
      console.error('❌ Erro ao atualizar tempo de sessão:', error);
    }
  }

  /**
   * Obtém o tempo atual da sessão (em tempo real)
   */
  getCurrentSessionTime(chatId: string): number {
    const sessionData = this.sessionData.get(chatId);
    if (!sessionData) return 0;

    const now = Date.now();
    const sessionDuration = now - sessionData.startTime;
    return sessionData.totalTime + sessionDuration;
  }

  /**
   * Formata o tempo em formato inteligente (SS, MM:SS, HH:MM:SS, Xd HH:MM, Xm Xd HH:MM)
   */
  formatTime(timeInMs: number): string {
    const totalSeconds = Math.floor(timeInMs / 1000);
    const totalMinutes = Math.floor(totalSeconds / 60);
    const totalHours = Math.floor(totalMinutes / 60);
    const totalDays = Math.floor(totalHours / 24);
    const totalMonths = Math.floor(totalDays / 30); // Aproximação de 30 dias por mês

    const seconds = totalSeconds % 60;
    const minutes = totalMinutes % 60;
    const hours = totalHours % 24;
    const days = totalDays % 30;

    // Menos de 1 minuto: apenas segundos
    if (totalMinutes === 0) {
      return `${totalSeconds}s`;
    }

    // Menos de 1 hora: MM:SS
    if (totalHours === 0) {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    // Menos de 1 dia: HH:MM:SS
    if (totalDays === 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Menos de 1 mês: Xd HH:MM
    if (totalMonths === 0) {
      return `${days}d ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }

    // 1 mês ou mais: Xm Xd HH:MM
    return `${totalMonths}m ${days}d ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }

  /**
   * Obtém o tempo total acumulado de um chat do Firestore
   */
  async getTotalTime(userId: string, chatId: string): Promise<number> {
    try {
      const chatRef = doc(db, 'usuarios', userId, 'conversas', chatId);
      const chatDoc = await getDoc(chatRef);
      
      if (chatDoc.exists()) {
        const data = chatDoc.data();
        return data.sessionTime?.totalTime || 0;
      }
      
      return 0;
    } catch (error) {
      console.error('❌ Erro ao obter tempo total:', error);
      return 0;
    }
  }

  /**
   * Limpa todos os timers (para cleanup)
   */
  cleanup(): void {
    this.timers.forEach((timer) => {
      clearInterval(timer);
    });
    this.timers.clear();
    this.sessionData.clear();
  }
}

export const sessionTimeService = new SessionTimeServiceImpl();
