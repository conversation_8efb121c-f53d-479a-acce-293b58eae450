'use client';

interface ConversationEfficiencyProps {
  efficiency: {
    goalAchievementRate: number;
    clarificationRequests: number;
    followUpQuestions: number;
    satisfactionIndicators: number;
  };
  totalMessages: number;
  className?: string;
}

export default function ConversationEfficiencyMeter({ 
  efficiency, 
  totalMessages, 
  className = '' 
}: ConversationEfficiencyProps) {
  
  // Calcular score de eficiência (0-100)
  const calculateEfficiencyScore = () => {
    if (totalMessages === 0) return 0;
    
    const satisfactionRate = efficiency.satisfactionIndicators / totalMessages;
    const clarificationRate = efficiency.clarificationRequests / totalMessages;
    
    // Score baseado em satisfação alta e baixa necessidade de esclarecimentos
    const score = Math.max(0, Math.min(100, 
      (satisfactionRate * 60) + // 60% baseado em satisfação
      ((1 - clarificationRate) * 30) + // 30% baseado em baixa confusão
      (efficiency.goalAchievementRate * 10) // 10% baseado em objetivos
    ));
    
    return Math.round(score);
  };

  const efficiencyScore = calculateEfficiencyScore();
  
  const getEfficiencyColor = (score: number) => {
    if (score >= 80) return { color: 'text-green-400', bg: 'bg-green-500', ring: 'ring-green-500' };
    if (score >= 60) return { color: 'text-yellow-400', bg: 'bg-yellow-500', ring: 'ring-yellow-500' };
    if (score >= 40) return { color: 'text-orange-400', bg: 'bg-orange-500', ring: 'ring-orange-500' };
    return { color: 'text-red-400', bg: 'bg-red-500', ring: 'ring-red-500' };
  };

  const getEfficiencyLabel = (score: number) => {
    if (score >= 80) return 'Excelente';
    if (score >= 60) return 'Boa';
    if (score >= 40) return 'Regular';
    return 'Baixa';
  };

  const colors = getEfficiencyColor(efficiencyScore);
  
  // Calcular ângulo para o medidor circular (0-270 graus)
  const angle = (efficiencyScore / 100) * 270;
  const radius = 45;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (angle / 360) * circumference;

  return (
    <div className={`bg-slate-800/30 rounded-xl p-6 border border-slate-600/20 ${className}`}>
      <h4 className="text-md font-medium text-white mb-6">⚡ Eficiência da Conversa</h4>
      
      {/* Medidor Circular */}
      <div className="flex items-center justify-center mb-6">
        <div className="relative">
          <svg width="120" height="120" className="transform -rotate-90">
            {/* Círculo de fundo */}
            <circle
              cx="60"
              cy="60"
              r={radius}
              stroke="rgba(148, 163, 184, 0.2)"
              strokeWidth="8"
              fill="none"
              strokeLinecap="round"
            />
            
            {/* Círculo de progresso */}
            <circle
              cx="60"
              cy="60"
              r={radius}
              stroke="currentColor"
              strokeWidth="8"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className={colors.color}
              style={{
                transition: 'stroke-dashoffset 1s ease-in-out'
              }}
            />
          </svg>
          
          {/* Texto central */}
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <div className={`text-2xl font-bold ${colors.color}`}>
              {efficiencyScore}%
            </div>
            <div className="text-xs text-slate-400">
              {getEfficiencyLabel(efficiencyScore)}
            </div>
          </div>
        </div>
      </div>

      {/* Métricas detalhadas */}
      <div className="space-y-4">
        {/* Indicadores de Satisfação */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-sm text-slate-300">Satisfação</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400 font-medium">{efficiency.satisfactionIndicators}</span>
            <span className="text-xs text-slate-400">
              ({totalMessages > 0 ? ((efficiency.satisfactionIndicators / totalMessages) * 100).toFixed(1) : 0}%)
            </span>
          </div>
        </div>

        {/* Pedidos de Esclarecimento */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-orange-500"></div>
            <span className="text-sm text-slate-300">Esclarecimentos</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-orange-400 font-medium">{efficiency.clarificationRequests}</span>
            <span className="text-xs text-slate-400">
              ({totalMessages > 0 ? ((efficiency.clarificationRequests / totalMessages) * 100).toFixed(1) : 0}%)
            </span>
          </div>
        </div>

        {/* Perguntas de Follow-up */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-sm text-slate-300">Follow-ups</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-blue-400 font-medium">{efficiency.followUpQuestions}</span>
            <span className="text-xs text-slate-400">perguntas</span>
          </div>
        </div>

        {/* Taxa de Objetivos */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-purple-500"></div>
            <span className="text-sm text-slate-300">Objetivos</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-purple-400 font-medium">
              {(efficiency.goalAchievementRate * 100).toFixed(1)}%
            </span>
            <span className="text-xs text-slate-400">alcançados</span>
          </div>
        </div>
      </div>

      {/* Barra de progresso linear como alternativa */}
      <div className="mt-6">
        <div className="flex justify-between text-xs text-slate-400 mb-2">
          <span>Eficiência Geral</span>
          <span>{efficiencyScore}%</span>
        </div>
        <div className="w-full bg-slate-700/50 rounded-full h-2">
          <div 
            className={`h-2 rounded-full ${colors.bg} transition-all duration-1000 ease-out`}
            style={{ width: `${efficiencyScore}%` }}
          ></div>
        </div>
      </div>

      {/* Dicas baseadas na eficiência */}
      {efficiencyScore < 60 && (
        <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-600/30 rounded-lg">
          <div className="text-xs text-yellow-300">
            💡 <strong>Dica:</strong> {
              efficiency.clarificationRequests > efficiency.satisfactionIndicators 
                ? "Tente ser mais específico nas suas perguntas para reduzir confusões."
                : "Use mais palavras de confirmação como 'obrigado' ou 'perfeito' quando satisfeito."
            }
          </div>
        </div>
      )}
    </div>
  );
}
