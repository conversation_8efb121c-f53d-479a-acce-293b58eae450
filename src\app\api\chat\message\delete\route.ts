import { NextRequest, NextResponse } from 'next/server';
import { chatStorageService } from '@/lib/chatStorageService';

export const dynamic = 'force-dynamic';

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, chatId, messageId } = body;

    // Validar parâmetros obrigatórios
    if (!userId || !chatId || !messageId) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, chatId, messageId' },
        { status: 400 }
      );
    }

    // Obter dados atuais do chat
    const currentData = await chatStorageService.getChatData(userId, chatId);
    
    if (!currentData) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    // Filtrar mensagens para remover a mensagem especificada
    const updatedMessages = currentData.messages.filter(message => message.id !== messageId);

    if (updatedMessages.length === currentData.messages.length) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    // Atualizar dados do chat
    const updatedChatData = {
      ...currentData,
      messages: updatedMessages
    };

    await chatStorageService.updateChatData(userId, chatId, updatedChatData);

    return NextResponse.json({ 
      success: true, 
      message: 'Message deleted successfully' 
    });

  } catch (error) {
    console.error('Error deleting message:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
