// Interfaces para o sistema de mensagens dos chats

export interface AttachmentMetadata {
  id: string;
  type: 'image' | 'pdf';
  filename: string;
  url: string;
  size: number;
  uploadedAt: number;
  base64Data?: string; // Para PDFs, armazenar o base64 aqui
  chatName?: string; // Nome do chat onde o anexo foi enviado
}

export interface TemporaryAttachmentMetadata extends AttachmentMetadata {
  expiresAt: number; // Timestamp de quando o arquivo expira (uploadedAt + 10 horas)
  isTemporary: true; // Flag para identificar anexos temporários
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  attachments?: AttachmentMetadata[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    cost: number;
  };
  responseTime?: number; // tempo de resposta em ms
  typingSpeed?: number; // velocidade de digitação estimada (caracteres por minuto)
}

export interface FavoriteMessage {
  id: string; // ID da mensagem favorita
  messageId: string; // ID da mensagem original
  chatId: string; // ID do chat onde a mensagem está
  chatName: string; // Nome do chat para exibição
  role: 'user' | 'assistant';
  content: string;
  timestamp: number; // timestamp da mensagem original
  favoritedAt: number; // timestamp quando foi favoritada
  attachments?: AttachmentMetadata[];
}

// Interfaces para modelos de IA
export interface AIModel {
  id: string;
  name: string;
  description?: string;
  context_length: number;
  pricing: {
    prompt: string;
    completion: string;
    image?: string;
  };
  architecture?: {
    input_modalities: string[];
    output_modalities: string[];
    tokenizer: string;
  };
  created?: number;
  isFavorite?: boolean;
}

export interface ModelProvider {
  id: string;
  name: string;
  endpoint: string;
  apiKey: string;
  models: AIModel[];
}

// Tipos para filtros de modelos
export type ModelCategory = 'paid' | 'free' | 'favorites';
export type ModelSortBy = 'newest' | 'price_low' | 'price_high' | 'context_high';

export interface ModelFilters {
  category: ModelCategory;
  sortBy: ModelSortBy;
  searchTerm: string;
}

export interface ChatData {
  id: string;
  name: string;
  messages: ChatMessage[];
  context: string;
  system_prompt: string;
  temperature: number;
  maxTokens: number;
  frequency_penalty: number;
  repetition_penalty: number;
  createdAt: number;
  lastUpdatedAt: number;
  chatFolderId?: string;
  lastUsedService?: string;
  lastUsedModel?: string;
  latexInstructions?: boolean;
  password?: string; // senha para proteger o chat
  sessionTime?: {
    totalTime: number; // tempo total acumulado em ms
    lastSessionStart: number; // timestamp do início da sessão atual
    lastUpdated: number; // timestamp da última atualização
  };
}

export interface ChatStorageService {
  createChatFile(userId: string, chatId: string, chatData: Omit<ChatData, 'messages'>): Promise<void>;
  getChatData(userId: string, chatId: string): Promise<ChatData | null>;
  updateChatData(userId: string, chatId: string, chatData: ChatData): Promise<void>;
  addMessage(userId: string, chatId: string, message: MessageInput): Promise<void>;
  deleteChatFile(userId: string, chatId: string): Promise<void>;
}

export interface MessageInput {
  role: 'user' | 'assistant';
  content: string;
  attachments?: AttachmentMetadata[];
}

// Interfaces para estatísticas do chat
export interface ChatStatistics {
  // Contadores básicos
  totalMessages: number;
  userMessages: number;
  assistantMessages: number;
  totalAttachments: number;

  // Contagem de palavras
  totalWords: number;
  userWords: number;
  assistantWords: number;

  // Tokens e custos
  totalPromptTokens: number;
  totalCompletionTokens: number;
  totalTokens: number;
  totalCost: number;

  // Métricas de tempo
  averageResponseTime: number; // em ms
  averageMessageLength: number; // caracteres
  averageUserTypingSpeed: number; // caracteres por minuto
  averageTimeBetweenMessages: number; // em ms

  // Sessões
  totalSessions: number;
  averageSessionDuration: number; // em ms
  averageInactivityPeriod: number; // em ms

  // Análise temporal
  activityHeatmap: {
    hourOfDay: number[]; // 24 elementos (0-23h)
    dayOfWeek: number[]; // 7 elementos (0-6, domingo-sábado)
  };

  // Palavras mais usadas (top 10)
  mostUsedWords: Array<{
    word: string;
    count: number;
  }>;

  // Histórico de performance do modelo
  modelPerformanceHistory: Array<{
    timestamp: number;
    responseTime: number;
    model: string;
  }>;

  // === NOVAS ESTATÍSTICAS ÚNICAS PARA CHATS INDIVIDUAIS ===

  // Estatísticas de edição e regeneração
  messageEdits: {
    totalEdits: number;
    userMessageEdits: number;
    averageEditsPerMessage: number;
    mostEditedMessageLength: number;
  };

  messageRegenerations: {
    totalRegenerations: number;
    averageRegenerationsPerAssistantMessage: number;
    regenerationReasons: Array<{
      reason: string;
      count: number;
    }>;
  };

  // Análise de sentimento das mensagens do usuário
  sentimentAnalysis: {
    positive: number;
    neutral: number;
    negative: number;
    averageSentimentScore: number; // -1 a 1
    sentimentTrend: Array<{
      timestamp: number;
      score: number;
    }>;
  };

  // Padrões de conversa
  conversationPatterns: {
    averageConversationLength: number; // mensagens por conversa
    longestConversationStreak: number;
    shortestResponseTime: number;
    longestResponseTime: number;
    questionsAsked: number;
    codeBlocksShared: number;
    mathExpressionsUsed: number;
  };

  // Complexidade das mensagens
  messageComplexity: {
    averageWordsPerMessage: number;
    averageSentencesPerMessage: number;
    averageSyllablesPerWord: number;
    readabilityScore: number; // Flesch Reading Ease
    technicalTermsUsed: number;
  };

  // Tempo de leitura e interação
  readingMetrics: {
    estimatedReadingTime: number; // tempo total estimado de leitura em ms
    averageTimeToRespond: number; // tempo entre receber resposta e enviar próxima mensagem
    quickResponses: number; // respostas em menos de 30 segundos
    thoughtfulResponses: number; // respostas após mais de 2 minutos
  };

  // Uso de recursos específicos
  featureUsage: {
    attachmentsUsed: {
      images: number;
      pdfs: number;
      totalSizeMB: number;
    };
    latexUsage: number;
    codeLanguages: Array<{
      language: string;
      count: number;
    }>;
    topicsDiscussed: Array<{
      topic: string;
      confidence: number;
      messageCount: number;
    }>;
  };

  // Eficiência da conversa
  conversationEfficiency: {
    goalAchievementRate: number; // baseado em palavras-chave de conclusão
    clarificationRequests: number;
    followUpQuestions: number;
    satisfactionIndicators: number; // "obrigado", "perfeito", etc.
  };

  // Metadados
  firstMessageAt: number;
  lastMessageAt: number;
  lastUpdatedAt: number;
}

export interface SessionData {
  startTime: number;
  endTime?: number;
  messageCount: number;
  duration?: number;
}
