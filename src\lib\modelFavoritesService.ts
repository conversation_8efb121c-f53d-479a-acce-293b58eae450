import { db } from './firebase';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  getDocs,
  deleteDoc,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';

export interface FavoriteModel {
  modelId: string;
  modelName: string;
  endpointId: string;
  addedAt: number;
}

class ModelFavoritesService {
  /**
   * Encode model ID to be safe for Firestore document IDs
   * Replaces characters that are not allowed in Firestore document IDs
   */
  private encodeModelId(modelId: string): string {
    return modelId
      .replace(/\//g, '__SLASH__')
      .replace(/\./g, '__DOT__')
      .replace(/#/g, '__HASH__')
      .replace(/\[/g, '__LBRACKET__')
      .replace(/\]/g, '__RBRACKET__')
      .replace(/\*/g, '__ASTERISK__')
      .replace(/~/g, '__TILDE__');
  }

  /**
   * Decode model ID back to original format
   */
  private decodeModelId(encodedModelId: string): string {
    return encodedModelId
      .replace(/__SLASH__/g, '/')
      .replace(/__DOT__/g, '.')
      .replace(/__HASH__/g, '#')
      .replace(/__LBRACKET__/g, '[')
      .replace(/__RBRACKET__/g, ']')
      .replace(/__ASTERISK__/g, '*')
      .replace(/__TILDE__/g, '~');
  }

  /**
   * Add a model to favorites for a specific endpoint
   */
  async addToFavorites(userId: string, endpointId: string, modelId: string, modelName: string): Promise<void> {
    try {
      const favoriteModel: FavoriteModel = {
        modelId,
        modelName,
        endpointId,
        addedAt: Date.now()
      };

      const encodedModelId = this.encodeModelId(modelId);
      const favoritesRef = doc(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos', encodedModelId);
      await setDoc(favoritesRef, favoriteModel);

      console.log('Model added to favorites successfully');
    } catch (error) {
      console.error('Error adding model to favorites:', error);
      throw error;
    }
  }

  /**
   * Remove a model from favorites
   */
  async removeFromFavorites(userId: string, endpointId: string, modelId: string): Promise<void> {
    try {
      const encodedModelId = this.encodeModelId(modelId);
      const favoritesRef = doc(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos', encodedModelId);
      await deleteDoc(favoritesRef);

      console.log('Model removed from favorites successfully');
    } catch (error) {
      console.error('Error removing model from favorites:', error);
      throw error;
    }
  }

  /**
   * Get all favorite models for a specific endpoint
   */
  async getFavoriteModels(userId: string, endpointId: string): Promise<FavoriteModel[]> {
    try {
      const favoritesCollection = collection(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos');
      const snapshot = await getDocs(favoritesCollection);
      
      const favorites: FavoriteModel[] = [];
      snapshot.forEach(doc => {
        favorites.push(doc.data() as FavoriteModel);
      });

      return favorites.sort((a, b) => b.addedAt - a.addedAt); // Most recent first
    } catch (error) {
      console.error('Error getting favorite models:', error);
      throw error;
    }
  }

  /**
   * Get all favorite models for all endpoints of a user
   */
  async getAllFavoriteModels(userId: string): Promise<{ [endpointId: string]: FavoriteModel[] }> {
    try {
      // First get all endpoints for the user
      const endpointsCollection = collection(db, 'usuarios', userId, 'endpoints');
      const endpointsSnapshot = await getDocs(endpointsCollection);
      
      const allFavorites: { [endpointId: string]: FavoriteModel[] } = {};

      // For each endpoint, get its favorite models
      for (const endpointDoc of endpointsSnapshot.docs) {
        const endpointId = endpointDoc.id;
        const favorites = await this.getFavoriteModels(userId, endpointId);
        allFavorites[endpointId] = favorites;
      }

      return allFavorites;
    } catch (error) {
      console.error('Error getting all favorite models:', error);
      throw error;
    }
  }

  /**
   * Check if a model is favorited for a specific endpoint
   */
  async isModelFavorited(userId: string, endpointId: string, modelId: string): Promise<boolean> {
    try {
      const encodedModelId = this.encodeModelId(modelId);
      const favoritesRef = doc(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos', encodedModelId);
      const docSnapshot = await getDoc(favoritesRef);

      return docSnapshot.exists();
    } catch (error) {
      console.error('Error checking if model is favorited:', error);
      return false;
    }
  }

  /**
   * Toggle favorite status of a model
   */
  async toggleFavorite(userId: string, endpointId: string, modelId: string, modelName: string): Promise<boolean> {
    try {
      const isFavorited = await this.isModelFavorited(userId, endpointId, modelId);
      
      if (isFavorited) {
        await this.removeFromFavorites(userId, endpointId, modelId);
        return false;
      } else {
        await this.addToFavorites(userId, endpointId, modelId, modelName);
        return true;
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      throw error;
    }
  }

  /**
   * Get favorite model IDs for a specific endpoint (for quick lookup)
   */
  async getFavoriteModelIds(userId: string, endpointId: string): Promise<Set<string>> {
    try {
      const favorites = await this.getFavoriteModels(userId, endpointId);
      return new Set(favorites.map(fav => fav.modelId));
    } catch (error) {
      console.error('Error getting favorite model IDs:', error);
      return new Set();
    }
  }

  /**
   * Clear all favorites for a specific endpoint
   */
  async clearEndpointFavorites(userId: string, endpointId: string): Promise<void> {
    try {
      const favoritesCollection = collection(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos');
      const snapshot = await getDocs(favoritesCollection);
      
      const deletePromises = snapshot.docs.map(doc => deleteDoc(doc.ref));
      await Promise.all(deletePromises);
      
      console.log('All favorites cleared for endpoint');
    } catch (error) {
      console.error('Error clearing endpoint favorites:', error);
      throw error;
    }
  }

  /**
   * Get favorite models count for an endpoint
   */
  async getFavoritesCount(userId: string, endpointId: string): Promise<number> {
    try {
      const favorites = await this.getFavoriteModels(userId, endpointId);
      return favorites.length;
    } catch (error) {
      console.error('Error getting favorites count:', error);
      return 0;
    }
  }

  /**
   * Migrate old favorites that might have problematic document IDs
   * This function helps clean up any favorites that were saved before the encoding fix
   */
  async migrateFavorites(userId: string, endpointId: string): Promise<void> {
    try {
      console.log('Starting favorites migration...');

      const favoritesCollection = collection(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos');
      const snapshot = await getDocs(favoritesCollection);

      const migratedCount = 0;

      for (const docSnapshot of snapshot.docs) {
        const docId = docSnapshot.id;
        const data = docSnapshot.data() as FavoriteModel;

        // Check if document ID contains problematic characters
        if (docId.includes('/') || docId.includes('.') || docId.includes('#') ||
            docId.includes('[') || docId.includes(']') || docId.includes('*') ||
            docId.includes('~')) {

          console.log(`Migrating favorite with problematic ID: ${docId}`);

          // Create new document with encoded ID
          const encodedId = this.encodeModelId(data.modelId);
          const newDocRef = doc(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos', encodedId);

          // Save with new encoded ID
          await setDoc(newDocRef, data);

          // Delete old document
          await deleteDoc(docSnapshot.ref);

          console.log(`Migrated: ${docId} -> ${encodedId}`);
        }
      }

      console.log(`Migration completed. ${migratedCount} favorites migrated.`);
    } catch (error) {
      console.error('Error during favorites migration:', error);
      throw error;
    }
  }
}

export const modelFavoritesService = new ModelFavoritesService();
