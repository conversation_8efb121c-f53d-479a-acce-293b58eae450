import { AIModel } from './types/chat';

export interface PriceRange {
  min: number;
  max: number;
}

export interface AdvancedFilters {
  // Filtros de preço
  priceRange?: PriceRange;
  freeOnly?: boolean;
  
  // Filtros de contexto
  minContextLength?: number;
  maxContextLength?: number;
  
  // Filtros de modalidades
  inputModalities?: string[];
  outputModalities?: string[];
  
  // Filtros de provedor
  providers?: string[];
  excludeProviders?: string[];
  
  // Filtros de funcionalidades
  features?: string[];
  excludeFeatures?: string[];
  
  // Filtros de qualidade/performance
  minScore?: number;
  onlyRecent?: boolean; // Apenas modelos dos últimos 6 meses
  
  // Filtros de popularidade
  onlyPopular?: boolean;
  minUsageCount?: number;
}

export interface SmartCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  filters: AdvancedFilters;
  tags: string[];
}

/**
 * Serviço para filtros avançados e categorias inteligentes
 */
export class AdvancedFiltersService {
  
  /**
   * Categorias inteligentes pré-definidas
   */
  private smartCategories: SmartCategory[] = [
    {
      id: 'coding',
      name: 'Modelos de Código',
      description: 'Especializados em programação e desenvolvimento',
      icon: '💻',
      filters: {
        features: ['code', 'programming'],
        minContextLength: 8000
      },
      tags: ['code', 'programming', 'developer', 'coding']
    },
    {
      id: 'multimodal',
      name: 'Modelos Multimodais',
      description: 'Suportam texto, imagem e outros formatos',
      icon: '🎨',
      filters: {
        inputModalities: ['image'],
        features: ['multimodal', 'vision']
      },
      tags: ['multimodal', 'vision', 'image', 'visual']
    },
    {
      id: 'budget',
      name: 'Modelos Econômicos',
      description: 'Modelos pagos com preços baixos',
      icon: '💰',
      filters: {
        priceRange: { min: 0.01, max: 2 }
      },
      tags: ['cheap', 'barato', 'low-cost']
    },
    {
      id: 'free',
      name: 'Modelos Gratuitos',
      description: 'Modelos completamente gratuitos',
      icon: '🆓',
      filters: {
        freeOnly: true
      },
      tags: ['free', 'gratis', 'no-cost']
    },
    {
      id: 'premium',
      name: 'Modelos Premium',
      description: 'Alta qualidade e performance',
      icon: '⭐',
      filters: {
        priceRange: { min: 10, max: 1000 },
        minContextLength: 32000
      },
      tags: ['premium', 'expensive', 'high-quality', 'enterprise']
    },
    {
      id: 'reasoning',
      name: 'Raciocínio e Lógica',
      description: 'Especializados em análise e raciocínio',
      icon: '🧠',
      filters: {
        features: ['reasoning', 'logic', 'math']
      },
      tags: ['reasoning', 'logic', 'analytical', 'math', 'mathematical']
    },
    {
      id: 'creative',
      name: 'Criação de Conteúdo',
      description: 'Ideais para escrita criativa e conteúdo',
      icon: '✍️',
      filters: {
        features: ['creative', 'writing']
      },
      tags: ['creative', 'writing', 'content-creation', 'story']
    },
    {
      id: 'fast',
      name: 'Modelos Rápidos',
      description: 'Otimizados para velocidade',
      icon: '⚡',
      filters: {
        features: ['fast', 'turbo'],
        priceRange: { min: 0, max: 5 }
      },
      tags: ['fast', 'quick', 'turbo', 'speed']
    },
    {
      id: 'long-context',
      name: 'Contexto Longo',
      description: 'Suportam documentos e conversas extensas',
      icon: '📚',
      filters: {
        minContextLength: 100000
      },
      tags: ['long-context', 'large-context', 'extended-context']
    },
    {
      id: 'chat',
      name: 'Assistentes de Chat',
      description: 'Otimizados para conversação',
      icon: '💬',
      filters: {
        features: ['chat', 'conversational']
      },
      tags: ['chat', 'conversational', 'assistant']
    },
    {
      id: 'research',
      name: 'Pesquisa e Análise',
      description: 'Com capacidades de busca na web',
      icon: '🔍',
      filters: {
        features: ['web-search', 'research']
      },
      tags: ['web-search', 'research', 'browsing', 'analysis']
    }
  ];
  
  /**
   * Aplica filtros avançados aos modelos
   */
  applyAdvancedFilters(models: AIModel[], filters: AdvancedFilters): AIModel[] {
    return models.filter(model => {
      // Filtro de preço
      if (filters.freeOnly) {
        const totalPrice = parseFloat(model.pricing.prompt) + parseFloat(model.pricing.completion);
        if (totalPrice > 0) return false;
      }
      
      if (filters.priceRange) {
        const totalPrice = parseFloat(model.pricing.prompt) + parseFloat(model.pricing.completion);
        if (totalPrice < filters.priceRange.min || totalPrice > filters.priceRange.max) {
          return false;
        }
      }
      
      // Filtro de contexto
      if (filters.minContextLength && model.context_length < filters.minContextLength) {
        return false;
      }
      
      if (filters.maxContextLength && model.context_length > filters.maxContextLength) {
        return false;
      }
      
      // Filtro de modalidades de entrada
      if (filters.inputModalities && filters.inputModalities.length > 0) {
        if (!model.architecture?.input_modalities) return false;
        const hasRequiredModalities = filters.inputModalities.every(modality =>
          model.architecture!.input_modalities!.includes(modality)
        );
        if (!hasRequiredModalities) return false;
      }
      
      // Filtro de modalidades de saída
      if (filters.outputModalities && filters.outputModalities.length > 0) {
        if (!model.architecture?.output_modalities) return false;
        const hasRequiredModalities = filters.outputModalities.every(modality =>
          model.architecture!.output_modalities!.includes(modality)
        );
        if (!hasRequiredModalities) return false;
      }
      
      // Filtro de provedores
      if (filters.providers && filters.providers.length > 0) {
        const provider = this.extractProvider(model.id);
        if (!filters.providers.includes(provider)) return false;
      }
      
      if (filters.excludeProviders && filters.excludeProviders.length > 0) {
        const provider = this.extractProvider(model.id);
        if (filters.excludeProviders.includes(provider)) return false;
      }
      
      // Filtro de funcionalidades
      if (filters.features && filters.features.length > 0) {
        const modelTags = this.extractModelTags(model);
        const hasRequiredFeatures = filters.features.some(feature =>
          modelTags.includes(feature)
        );
        if (!hasRequiredFeatures) return false;
      }
      
      if (filters.excludeFeatures && filters.excludeFeatures.length > 0) {
        const modelTags = this.extractModelTags(model);
        const hasExcludedFeatures = filters.excludeFeatures.some(feature =>
          modelTags.includes(feature)
        );
        if (hasExcludedFeatures) return false;
      }
      
      // Filtro de modelos recentes
      if (filters.onlyRecent && model.created) {
        const sixMonthsAgo = Date.now() - (6 * 30 * 24 * 60 * 60 * 1000);
        if (model.created < sixMonthsAgo) return false;
      }
      
      return true;
    });
  }
  
  /**
   * Obtém modelos por categoria inteligente
   */
  getModelsByCategory(models: AIModel[], categoryId: string): AIModel[] {
    const category = this.smartCategories.find(c => c.id === categoryId);
    if (!category) return models;
    
    // Aplicar filtros da categoria
    let filtered = this.applyAdvancedFilters(models, category.filters);
    
    // Aplicar filtro adicional por tags se necessário
    if (category.tags.length > 0) {
      filtered = filtered.filter(model => {
        const modelTags = this.extractModelTags(model);
        const text = `${model.name} ${model.description || ''}`.toLowerCase();
        
        return category.tags.some(tag =>
          modelTags.includes(tag) ||
          text.includes(tag)
        );
      });
    }
    
    return filtered;
  }
  
  /**
   * Obtém todas as categorias inteligentes
   */
  getSmartCategories(): SmartCategory[] {
    return this.smartCategories;
  }
  
  /**
   * Obtém categoria por ID
   */
  getCategoryById(id: string): SmartCategory | undefined {
    return this.smartCategories.find(c => c.id === id);
  }
  
  /**
   * Sugere categorias baseadas em um modelo
   */
  suggestCategoriesForModel(model: AIModel): SmartCategory[] {
    const modelTags = this.extractModelTags(model);
    const text = `${model.name} ${model.description || ''}`.toLowerCase();
    
    return this.smartCategories.filter(category => {
      // Verificar se o modelo se encaixa nos filtros da categoria
      const matchesFilters = this.applyAdvancedFilters([model], category.filters).length > 0;
      
      // Verificar se tem tags em comum
      const hasCommonTags = category.tags.some(tag =>
        modelTags.includes(tag) || text.includes(tag)
      );
      
      return matchesFilters || hasCommonTags;
    });
  }
  
  /**
   * Extrai o provedor do ID do modelo
   */
  private extractProvider(modelId: string): string {
    const parts = modelId.split('/');
    return parts.length > 1 ? parts[0] : '';
  }
  
  /**
   * Extrai tags do modelo (versão simplificada)
   */
  private extractModelTags(model: AIModel): string[] {
    const tags: string[] = [];
    const text = `${model.name} ${model.description || ''}`.toLowerCase();
    
    // Tags básicas para filtros
    if (text.includes('vision') || text.includes('image') || text.includes('multimodal')) {
      tags.push('multimodal', 'vision');
    }
    if (text.includes('code') || text.includes('programming')) {
      tags.push('code', 'programming');
    }
    if (text.includes('chat') || text.includes('conversation')) {
      tags.push('chat', 'conversational');
    }
    if (text.includes('reasoning') || text.includes('logic')) {
      tags.push('reasoning', 'logic');
    }
    if (text.includes('math')) {
      tags.push('math', 'mathematical');
    }
    if (text.includes('creative') || text.includes('writing')) {
      tags.push('creative', 'writing');
    }
    if (text.includes('fast') || text.includes('turbo')) {
      tags.push('fast', 'turbo');
    }
    if (text.includes('search') || text.includes('web')) {
      tags.push('web-search', 'research');
    }
    
    // Tags de preço
    const totalPrice = parseFloat(model.pricing.prompt) + parseFloat(model.pricing.completion);
    if (totalPrice === 0) tags.push('free', 'gratis');
    else if (totalPrice < 2) tags.push('cheap', 'barato');
    else if (totalPrice > 10) tags.push('expensive', 'premium');
    
    // Tags de contexto
    if (model.context_length >= 100000) tags.push('long-context');
    
    return tags;
  }
  
  /**
   * Obtém estatísticas dos filtros aplicados
   */
  getFilterStats(models: AIModel[], filters: AdvancedFilters): {
    totalModels: number;
    filteredModels: number;
    averagePrice: number;
    averageContext: number;
    providerDistribution: Record<string, number>;
  } {
    const filtered = this.applyAdvancedFilters(models, filters);
    
    const totalPrice = filtered.reduce((sum, model) => {
      return sum + parseFloat(model.pricing.prompt) + parseFloat(model.pricing.completion);
    }, 0);
    
    const totalContext = filtered.reduce((sum, model) => sum + model.context_length, 0);
    
    const providerDistribution: Record<string, number> = {};
    filtered.forEach(model => {
      const provider = this.extractProvider(model.id) || 'unknown';
      providerDistribution[provider] = (providerDistribution[provider] || 0) + 1;
    });
    
    return {
      totalModels: models.length,
      filteredModels: filtered.length,
      averagePrice: filtered.length > 0 ? totalPrice / filtered.length : 0,
      averageContext: filtered.length > 0 ? totalContext / filtered.length : 0,
      providerDistribution
    };
  }
}

// Instância singleton
export const advancedFiltersService = new AdvancedFiltersService();
