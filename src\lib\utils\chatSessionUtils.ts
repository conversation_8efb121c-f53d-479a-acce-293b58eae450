import { ChatMessage } from '@/lib/types/chat';
import { ChatSession, ChatSessionMetadata } from '@/lib/types/chatSessions';

/**
 * Conta palavras em um texto
 */
export function countWords(text: string): number {
  if (!text || text.trim().length === 0) return 0;
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

/**
 * Divide mensagens em sessões baseadas no limite de palavras
 * Sempre para em mensagens da IA para manter contexto
 */
export function divideMessagesIntoSessions(
  messages: ChatMessage[], 
  wordsPerSession: number
): ChatSession[] {
  if (!messages.length) return [];

  const sessions: ChatSession[] = [];
  let currentSessionWords = 0;
  let sessionStartIndex = 0;
  let sessionMessages: ChatMessage[] = [];

  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];
    const messageWords = countWords(message.content);
    
    // Adicionar mensagem à sessão atual
    sessionMessages.push(message);
    currentSessionWords += messageWords;

    // Verificar se deve criar nova sessão
    const shouldCreateNewSession = 
      currentSessionWords >= wordsPerSession && 
      message.role === 'assistant' && // Sempre parar em mensagens da IA
      i < messages.length - 1; // Não criar sessão vazia no final

    if (shouldCreateNewSession) {
      // Criar sessão atual
      sessions.push({
        id: `session-${sessions.length}`,
        startIndex: sessionStartIndex,
        endIndex: i,
        wordCount: currentSessionWords,
        messages: [...sessionMessages],
        isLoaded: false
      });

      // Resetar para próxima sessão
      sessionStartIndex = i + 1;
      sessionMessages = [];
      currentSessionWords = 0;
    }
  }

  // Adicionar última sessão se houver mensagens restantes
  if (sessionMessages.length > 0) {
    sessions.push({
      id: `session-${sessions.length}`,
      startIndex: sessionStartIndex,
      endIndex: messages.length - 1,
      wordCount: currentSessionWords,
      messages: [...sessionMessages],
      isLoaded: false
    });
  }

  return sessions;
}

/**
 * Calcula metadados das sessões
 */
export function calculateSessionMetadata(
  messages: ChatMessage[],
  wordsPerSession: number,
  sessionsEnabled: boolean
): ChatSessionMetadata {
  const totalWords = messages.reduce((total, message) => 
    total + countWords(message.content), 0
  );

  if (!sessionsEnabled) {
    return {
      totalSessions: 1,
      currentSessionIndex: 0,
      totalWords,
      wordsPerSession,
      sessionsEnabled: false
    };
  }

  const sessions = divideMessagesIntoSessions(messages, wordsPerSession);
  
  return {
    totalSessions: sessions.length,
    currentSessionIndex: sessions.length - 1, // Última sessão por padrão
    totalWords,
    wordsPerSession,
    sessionsEnabled: true
  };
}

/**
 * Obtém mensagens para exibição baseado nas sessões carregadas
 */
export function getDisplayMessages(
  allMessages: ChatMessage[],
  sessions: ChatSession[],
  loadedSessionIndices: Set<number>
): ChatMessage[] {
  if (!sessions.length) return allMessages;

  const displayMessages: ChatMessage[] = [];
  
  // Ordenar índices das sessões carregadas
  const sortedIndices = Array.from(loadedSessionIndices).sort((a, b) => a - b);
  
  for (const sessionIndex of sortedIndices) {
    if (sessionIndex < sessions.length) {
      displayMessages.push(...sessions[sessionIndex].messages);
    }
  }

  return displayMessages;
}

/**
 * Obtém a sessão mais recente (última)
 */
export function getLatestSession(sessions: ChatSession[]): ChatSession | null {
  return sessions.length > 0 ? sessions[sessions.length - 1] : null;
}

/**
 * Verifica se uma sessão específica está carregada
 */
export function isSessionLoaded(sessionIndex: number, loadedSessions: Set<number>): boolean {
  return loadedSessions.has(sessionIndex);
}

/**
 * Carrega uma sessão específica
 */
export function loadSession(
  sessionIndex: number, 
  loadedSessions: Set<number>
): Set<number> {
  const newLoadedSessions = new Set(loadedSessions);
  newLoadedSessions.add(sessionIndex);
  return newLoadedSessions;
}

/**
 * Carrega todas as sessões
 */
export function loadAllSessions(totalSessions: number): Set<number> {
  const allSessions = new Set<number>();
  for (let i = 0; i < totalSessions; i++) {
    allSessions.add(i);
  }
  return allSessions;
}

/**
 * Carrega a próxima sessão (mais antiga)
 */
export function loadPreviousSession(
  loadedSessions: Set<number>
): number | null {
  if (loadedSessions.size === 0) return null;
  
  const sortedLoaded = Array.from(loadedSessions).sort((a, b) => a - b);
  const earliestLoaded = sortedLoaded[0];
  
  return earliestLoaded > 0 ? earliestLoaded - 1 : null;
}
