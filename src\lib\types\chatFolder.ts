// Interfaces para o sistema de pastas de chats

export interface ChatFolder {
  id: string;
  userId: string;
  name: string;
  description?: string;
  color: string; // Cor da pasta para identificação visual
  isExpanded: boolean; // Estado de expansão/colapso da pasta
  order: number; // Ordem de exibição das pastas
  createdAt: number;
  updatedAt: number;
}

export interface ChatFolderInput {
  name: string;
  description?: string;
  color: string;
  isExpanded?: boolean;
  order?: number;
}

// Tipos para operações de drag and drop
export interface DragDropResult {
  draggableId: string; // ID do chat sendo arrastado
  source: {
    droppableId: string; // ID da pasta/área de origem
    index: number; // Posição original
  };
  destination: {
    droppableId: string; // ID da pasta/área de destino
    index: number; // Nova posição
  } | null;
}

export interface DroppableArea {
  id: string;
  type: 'folder' | 'unorganized'; // Tipo da área onde pode ser solto
  folderId?: string; // ID da pasta (se aplicável)
}

// Cores predefinidas para as pastas
export const FOLDER_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Emerald
  '#F59E0B', // Amber
  '#EF4444', // Red
  '#8B5CF6', // Violet
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#F97316', // Orange
  '#EC4899', // Pink
  '#6B7280', // Gray
] as const;

export type FolderColor = typeof FOLDER_COLORS[number];

// Estados para o contexto de pastas
export interface FolderContextState {
  folders: ChatFolder[];
  loading: boolean;
  error: string | null;
  expandedFolders: Set<string>; // IDs das pastas expandidas
}

// Ações para manipulação de pastas
export interface FolderActions {
  createFolder: (folderData: ChatFolderInput) => Promise<string>;
  updateFolder: (folderId: string, updates: Partial<ChatFolder>) => Promise<void>;
  deleteFolder: (folderId: string) => Promise<void>;
  toggleFolderExpansion: (folderId: string) => void;
  moveConversationToFolder: (conversationId: string, folderId: string | null) => Promise<void>;
  reorderFolders: (folderIds: string[]) => Promise<void>;
  refreshFolders: () => Promise<void>;
}
