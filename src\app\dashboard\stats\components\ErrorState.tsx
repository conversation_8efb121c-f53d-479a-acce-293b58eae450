'use client';

interface ErrorStateProps {
  error: string;
  onRetry: () => void;
}

export default function ErrorState({ error, onRetry }: ErrorStateProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-red-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-orange-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 text-center max-w-md mx-auto p-6">
        {/* Error icon */}
        <div className="mb-8">
          <div className="w-20 h-20 mx-auto bg-red-500/20 rounded-full flex items-center justify-center">
            <svg className="w-10 h-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        </div>

        {/* Error content */}
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">Ops! Algo deu errado</h2>
            <p className="text-red-300 text-sm bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              {error}
            </p>
          </div>

          <div className="text-white/60 text-sm">
            <p>Não foi possível carregar suas estatísticas no momento.</p>
            <p>Verifique sua conexão e tente novamente.</p>
          </div>

          {/* Action buttons */}
          <div className="space-y-3">
            <button
              onClick={onRetry}
              className="w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 hover:scale-105 shadow-lg"
            >
              Tentar Novamente
            </button>
            
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="w-full px-6 py-3 bg-white/10 text-white font-medium rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
            >
              Voltar ao Dashboard
            </button>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute -top-5 -right-5 w-3 h-3 bg-red-500/30 rounded-full animate-ping"></div>
        <div className="absolute -bottom-3 -left-3 w-2 h-2 bg-orange-500/30 rounded-full animate-ping delay-500"></div>
      </div>
    </div>
  );
}
