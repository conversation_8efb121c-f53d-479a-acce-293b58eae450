import { storage } from './firebase';
import { ref, uploadBytes, getDownloadURL, listAll, getMetadata, deleteObject } from 'firebase/storage';
import { AttachmentMetadata } from './types/chat';

export interface FileUploadResult {
  success: boolean;
  attachment?: AttachmentMetadata;
  error?: string;
}

export interface AttachmentWithChat extends AttachmentMetadata {
  chatId: string;
  chatName?: string;
}

export interface AttachmentListResult {
  success: boolean;
  attachments?: AttachmentWithChat[];
  error?: string;
}

class AttachmentService {
  private readonly MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly MAX_PDF_SIZE = 50 * 1024 * 1024; // 50MB
  private readonly SUPPORTED_IMAGE_TYPES = ['image/png', 'image/jpeg', 'image/webp'];
  private readonly SUPPORTED_PDF_TYPES = ['application/pdf'];

  /**
   * Valida se o arquivo é suportado
   */
  private validateFile(file: File): { valid: boolean; error?: string } {
    // Verificar tipo de arquivo
    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);
    const isPdf = this.SUPPORTED_PDF_TYPES.includes(file.type);

    if (!isImage && !isPdf) {
      return {
        valid: false,
        error: 'Tipo de arquivo não suportado. Use PNG, JPEG, WEBP ou PDF.'
      };
    }

    // Verificar tamanho
    const maxSize = isImage ? this.MAX_IMAGE_SIZE : this.MAX_PDF_SIZE;
    if (file.size > maxSize) {
      const maxSizeMB = maxSize / (1024 * 1024);
      return {
        valid: false,
        error: `Arquivo muito grande. Tamanho máximo: ${maxSizeMB}MB`
      };
    }

    return { valid: true };
  }

  /**
   * Gera um ID único para o anexo
   */
  private generateAttachmentId(): string {
    return `attachment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Determina o tipo do anexo baseado no tipo MIME
   */
  private getAttachmentType(mimeType: string): 'image' | 'pdf' {
    return this.SUPPORTED_IMAGE_TYPES.includes(mimeType) ? 'image' : 'pdf';
  }

  /**
   * Faz upload de um arquivo para o Firebase Storage
   */
  async uploadFile(
    userId: string,
    chatId: string,
    file: File,
    chatName?: string
  ): Promise<FileUploadResult> {
    try {
      // Validar arquivo
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // Gerar ID único para o anexo
      const attachmentId = this.generateAttachmentId();
      const fileExtension = file.name.split('.').pop() || '';
      const fileName = `${attachmentId}.${fileExtension}`;

      // Criar referência no Storage
      const attachmentPath = `usuarios/${userId}/conversas/${chatId}/anexos/${fileName}`;
      const attachmentRef = ref(storage, attachmentPath);

      // Upload do arquivo com metadados customizados
      console.log(`Uploading file to: ${attachmentPath}`);
      const metadata = {
        customMetadata: {
          originalName: file.name,
          ...(chatName && { chatName })
        }
      };
      await uploadBytes(attachmentRef, file, metadata);

      // Obter URL de download
      const downloadURL = await getDownloadURL(attachmentRef);

      // Para PDFs, converter para base64 no momento do upload
      let base64Data: string | undefined;
      if (this.getAttachmentType(file.type) === 'pdf') {
        try {
          base64Data = await this.fileToBase64(file);
          console.log('PDF converted to base64 during upload, length:', base64Data.length);
        } catch (error) {
          console.error('Error converting PDF to base64 during upload:', error);
        }
      }

      // Criar metadata do anexo
      const attachment: AttachmentMetadata = {
        id: attachmentId,
        type: this.getAttachmentType(file.type),
        filename: file.name,
        url: downloadURL,
        size: file.size,
        uploadedAt: Date.now(),
        ...(base64Data && { base64Data }),
        ...(chatName && { chatName })
      };

      console.log(`File uploaded successfully:`, attachment);

      return {
        success: true,
        attachment
      };

    } catch (error) {
      console.error('Error uploading file:', error);
      return {
        success: false,
        error: 'Erro ao fazer upload do arquivo. Tente novamente.'
      };
    }
  }

  /**
   * Faz upload de múltiplos arquivos
   */
  async uploadMultipleFiles(
    userId: string,
    chatId: string,
    files: File[],
    chatName?: string
  ): Promise<{
    success: boolean;
    attachments: AttachmentMetadata[];
    errors: string[];
  }> {
    const attachments: AttachmentMetadata[] = [];
    const errors: string[] = [];

    for (const file of files) {
      const result = await this.uploadFile(userId, chatId, file, chatName);

      if (result.success && result.attachment) {
        attachments.push(result.attachment);
      } else {
        errors.push(`${file.name}: ${result.error}`);
      }
    }

    return {
      success: attachments.length > 0,
      attachments,
      errors
    };
  }

  /**
   * Converte arquivo para base64 (usado para PDFs na API)
   */
  async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = () => reject(new Error('Erro ao ler arquivo'));
      reader.readAsDataURL(file);
    });
  }

  /**
   * Valida se um arquivo pode ser anexado
   */
  canAttachFile(file: File): boolean {
    return this.validateFile(file).valid;
  }

  /**
   * Obtém informações sobre limites de arquivo
   */
  getFileLimits() {
    return {
      maxImageSize: this.MAX_IMAGE_SIZE,
      maxPdfSize: this.MAX_PDF_SIZE,
      supportedImageTypes: this.SUPPORTED_IMAGE_TYPES,
      supportedPdfTypes: this.SUPPORTED_PDF_TYPES
    };
  }

  /**
   * Deleta todos os anexos de um chat específico
   */
  async deleteChatAttachments(userId: string, chatId: string): Promise<{
    success: boolean;
    deletedCount: number;
    errors: string[];
  }> {
    try {
      const attachmentsRef = ref(storage, `usuarios/${userId}/conversas/${chatId}/anexos`);

      let deletedCount = 0;
      const errors: string[] = [];

      try {
        // Listar todos os anexos do chat
        const attachmentsList = await listAll(attachmentsRef);

        // Deletar cada anexo individualmente
        for (const attachmentRef of attachmentsList.items) {
          try {
            await deleteObject(attachmentRef);
            deletedCount++;
            console.log(`Deleted attachment: ${attachmentRef.fullPath}`);
          } catch (error) {
            const errorMsg = `Failed to delete ${attachmentRef.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            errors.push(errorMsg);
            console.error(errorMsg);
          }
        }

        console.log(`Successfully deleted ${deletedCount} attachments for chat ${chatId}`);

        return {
          success: true,
          deletedCount,
          errors
        };
      } catch (error) {
        // Se a pasta de anexos não existir, não é um erro
        if ((error as any)?.code === 'storage/object-not-found') {
          console.log(`No attachments folder found for chat ${chatId}`);
          return {
            success: true,
            deletedCount: 0,
            errors: []
          };
        }
        throw error;
      }
    } catch (error) {
      console.error('Error deleting chat attachments:', error);
      return {
        success: false,
        deletedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Lista todos os anexos de um usuário
   */
  async getUserAttachments(userId: string): Promise<AttachmentListResult> {
    try {
      const userConversationsRef = ref(storage, `usuarios/${userId}/conversas`);
      const conversationsList = await listAll(userConversationsRef);

      const allAttachments: AttachmentWithChat[] = [];

      // Para cada conversa, listar os anexos
      for (const conversationRef of conversationsList.prefixes) {
        const chatId = conversationRef.name;
        const attachmentsRef = ref(storage, `${conversationRef.fullPath}/anexos`);

        try {
          const attachmentsList = await listAll(attachmentsRef);

          // Para cada anexo, obter metadados
          for (const attachmentRef of attachmentsList.items) {
            try {
              const metadata = await getMetadata(attachmentRef);
              const downloadURL = await getDownloadURL(attachmentRef);

              // Extrair informações do nome do arquivo
              const filename = metadata.customMetadata?.originalName || attachmentRef.name;
              const fileExtension = filename.split('.').pop()?.toLowerCase() || '';
              const type = this.getAttachmentTypeFromExtension(fileExtension);

              const attachment: AttachmentWithChat = {
                id: attachmentRef.name.split('.')[0], // Remove extensão para obter ID
                type,
                filename,
                url: downloadURL,
                size: metadata.size || 0,
                uploadedAt: new Date(metadata.timeCreated).getTime(),
                chatId,
                chatName: metadata.customMetadata?.chatName || 'Chat sem nome'
              };

              allAttachments.push(attachment);
            } catch (error) {
              console.warn(`Error processing attachment ${attachmentRef.name}:`, error);
            }
          }
        } catch (error) {
          // Pasta de anexos não existe para esta conversa, continuar
          console.log(`No attachments folder for chat ${chatId}`);
        }
      }

      // Ordenar por data de upload (mais recente primeiro)
      allAttachments.sort((a, b) => b.uploadedAt - a.uploadedAt);

      return {
        success: true,
        attachments: allAttachments
      };
    } catch (error) {
      console.error('Error listing user attachments:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Determina o tipo do anexo baseado na extensão do arquivo
   */
  private getAttachmentTypeFromExtension(extension: string): 'image' | 'pdf' {
    const imageExtensions = ['png', 'jpg', 'jpeg', 'webp'];
    return imageExtensions.includes(extension) ? 'image' : 'pdf';
  }
}

// Exportar instância singleton
export const attachmentService = new AttachmentService();
export default attachmentService;
