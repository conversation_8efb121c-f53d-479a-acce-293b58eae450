'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useAppearance } from '@/contexts/AppearanceContext';
import {
  updateUserSettings,
  uploadProfilePhoto,
  deleteProfilePhoto,
  getUserSettings,
  addAPIEndpoint,
  updateAPIEndpoint,
  deleteAPIEndpoint,
  getUserAPIEndpoints,
  setActiveAPIEndpoint,
  updateChatAppearance,
  getChatAppearance,
  updateStreamingSettings,
  getStreamingSettings,
  DEFAULT_ENDPOINTS,
  type APIEndpoint,
  type ChatAppearanceSettings,
  type StreamingSettings,
  type ChatSessionSettings
} from '@/lib/settingsService';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/lib/firebase';
import MemoryManager from './components/MemoryManager';

type TabType = 'geral' | 'aparencia' | 'ia' | 'memorias';

// Fontes disponíveis para o chat
const AVAILABLE_FONTS = [
  { name: 'Inter', label: 'Inter (Padrão)', family: 'Inter, sans-serif' },
  { name: 'Poppins', label: 'Poppins', family: 'Poppins, sans-serif' },
  { name: 'Roboto', label: 'Roboto', family: 'Roboto, sans-serif' },
  { name: 'Open Sans', label: 'Open Sans', family: '"Open Sans", sans-serif' },
  { name: 'Lato', label: 'Lato', family: 'Lato, sans-serif' },
  { name: 'Montserrat', label: 'Montserrat', family: 'Montserrat, sans-serif' },
  { name: 'Source Sans Pro', label: 'Source Sans Pro', family: '"Source Sans Pro", sans-serif' },
  { name: 'Nunito', label: 'Nunito', family: 'Nunito, sans-serif' },
  { name: 'Fira Code', label: 'Fira Code (Monospace)', family: '"Fira Code", monospace' },
  { name: 'JetBrains Mono', label: 'JetBrains Mono', family: '"JetBrains Mono", monospace' }
];

const SettingsPage = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<TabType>('geral');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error'>('success');

  // Estados para formulários
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [profilePhoto, setProfilePhoto] = useState<string | null>(null);

  // Estados para configurações de aparência
  const [chatAppearance, setChatAppearance] = useState<ChatAppearanceSettings>({
    fontSize: 14,
    fontFamily: 'Inter'
  });

  // Estados para configurações de streaming
  const [streamingEnabled, setStreamingEnabled] = useState(false);

  // Estados para configurações de sessão
  const [chatSessions, setChatSessions] = useState<ChatSessionSettings>({
    enabled: false,
    wordsPerSession: 5000
  });

  // Estados para endpoints de API
  const [endpoints, setEndpoints] = useState<APIEndpoint[]>([]);
  const [showAddEndpoint, setShowAddEndpoint] = useState(false);
  const [newEndpoint, setNewEndpoint] = useState({
    name: '',
    endpoint: '',
    apiKey: '',
    defaultModel: ''
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { user, userData } = useAuth();
  const { updateAppearance, updateSessions } = useAppearance();

  // Inicializar dados do usuário
  useEffect(() => {
    if (userData) {
      setUsername(userData.username || '');
      setEmail(userData.email || user?.email || '');
      setProfilePhoto(userData.profilePhotoURL || null);
      setChatAppearance(userData.chatAppearance || {
        fontSize: 14,
        fontFamily: 'Inter'
      });
      setStreamingEnabled(userData.streaming?.enabled || false);
      setChatSessions(userData.chatSessions || {
        enabled: false,
        wordsPerSession: 5000
      });
    }

    // Carregar endpoints
    if (user) {
      loadEndpoints();
    }
  }, [user, userData]);

  const loadEndpoints = async () => {
    if (!user) return;
    try {
      const userEndpoints = await getUserAPIEndpoints(user.uid);
      setEndpoints(userEndpoints);
    } catch (error) {
      console.error('Error loading endpoints:', error);
    }
  };

  const showMessage = (text: string, type: 'success' | 'error') => {
    setMessage(text);
    setMessageType(type);
    setTimeout(() => setMessage(''), 5000);
  };

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    setIsLoading(true);
    try {
      const photoURL = await uploadProfilePhoto(user.uid, file);
      setProfilePhoto(photoURL);
      showMessage('Foto de perfil atualizada com sucesso!', 'success');
    } catch (error) {
      console.error('Error uploading photo:', error);
      showMessage('Erro ao carregar foto. Tente novamente.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Salvar configurações básicas
      await updateUserSettings(user.uid, {
        username: username.trim(),
        email: email.trim(),
        chatAppearance: chatAppearance,
        streaming: { enabled: streamingEnabled },
        chatSessions: chatSessions
      });

      // Atualizar aparência e sessões globalmente
      await updateAppearance(chatAppearance);
      await updateSessions(chatSessions);

      showMessage('Configurações salvas com sucesso!', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      showMessage('Erro ao salvar configurações. Tente novamente.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddEndpoint = async () => {
    if (!user || !newEndpoint.name || !newEndpoint.endpoint || !newEndpoint.apiKey) {
      showMessage('Preencha todos os campos obrigatórios', 'error');
      return;
    }

    setIsLoading(true);
    try {
      await addAPIEndpoint(user.uid, {
        name: newEndpoint.name,
        endpoint: newEndpoint.endpoint,
        apiKey: newEndpoint.apiKey,
        defaultModel: newEndpoint.defaultModel,
        isActive: false // Novos endpoints começam inativos
      });

      setNewEndpoint({ name: '', endpoint: '', apiKey: '', defaultModel: '' });
      setShowAddEndpoint(false);
      await loadEndpoints();
      showMessage('Endpoint adicionado com sucesso!', 'success');
    } catch (error) {
      console.error('Error adding endpoint:', error);
      showMessage('Erro ao adicionar endpoint. Tente novamente.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteEndpoint = async (endpointId: string) => {
    if (!user) return;

    const confirmed = window.confirm('Tem certeza que deseja deletar este endpoint?');
    if (!confirmed) return;

    setIsLoading(true);
    try {
      await deleteAPIEndpoint(user.uid, endpointId);
      await loadEndpoints();
      showMessage('Endpoint removido com sucesso!', 'success');
    } catch (error) {
      console.error('Error deleting endpoint:', error);
      showMessage('Erro ao remover endpoint. Tente novamente.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleEndpoint = async (endpointId: string, currentStatus: boolean) => {
    if (!user) return;

    setIsLoading(true);
    try {
      await setActiveAPIEndpoint(user.uid, endpointId, !currentStatus);
      await loadEndpoints();
      showMessage(`Endpoint ${!currentStatus ? 'ativado' : 'desativado'} com sucesso!`, 'success');
    } catch (error) {
      console.error('Error toggling endpoint:', error);
      showMessage('Erro ao alterar status do endpoint. Tente novamente.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestEndpoint = async (endpoint: APIEndpoint) => {
    setIsLoading(true);
    try {
      const testAIEndpoint = httpsCallable(functions, 'testAIEndpoint');

      const result = await testAIEndpoint({
        endpoint: endpoint.endpoint,
        apiKey: endpoint.apiKey,
        model: endpoint.defaultModel
      });

      const data = result.data as any;

      if (data.success) {
        showMessage(`✅ Endpoint funcionando! Modelo: ${data.model}`, 'success');
      } else {
        showMessage('❌ Endpoint não está funcionando corretamente', 'error');
      }
    } catch (error: any) {
      console.error('Error testing endpoint:', error);
      const errorMessage = error.message || 'Erro ao testar endpoint';
      showMessage(`❌ ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-screen bg-gradient-to-br from-blue-950 via-blue-900 to-blue-950 flex overflow-hidden">
      {/* Sidebar */}
      <div className="w-full sm:w-80 bg-blue-900/30 backdrop-blur-xl border-r border-blue-700/30 flex flex-col">
        {/* Header da Sidebar */}
        <div className="p-4 sm:p-6 border-b border-blue-700/30">
          <div className="flex items-center gap-2 sm:gap-3 mb-2">
            <button
              onClick={() => router.push('/dashboard')}
              className="p-1.5 sm:p-2 rounded-lg bg-blue-700/50 hover:bg-blue-600/70 transition-all duration-200 text-blue-300 hover:text-white"
              title="Voltar ao Dashboard"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-white">Configurações</h1>
              <p className="text-blue-300/70 text-xs sm:text-sm">Gerencie suas preferências</p>
            </div>
          </div>
        </div>

        {/* Menu da Sidebar */}
        <div className="flex-1 p-3 sm:p-4 overflow-y-auto custom-scrollbar">
          <nav className="space-y-1.5 sm:space-y-2">
            <button
              onClick={() => setActiveTab('geral')}
              className={`w-full text-left px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-all duration-200 flex items-center gap-2 sm:gap-3 ${
                activeTab === 'geral'
                  ? 'bg-blue-600/30 text-blue-200 border border-blue-500/30'
                  : 'text-blue-300/70 hover:text-blue-200 hover:bg-blue-800/20'
              }`}
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <div>
                <div className="font-medium text-sm sm:text-base">Geral</div>
                <div className="text-xs opacity-70 hidden sm:block">Perfil e conta</div>
              </div>
            </button>

            <button
              onClick={() => setActiveTab('aparencia')}
              className={`w-full text-left px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-all duration-200 flex items-center gap-2 sm:gap-3 ${
                activeTab === 'aparencia'
                  ? 'bg-blue-600/30 text-blue-200 border border-blue-500/30'
                  : 'text-blue-300/70 hover:text-blue-200 hover:bg-blue-800/20'
              }`}
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a4 4 0 004-4V5z" />
              </svg>
              <div>
                <div className="font-medium">Aparência</div>
                <div className="text-xs opacity-70 hidden sm:block">Chat e streaming</div>
              </div>
            </button>

            <button
              onClick={() => setActiveTab('ia')}
              className={`w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center gap-3 ${
                activeTab === 'ia'
                  ? 'bg-blue-600/30 text-blue-200 border border-blue-500/30'
                  : 'text-blue-300/70 hover:text-blue-200 hover:bg-blue-800/20'
              }`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <div>
                <div className="font-medium">Inteligência Artificial</div>
                <div className="text-xs opacity-70">APIs e modelos</div>
              </div>
            </button>

            <button
              onClick={() => setActiveTab('memorias')}
              className={`w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center gap-3 ${
                activeTab === 'memorias'
                  ? 'bg-blue-600/30 text-blue-200 border border-blue-500/30'
                  : 'text-blue-300/70 hover:text-blue-200 hover:bg-blue-800/20'
              }`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <div>
                <div className="font-medium">Memórias</div>
                <div className="text-xs opacity-70">Contexto e lembretes</div>
              </div>
            </button>
          </nav>
        </div>
      </div>

      {/* Conteúdo Principal */}
      <div className="flex-1 flex flex-col h-screen">
        {/* Header do Conteúdo */}
        <div className="p-4 sm:p-6 border-b border-blue-700/30 bg-blue-900/20 flex-shrink-0">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0">
            <div>
              <h2 className="text-lg sm:text-xl font-semibold text-white">
                {activeTab === 'geral' ? 'Configurações Gerais' :
                 activeTab === 'aparencia' ? 'Configurações de Aparência' :
                 activeTab === 'ia' ? 'Configurações de IA' : 'Configurações de Memórias'}
              </h2>
              <p className="text-blue-300/70 text-xs sm:text-sm mt-1">
                {activeTab === 'geral'
                  ? 'Gerencie suas informações pessoais e configurações da conta'
                  : activeTab === 'aparencia'
                  ? 'Personalize a aparência do chat, configurações de streaming e sessões'
                  : activeTab === 'ia'
                  ? 'Configure seus endpoints de API para usar diferentes provedores de IA (BYOK)'
                  : 'Gerencie suas memórias e contextos para personalizar as respostas da IA'
                }
              </p>
            </div>
            {activeTab === 'ia' && (
              <button
                onClick={() => setShowAddEndpoint(true)}
                className="bg-blue-600 hover:bg-blue-500 text-white px-3 sm:px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 text-sm sm:text-base"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span className="hidden sm:inline">Adicionar Endpoint</span>
                <span className="sm:hidden">Adicionar</span>
              </button>
            )}
          </div>

          {/* Mensagem de feedback */}
          {message && (
            <div className={`mt-3 sm:mt-4 p-3 sm:p-4 rounded-lg border text-sm sm:text-base ${
              messageType === 'success'
                ? 'bg-green-900/20 border-green-700/30 text-green-300'
                : 'bg-red-900/20 border-red-700/30 text-red-300'
            }`}>
              {message}
            </div>
          )}
        </div>

        {/* Conteúdo Scrollável */}
        <div className="flex-1 overflow-y-auto p-3 sm:p-6 pb-16 sm:pb-20 custom-scrollbar" style={{ maxHeight: 'calc(100vh - 200px)' }}>
          {activeTab === 'geral' && (
            <div className="space-y-6 max-w-6xl">
                
                {/* Foto de Perfil */}
                <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-blue-300 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Foto de Perfil
                  </h3>
                  <div className="flex items-center gap-6">
                    <div className="relative">
                      {profilePhoto ? (
                        <img
                          src={profilePhoto}
                          alt="Foto de perfil"
                          className="w-20 h-20 rounded-full object-cover shadow-lg"
                        />
                      ) : (
                        <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center shadow-lg">
                          <span className="text-white font-bold text-2xl">
                            {username?.charAt(0).toUpperCase() || user?.email?.charAt(0).toUpperCase() || '?'}
                          </span>
                        </div>
                      )}
                      {isLoading && (
                        <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                          <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      )}
                    </div>
                    <div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handlePhotoUpload}
                        className="hidden"
                      />
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isLoading}
                        className="bg-blue-600 hover:bg-blue-500 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        {isLoading ? 'Carregando...' : 'Carregar Foto'}
                      </button>
                      <p className="text-xs text-blue-400/70 mt-2">JPG, PNG ou GIF. Máximo 5MB.</p>
                    </div>
                  </div>
                </div>



                {/* Informações da Conta */}
                <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-blue-300 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    Informações da Conta
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-300 mb-2">Nome de Usuário</label>
                      <input
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="w-full bg-blue-900/30 border border-blue-700/50 rounded-lg px-4 py-3 text-blue-100 placeholder:text-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-blue-300 mb-2">Email</label>
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full bg-blue-900/30 border border-blue-700/50 rounded-lg px-4 py-3 text-blue-100 placeholder:text-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                      />
                    </div>
                  </div>
                </div>

                {/* Alterar Senha */}
                <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-blue-300 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    Alterar Senha
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-300 mb-2">Senha Atual</label>
                      <input
                        type="password"
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        className="w-full bg-blue-900/30 border border-blue-700/50 rounded-lg px-4 py-3 text-blue-100 placeholder:text-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-blue-300 mb-2">Nova Senha</label>
                      <input
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        className="w-full bg-blue-900/30 border border-blue-700/50 rounded-lg px-4 py-3 text-blue-100 placeholder:text-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                      />
                    </div>
                  </div>
                </div>



                {/* Botões de Ação */}
                <div className="flex justify-end gap-4">
                  <button
                    onClick={() => {
                      setUsername(userData?.username || '');
                      setEmail(userData?.email || user?.email || '');
                      setCurrentPassword('');
                      setNewPassword('');
                      setChatAppearance(userData?.chatAppearance || {
                        fontSize: 14,
                        fontFamily: 'Inter'
                      });
                      setStreamingEnabled(userData?.streaming?.enabled || false);
                      setChatSessions(userData?.chatSessions || {
                        enabled: false,
                        wordsPerSession: 5000
                      });
                    }}
                    className="px-6 py-3 text-blue-300 hover:text-white transition-colors duration-200"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleSaveSettings}
                    disabled={isLoading}
                    className="px-6 py-3 bg-blue-600 hover:bg-blue-500 disabled:opacity-50 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
                  >
                    {isLoading && (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    )}
                    {isLoading ? 'Salvando...' : 'Salvar Alterações'}
                  </button>
                </div>
            </div>
          )}

          {activeTab === 'aparencia' && (
            <div className="space-y-6 max-w-6xl">
              {/* Configurações de Aparência do Chat */}
              <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a4 4 0 004-4V5z" />
                  </svg>
                  <h3 className="text-lg font-medium text-blue-300">Aparência do Chat</h3>
                </div>
                <p className="text-blue-400/70 text-sm mb-6">Personalize a fonte e tamanho do texto nas mensagens do chat</p>

                <div className="space-y-6">
                  {/* Seletor de Fonte */}
                  <div>
                    <label className="block text-sm font-medium text-blue-300 mb-2">
                      Fonte do Chat *
                    </label>
                    <select
                      value={chatAppearance.fontFamily}
                      onChange={(e) => setChatAppearance({...chatAppearance, fontFamily: e.target.value})}
                      className="w-full bg-blue-900/30 border border-blue-700/50 rounded-lg px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                    >
                      {AVAILABLE_FONTS.map((font) => (
                        <option key={font.name} value={font.name} style={{ fontFamily: font.family }}>
                          {font.label}
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-blue-400/60 mt-1">Escolha a fonte que será usada nas mensagens do chat</p>
                  </div>

                  {/* Controle de Tamanho da Fonte */}
                  <div>
                    <label className="block text-sm font-medium text-blue-300 mb-2">
                      Tamanho da Fonte: {chatAppearance.fontSize}px
                    </label>
                    <div className="space-y-3">
                      <input
                        type="range"
                        min="10"
                        max="24"
                        step="1"
                        value={chatAppearance.fontSize}
                        onChange={(e) => setChatAppearance({...chatAppearance, fontSize: parseInt(e.target.value)})}
                        className="w-full h-2 bg-blue-900/50 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <div className="flex justify-between text-xs text-blue-400/60">
                        <span>Pequeno (10px)</span>
                        <span>Médio (14px)</span>
                        <span>Grande (24px)</span>
                      </div>
                    </div>
                    <p className="text-xs text-blue-400/60 mt-1">Ajuste o tamanho do texto nas mensagens</p>
                  </div>
                </div>
              </div>

              {/* Preview da Aparência */}
              <div className="bg-blue-950/50 border border-blue-600/30 rounded-lg p-6">
                <h4 className="text-lg font-medium text-blue-300 mb-4 flex items-center gap-2">
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  Prévia das Mensagens
                </h4>
                <p className="text-blue-400/70 text-sm mb-4">Veja como ficará a aparência das mensagens com suas configurações</p>
                <div className="space-y-4">
                  {/* Mensagem do usuário */}
                  <div className="flex justify-end">
                    <div
                      className="bg-gradient-to-br from-green-900/80 to-emerald-900/80 border border-green-600/30 rounded-2xl px-4 py-3 max-w-xs shadow-lg"
                      style={{
                        fontSize: `${chatAppearance.fontSize}px`,
                        fontFamily: AVAILABLE_FONTS.find(f => f.name === chatAppearance.fontFamily)?.family || 'Inter, sans-serif'
                      }}
                    >
                      <p className="text-green-100">Esta é uma mensagem de exemplo do usuário</p>
                    </div>
                  </div>
                  {/* Mensagem da IA */}
                  <div className="flex justify-start">
                    <div
                      className="bg-gradient-to-br from-blue-900/80 to-blue-800/80 border border-blue-600/30 rounded-2xl px-4 py-3 max-w-xs shadow-lg"
                      style={{
                        fontSize: `${chatAppearance.fontSize}px`,
                        fontFamily: AVAILABLE_FONTS.find(f => f.name === chatAppearance.fontFamily)?.family || 'Inter, sans-serif'
                      }}
                    >
                      <p className="text-blue-100">Esta é uma resposta de exemplo da IA com a fonte e tamanho selecionados</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Configurações de Streaming */}
              <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <h3 className="text-lg font-medium text-blue-300">Configurações de Streaming</h3>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-blue-300 mb-1">
                        Streaming de Mensagens
                      </label>
                      <p className="text-xs text-blue-400/70">
                        Quando ativado, as respostas da IA aparecerão palavra por palavra em tempo real.
                        Você pode cancelar a geração a qualquer momento.
                      </p>
                    </div>
                    <div className="ml-4">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={streamingEnabled}
                          onChange={(e) => setStreamingEnabled(e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-blue-900/50 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-500/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>

                  {streamingEnabled && (
                    <div className="bg-blue-800/20 border border-blue-600/30 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <svg className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                          <p className="text-sm text-blue-300 font-medium">Streaming Ativado</p>
                          <p className="text-xs text-blue-400/70 mt-1">
                            As mensagens aparecerão em tempo real. Durante a geração, você verá um botão "Parar"
                            para cancelar a resposta e manter apenas o que já foi gerado.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Configurações de Sessões de Chat */}
              <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <h3 className="text-lg font-medium text-blue-300">Sessões de Chat</h3>
                </div>
                <p className="text-blue-400/70 text-sm mb-6">Configure a paginação de chats grandes para melhor performance</p>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-blue-300 mb-1">Ativar Sessões</h4>
                      <p className="text-xs text-blue-400/70">
                        Divide chats grandes em sessões menores para evitar lag
                      </p>
                    </div>
                    <div className="flex-shrink-0 ml-4">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={chatSessions.enabled}
                          onChange={(e) => setChatSessions(prev => ({ ...prev, enabled: e.target.checked }))}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-blue-800/50 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>

                  {chatSessions.enabled && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-300 mb-2">
                          Palavras por Sessão: {chatSessions.wordsPerSession.toLocaleString()}
                        </label>
                        <input
                          type="range"
                          min="1000"
                          max="20000"
                          step="500"
                          value={chatSessions.wordsPerSession}
                          onChange={(e) => setChatSessions(prev => ({ ...prev, wordsPerSession: parseInt(e.target.value) }))}
                          className="w-full h-2 bg-blue-800/50 rounded-lg appearance-none cursor-pointer slider"
                        />
                        <div className="flex justify-between text-xs text-blue-400/70 mt-1">
                          <span>1.000</span>
                          <span>20.000</span>
                        </div>
                      </div>

                      <div className="bg-blue-800/20 border border-blue-600/30 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                          <svg className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <p className="text-sm text-blue-300 font-medium">Como Funciona</p>
                            <p className="text-xs text-blue-400/70 mt-1">
                              Chats grandes são divididos em sessões baseadas no número de palavras.
                              Você pode navegar entre sessões e carregar mais conforme necessário.
                              As divisões sempre param em mensagens da IA para manter o contexto.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Botões de Ação */}
              <div className="flex justify-end gap-4">
                <button
                  onClick={() => {
                    setChatAppearance(userData?.chatAppearance || {
                      fontSize: 14,
                      fontFamily: 'Inter'
                    });
                    setStreamingEnabled(userData?.streaming?.enabled || false);
                    setChatSessions(userData?.chatSessions || {
                      enabled: false,
                      wordsPerSession: 5000
                    });
                  }}
                  className="px-6 py-3 text-blue-300 hover:text-white transition-colors duration-200"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSaveSettings}
                  disabled={isLoading}
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-500 disabled:opacity-50 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
                >
                  {isLoading && (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  )}
                  {isLoading ? 'Salvando...' : 'Salvar Alterações'}
                </button>
              </div>
            </div>
          )}

          {activeTab === 'ia' && (
            <div className="space-y-6 custom-scrollbar">
                {/* Lista de Endpoints */}
                <div className="space-y-4 max-h-96 overflow-y-auto custom-scrollbar">
                  {endpoints.map((endpoint) => (
                    <div key={endpoint.id} className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-6">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-medium text-blue-300">{endpoint.name}</h3>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              endpoint.isActive
                                ? 'bg-green-600 text-white'
                                : 'bg-gray-600 text-gray-300'
                            }`}>
                              {endpoint.isActive ? 'Ativo' : 'Inativo'}
                            </span>
                          </div>
                          <p className="text-blue-400/70 text-sm mb-2">{endpoint.endpoint}</p>
                          <p className="text-blue-400/70 text-sm">Modelo padrão: {endpoint.defaultModel || 'Não definido'}</p>
                        </div>
                        <div className="flex items-center gap-4">
                          {/* Toggle Switch */}
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-blue-300">Ativo</span>
                            <button
                              onClick={() => handleToggleEndpoint(endpoint.id, endpoint.isActive)}
                              disabled={isLoading}
                              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                                endpoint.isActive ? 'bg-green-600' : 'bg-gray-600'
                              }`}
                            >
                              <span
                                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                  endpoint.isActive ? 'translate-x-6' : 'translate-x-1'
                                }`}
                              />
                            </button>
                          </div>

                          <div className="flex gap-2">
                            <button
                              onClick={() => handleTestEndpoint(endpoint)}
                              disabled={isLoading}
                              className="text-green-400 hover:text-green-300 disabled:opacity-50 p-2"
                              title="Testar endpoint"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </button>
                            <button
                              onClick={() => handleDeleteEndpoint(endpoint.id)}
                              disabled={isLoading}
                              className="text-red-400 hover:text-red-300 disabled:opacity-50 p-2"
                              title="Deletar"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {endpoints.length === 0 && (
                    <div className="text-center py-12 text-blue-400/70">
                      <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      <p>Nenhum endpoint configurado</p>
                      <p className="text-sm mt-1">Adicione um endpoint para começar a usar a IA</p>
                    </div>
                  )}
                </div>

                {/* Modal Adicionar Endpoint */}
                {showAddEndpoint && (
                  <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
                    <div className="bg-blue-900/95 border border-blue-700/50 rounded-xl shadow-2xl p-6 w-full max-w-md">
                      <h3 className="text-lg font-semibold text-white mb-4">Adicionar Endpoint</h3>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-blue-300 mb-2">Nome do Endpoint</label>
                          <input
                            type="text"
                            value={newEndpoint.name}
                            onChange={(e) => setNewEndpoint({...newEndpoint, name: e.target.value})}
                            className="w-full bg-blue-900/30 border border-blue-700/50 rounded-lg px-4 py-3 text-blue-100"
                            placeholder="Ex: OpenRouter"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-blue-300 mb-2">URL do Endpoint</label>
                          <input
                            type="url"
                            value={newEndpoint.endpoint}
                            onChange={(e) => setNewEndpoint({...newEndpoint, endpoint: e.target.value})}
                            className="w-full bg-blue-900/30 border border-blue-700/50 rounded-lg px-4 py-3 text-blue-100"
                            placeholder="https://api.exemplo.com/v1"
                          />

                          {/* Endpoints Padrão */}
                          <div className="mt-2">
                            <p className="text-xs text-blue-400/70 mb-2">Endpoints padrão:</p>
                            <div className="grid grid-cols-1 gap-1">
                              {DEFAULT_ENDPOINTS.map((template) => (
                                <button
                                  key={template.name}
                                  onClick={() => setNewEndpoint({
                                    ...newEndpoint,
                                    name: template.name,
                                    endpoint: template.endpoint
                                  })}
                                  className="text-left text-xs text-blue-400 hover:text-blue-300 p-2 rounded hover:bg-blue-800/30"
                                >
                                  <span className="font-medium">{template.name}</span>
                                  <br />
                                  <span className="opacity-70">{template.endpoint}</span>
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-blue-300 mb-2">Chave da API</label>
                          <input
                            type="password"
                            value={newEndpoint.apiKey}
                            onChange={(e) => setNewEndpoint({...newEndpoint, apiKey: e.target.value})}
                            className="w-full bg-blue-900/30 border border-blue-700/50 rounded-lg px-4 py-3 text-blue-100"
                            placeholder="sk-..."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-blue-300 mb-2">Modelo Padrão (Opcional)</label>
                          <input
                            type="text"
                            value={newEndpoint.defaultModel}
                            onChange={(e) => setNewEndpoint({...newEndpoint, defaultModel: e.target.value})}
                            className="w-full bg-blue-900/30 border border-blue-700/50 rounded-lg px-4 py-3 text-blue-100"
                            placeholder="google/gemini-2.5-flash, gpt-4, claude-3, etc."
                          />
                        </div>
                      </div>

                      <div className="flex justify-end gap-4 mt-6">
                        <button
                          onClick={() => {
                            setShowAddEndpoint(false);
                            setNewEndpoint({ name: '', endpoint: '', apiKey: '', defaultModel: '' });
                          }}
                          className="px-4 py-2 text-blue-300 hover:text-white transition-colors"
                        >
                          Cancelar
                        </button>
                        <button
                          onClick={handleAddEndpoint}
                          disabled={isLoading}
                          className="px-4 py-2 bg-blue-600 hover:bg-blue-500 disabled:opacity-50 text-white rounded-lg transition-colors"
                        >
                          {isLoading ? 'Adicionando...' : 'Adicionar'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}
            </div>
          )}

          {activeTab === 'memorias' && (
            <div className="space-y-6">
              <MemoryManager />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
