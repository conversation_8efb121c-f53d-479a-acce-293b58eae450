'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { getChatAppearance, updateChatAppearance, getChatSessions, updateChatSessions, type ChatAppearanceSettings, type ChatSessionSettings } from '@/lib/settingsService';

interface AppearanceContextType {
  chatAppearance: ChatAppearanceSettings;
  chatSessions: ChatSessionSettings;
  updateAppearance: (settings: ChatAppearanceSettings) => Promise<void>;
  updateSessions: (settings: ChatSessionSettings) => Promise<void>;
  loading: boolean;
}

const AppearanceContext = createContext<AppearanceContextType>({
  chatAppearance: {
    fontSize: 14,
    fontFamily: 'Inter'
  },
  chatSessions: {
    enabled: false,
    wordsPerSession: 5000
  },
  updateAppearance: async () => {},
  updateSessions: async () => {},
  loading: true
});

export const useAppearance = () => useContext(AppearanceContext);

interface AppearanceProviderProps {
  children: ReactNode;
}

export const AppearanceProvider: React.FC<AppearanceProviderProps> = ({ children }) => {
  const [chatAppearance, setChatAppearance] = useState<ChatAppearanceSettings>({
    fontSize: 14,
    fontFamily: 'Inter'
  });
  const [chatSessions, setChatSessions] = useState<ChatSessionSettings>({
    enabled: false,
    wordsPerSession: 5000
  });
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  // Carregar configurações de aparência quando o usuário estiver disponível
  useEffect(() => {
    const loadAppearanceSettings = async () => {
      if (user) {
        try {
          const [appearanceSettings, sessionSettings] = await Promise.all([
            getChatAppearance(user.uid),
            getChatSessions(user.uid)
          ]);
          setChatAppearance(appearanceSettings);
          setChatSessions(sessionSettings);
        } catch (error) {
          console.error('Error loading appearance settings:', error);
        }
      }
      setLoading(false);
    };

    loadAppearanceSettings();
  }, [user]);

  // Aplicar configurações de aparência ao documento
  useEffect(() => {
    if (!loading) {
      // Aplicar variáveis CSS customizadas
      document.documentElement.style.setProperty('--chat-font-size', `${chatAppearance.fontSize}px`);
      document.documentElement.style.setProperty('--chat-font-family', getFontFamily(chatAppearance.fontFamily));
    }
  }, [chatAppearance, loading]);

  const getFontFamily = (fontName: string): string => {
    const fontMap: Record<string, string> = {
      'Inter': 'Inter, sans-serif',
      'Poppins': 'Poppins, sans-serif',
      'Roboto': 'Roboto, sans-serif',
      'Open Sans': '"Open Sans", sans-serif',
      'Lato': 'Lato, sans-serif',
      'Montserrat': 'Montserrat, sans-serif',
      'Source Sans Pro': '"Source Sans Pro", sans-serif',
      'Nunito': 'Nunito, sans-serif',
      'Fira Code': '"Fira Code", monospace',
      'JetBrains Mono': '"JetBrains Mono", monospace'
    };
    return fontMap[fontName] || 'Inter, sans-serif';
  };

  const updateAppearance = async (settings: ChatAppearanceSettings) => {
    if (!user) return;

    try {
      await updateChatAppearance(user.uid, settings);
      setChatAppearance(settings);
    } catch (error) {
      console.error('Error updating appearance settings:', error);
      throw error;
    }
  };

  const updateSessions = async (settings: ChatSessionSettings) => {
    if (!user) return;

    try {
      await updateChatSessions(user.uid, settings);
      setChatSessions(settings);
    } catch (error) {
      console.error('Error updating session settings:', error);
      throw error;
    }
  };

  const value = {
    chatAppearance,
    chatSessions,
    updateAppearance,
    updateSessions,
    loading
  };

  return (
    <AppearanceContext.Provider value={value}>
      {children}
    </AppearanceContext.Provider>
  );
};
