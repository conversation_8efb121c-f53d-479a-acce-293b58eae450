'use client';

import { useState } from 'react';
import { AttachmentWithChat } from '@/lib/attachmentService';

interface AttachmentCardProps {
  attachment: AttachmentWithChat;
  onView: (attachment: AttachmentWithChat) => void;
  onDownload: (attachment: AttachmentWithChat) => void;
  onGoToChat: (chatId: string) => void;
}

export default function AttachmentCard({ 
  attachment, 
  onView, 
  onDownload, 
  onGoToChat 
}: AttachmentCardProps) {
  const [imageError, setImageError] = useState(false);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFileIcon = (type: 'image' | 'pdf') => {
    if (type === 'image') {
      return (
        <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    }
  };

  return (
    <div className="bg-blue-900/30 border border-blue-700/30 rounded-xl overflow-hidden backdrop-blur-sm hover:bg-blue-800/40 hover:border-blue-500/40 transition-all duration-200 group">
      {/* Preview */}
      <div className="aspect-video bg-blue-800/50 flex items-center justify-center relative overflow-hidden">
        {attachment.type === 'image' && !imageError ? (
          <img
            src={attachment.url}
            alt={attachment.filename}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="flex flex-col items-center justify-center text-center p-4">
            {getFileIcon(attachment.type)}
            <span className="text-xs text-blue-300/70 mt-2 font-medium">
              {attachment.type.toUpperCase()}
            </span>
          </div>
        )}
        
        {/* Overlay com ações */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
          <button
            onClick={() => onView(attachment)}
            className="p-2 bg-blue-600/80 hover:bg-blue-500 text-white rounded-lg transition-colors"
            title="Visualizar"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
          <button
            onClick={() => onDownload(attachment)}
            className="p-2 bg-green-600/80 hover:bg-green-500 text-white rounded-lg transition-colors"
            title="Download"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Informações */}
      <div className="p-4">
        {/* Nome do arquivo */}
        <h3 className="font-medium text-white truncate mb-2" title={attachment.filename}>
          {attachment.filename}
        </h3>

        {/* Metadados */}
        <div className="space-y-2 text-sm text-blue-300/70">
          <div className="flex items-center justify-between">
            <span>Tamanho:</span>
            <span className="text-blue-200">{formatFileSize(attachment.size)}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span>Data:</span>
            <span className="text-blue-200">{formatDate(attachment.uploadedAt)}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span>Chat:</span>
            <button
              onClick={() => onGoToChat(attachment.chatId)}
              className="text-blue-400 hover:text-blue-300 transition-colors truncate max-w-24"
              title={attachment.chatName}
            >
              {attachment.chatName}
            </button>
          </div>
        </div>

        {/* Ações */}
        <div className="flex gap-2 mt-4">
          <button
            onClick={() => onView(attachment)}
            className="flex-1 px-3 py-2 bg-blue-600/50 hover:bg-blue-600/70 text-blue-200 hover:text-white rounded-lg transition-all duration-200 text-sm font-medium"
          >
            Visualizar
          </button>
          <button
            onClick={() => onDownload(attachment)}
            className="px-3 py-2 bg-blue-700/50 hover:bg-blue-600/70 text-blue-300 hover:text-white rounded-lg transition-all duration-200"
            title="Download"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
