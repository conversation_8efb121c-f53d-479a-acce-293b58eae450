import React from 'react';
import { useWordCount } from '@/hooks/useWordCount';
import { ChatMessage } from '@/lib/types/chat';

interface WordCountDisplayProps {
  chatId: string | null;
  messages: ChatMessage[];
  className?: string;
}

/**
 * Componente para exibir contagem de palavras em tempo real
 */
export const WordCountDisplay: React.FC<WordCountDisplayProps> = ({ 
  chatId, 
  messages, 
  className = '' 
}) => {
  const { wordStats } = useWordCount(chatId, messages);

  if (!chatId || messages.length === 0) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-4 text-sm text-gray-400 ${className}`}>
      <div className="flex items-center space-x-2">
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <span>{wordStats.totalWords} palavras</span>
      </div>
      
      <div className="text-xs text-gray-500">
        {wordStats.userWords} suas • {wordStats.assistantWords} IA
      </div>
    </div>
  );
};

export default WordCountDisplay;
