import { db } from './firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { chatStorageService } from './chatStorageService';
import { ChatData, ChatMessage, AttachmentMetadata } from './types/chat';

export interface SyncService {
  syncChatToFirestore(userId: string, chatId: string): Promise<void>;
  syncChatFromFirestore(userId: string, chatId: string): Promise<void>;
  addMessageToChat(userId: string, chatId: string, role: 'user' | 'assistant', content: string, attachments?: AttachmentMetadata[], usage?: any, responseTime?: number, typingSpeed?: number): Promise<void>;
  getChatMessages(userId: string, chatId: string): Promise<ChatMessage[]>;
  updateChatMetadata(userId: string, chatId: string, lastUsedService?: string, lastUsedModel?: string): Promise<void>;
}

class SyncServiceImpl implements SyncService {

  /**
   * Determina se estamos no contexto do servidor ou cliente
   */
  private isServerSide(): boolean {
    return typeof window === 'undefined';
  }

  /**
   * Obtém o serviço de dados apropriado baseado no contexto
   */
  private async getChatDataService() {
    if (this.isServerSide()) {
      // No servidor, usar chatStorageService diretamente
      return chatStorageService;
    } else {
      // No cliente, usar chatDataService (que faz requisições HTTP)
      const { chatDataService } = await import('./chatDataService');
      return chatDataService;
    }
  }

  /**
   * Remove campos undefined de um objeto para evitar erros no Firestore
   */
  private removeUndefinedFields(obj: any): any {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined) {
        cleaned[key] = value;
      }
    }
    return cleaned;
  }
  
  /**
   * Sincroniza dados do Storage para o Firestore
   * Atualiza metadados como última atualização, última mensagem, etc.
   */
  async syncChatToFirestore(userId: string, chatId: string): Promise<void> {
    try {
      const dataService = await this.getChatDataService();
      const chatData = await dataService.getChatData(userId, chatId);
      
      if (!chatData) {
        console.warn(`Chat data not found in Storage: ${chatId}`);
        return;
      }

      // Preparar dados para atualizar no Firestore
      const firestoreUpdates = this.removeUndefinedFields({
        lastUpdatedAt: new Date(chatData.lastUpdatedAt),
        systemPrompt: chatData.system_prompt,
        context: chatData.context,
        temperature: chatData.temperature,
        maxTokens: chatData.maxTokens,
        frequencyPenalty: chatData.frequency_penalty,
        repetitionPenalty: chatData.repetition_penalty,
        lastUsedService: chatData.lastUsedService,
        lastUsedModel: chatData.lastUsedModel
      });

      // Se há mensagens, adicionar informações da última mensagem
      if (chatData.messages && chatData.messages.length > 0) {
        const lastMessage = chatData.messages[chatData.messages.length - 1];
        firestoreUpdates.ultimaMensagem = lastMessage.content.substring(0, 100); // Limitar tamanho
        firestoreUpdates.ultimaMensagemEm = new Date(lastMessage.timestamp);
      }

      // Atualizar documento no Firestore
      const conversationRef = doc(db, 'usuarios', userId, 'conversas', chatId);
      await updateDoc(conversationRef, firestoreUpdates);
      
      console.log(`Chat synced to Firestore: ${chatId}`);
    } catch (error) {
      console.error('Error syncing chat to Firestore:', error);
      throw error;
    }
  }

  /**
   * Sincroniza dados do Firestore para o Storage
   * Atualiza configurações e metadados no arquivo JSON
   */
  async syncChatFromFirestore(userId: string, chatId: string): Promise<void> {
    try {
      const conversationRef = doc(db, 'usuarios', userId, 'conversas', chatId);
      const conversationDoc = await getDoc(conversationRef);
      
      if (!conversationDoc.exists()) {
        console.warn(`Conversation not found in Firestore: ${chatId}`);
        return;
      }

      const firestoreData = conversationDoc.data();
      const dataService = await this.getChatDataService();
      const chatData = await dataService.getChatData(userId, chatId);
      
      if (!chatData) {
        console.warn(`Chat data not found in Storage: ${chatId}`);
        return;
      }

      // Atualizar dados do Storage com informações do Firestore
      const updatedChatData: ChatData = {
        ...chatData,
        name: firestoreData.name || chatData.name,
        system_prompt: firestoreData.systemPrompt || chatData.system_prompt,
        context: firestoreData.context || chatData.context,
        temperature: firestoreData.temperature !== undefined ? firestoreData.temperature : chatData.temperature,
        maxTokens: firestoreData.maxTokens !== undefined ? firestoreData.maxTokens : chatData.maxTokens,
        frequency_penalty: firestoreData.frequencyPenalty !== undefined ? firestoreData.frequencyPenalty : chatData.frequency_penalty,
        repetition_penalty: firestoreData.repetitionPenalty !== undefined ? firestoreData.repetitionPenalty : chatData.repetition_penalty,
        lastUsedService: firestoreData.lastUsedService !== undefined ? firestoreData.lastUsedService : chatData.lastUsedService,
        lastUsedModel: firestoreData.lastUsedModel !== undefined ? firestoreData.lastUsedModel : chatData.lastUsedModel,
        lastUpdatedAt: Date.now()
      };

      await dataService.updateChatData(userId, chatId, updatedChatData);
      
      console.log(`Chat synced from Firestore: ${chatId}`);
    } catch (error) {
      console.error('Error syncing chat from Firestore:', error);
      throw error;
    }
  }

  /**
   * Adiciona uma mensagem ao chat (apenas Storage por enquanto)
   */
  async addMessageToChat(userId: string, chatId: string, role: 'user' | 'assistant', content: string, attachments?: AttachmentMetadata[], usage?: any, responseTime?: number, typingSpeed?: number): Promise<void> {
    try {
      // Criar objeto de mensagem com dados adicionais
      const messageInput = {
        role,
        content,
        attachments,
        ...(usage && {
          usage: {
            prompt_tokens: usage.prompt_tokens || 0,
            completion_tokens: usage.completion_tokens || 0,
            total_tokens: usage.total_tokens || 0,
            cost: usage.cost || 0
          }
        }),
        ...(responseTime && { responseTime }),
        ...(typingSpeed && { typingSpeed })
      };

      // Adicionar mensagem ao Storage
      await chatStorageService.addMessage(userId, chatId, messageInput);

      // TODO: Sincronizar com Firestore quando autenticação do servidor estiver configurada
      // await this.syncChatToFirestore(userId, chatId);

      console.log(`Message added to Storage: ${chatId}`);
    } catch (error) {
      console.error('Error adding message to chat:', error);
      throw error;
    }
  }

  /**
   * Obtém todas as mensagens de um chat
   */
  async getChatMessages(userId: string, chatId: string): Promise<ChatMessage[]> {
    try {
      const dataService = await this.getChatDataService();
      const chatData = await dataService.getChatData(userId, chatId);
      return chatData?.messages || [];
    } catch (error) {
      console.error('Error getting chat messages:', error);
      throw error;
    }
  }

  /**
   * Atualiza metadados do chat (serviço e modelo utilizados)
   */
  async updateChatMetadata(userId: string, chatId: string, lastUsedService?: string, lastUsedModel?: string): Promise<void> {
    try {
      const dataService = await this.getChatDataService();
      const chatData = await dataService.getChatData(userId, chatId);

      if (!chatData) {
        console.warn(`Chat data not found: ${chatId}`);
        return;
      }

      const updatedChatData: ChatData = {
        ...chatData,
        lastUsedService: lastUsedService || chatData.lastUsedService,
        lastUsedModel: lastUsedModel || chatData.lastUsedModel,
        lastUpdatedAt: Date.now()
      };

      // Atualizar no Storage
      await dataService.updateChatData(userId, chatId, updatedChatData);

      // Sincronizar com Firestore
      await this.syncChatToFirestore(userId, chatId);

      console.log(`Chat metadata updated and synced: ${chatId}`);
    } catch (error) {
      console.error('Error updating chat metadata:', error);
      throw error;
    }
  }

  /**
   * Verifica se um chat existe tanto no Firestore quanto no Storage
   */
  async validateChatConsistency(userId: string, chatId: string): Promise<{
    firestoreExists: boolean;
    storageExists: boolean;
    consistent: boolean;
  }> {
    try {
      // Verificar Firestore
      const conversationRef = doc(db, 'usuarios', userId, 'conversas', chatId);
      const conversationDoc = await getDoc(conversationRef);
      const firestoreExists = conversationDoc.exists();

      // Verificar Storage
      const dataService = await this.getChatDataService();
      const chatData = await dataService.getChatData(userId, chatId);
      const storageExists = chatData !== null;

      return {
        firestoreExists,
        storageExists,
        consistent: firestoreExists === storageExists
      };
    } catch (error) {
      console.error('Error validating chat consistency:', error);
      return {
        firestoreExists: false,
        storageExists: false,
        consistent: false
      };
    }
  }
}

// Exportar uma instância singleton do serviço
export const syncService = new SyncServiceImpl();
export default syncService;
