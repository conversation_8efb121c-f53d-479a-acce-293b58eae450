import { modelFavoritesService } from '../lib/modelFavoritesService';

// Test para verificar se o sistema de favoritos está funcionando
async function testModelFavorites() {
  console.log('🧪 Testando sistema de favoritos...');

  const testUserId = 'test-user-123';
  const testEndpointId = 'test-endpoint-456';
  const testModelId = 'minimax/minimax-m1'; // Modelo com barra que causava erro
  const testModelName = 'Minimax M1';

  try {
    // 1. Verificar se o modelo não está favoritado inicialmente
    console.log('1. Verificando status inicial...');
    const initialStatus = await modelFavoritesService.isModelFavorited(testUserId, testEndpointId, testModelId);
    console.log(`Status inicial: ${initialStatus ? 'Favoritado' : 'Não favoritado'}`);

    // 2. Adicionar aos favoritos
    console.log('2. Adicionando aos favoritos...');
    await modelFavoritesService.addToFavorites(testUserId, testEndpointId, testModelId, testModelName);
    console.log('✅ Modelo adicionado aos favoritos');

    // 3. Verificar se foi adicionado
    console.log('3. Verificando se foi adicionado...');
    const afterAddStatus = await modelFavoritesService.isModelFavorited(testUserId, testEndpointId, testModelId);
    console.log(`Status após adicionar: ${afterAddStatus ? 'Favoritado' : 'Não favoritado'}`);

    // 4. Listar favoritos
    console.log('4. Listando favoritos...');
    const favorites = await modelFavoritesService.getFavoriteModels(testUserId, testEndpointId);
    console.log(`Favoritos encontrados: ${favorites.length}`);
    favorites.forEach(fav => {
      console.log(`- ${fav.modelName} (${fav.modelId})`);
    });

    // 5. Remover dos favoritos
    console.log('5. Removendo dos favoritos...');
    await modelFavoritesService.removeFromFavorites(testUserId, testEndpointId, testModelId);
    console.log('✅ Modelo removido dos favoritos');

    // 6. Verificar se foi removido
    console.log('6. Verificando se foi removido...');
    const afterRemoveStatus = await modelFavoritesService.isModelFavorited(testUserId, testEndpointId, testModelId);
    console.log(`Status após remover: ${afterRemoveStatus ? 'Favoritado' : 'Não favoritado'}`);

    // 7. Testar toggle
    console.log('7. Testando toggle...');
    const toggleResult1 = await modelFavoritesService.toggleFavorite(testUserId, testEndpointId, testModelId, testModelName);
    console.log(`Toggle 1 resultado: ${toggleResult1 ? 'Adicionado' : 'Removido'}`);

    const toggleResult2 = await modelFavoritesService.toggleFavorite(testUserId, testEndpointId, testModelId, testModelName);
    console.log(`Toggle 2 resultado: ${toggleResult2 ? 'Adicionado' : 'Removido'}`);

    console.log('🎉 Todos os testes passaram!');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
  }
}

// Função para testar diferentes tipos de IDs problemáticos
async function testProblematicModelIds() {
  console.log('🧪 Testando IDs problemáticos...');

  const testUserId = 'test-user-123';
  const testEndpointId = 'test-endpoint-456';
  
  const problematicIds = [
    'minimax/minimax-m1',
    'anthropic/claude-3.5-sonnet',
    'google/gemini-2.0-flash-exp',
    'meta-llama/llama-3.2-90b-vision-instruct',
    'openai/gpt-4o-2024-11-20',
    'model.with.dots',
    'model#with#hash',
    'model[with]brackets',
    'model*with*asterisk',
    'model~with~tilde'
  ];

  for (const modelId of problematicIds) {
    try {
      console.log(`\nTestando: ${modelId}`);
      
      // Testar toggle
      const result = await modelFavoritesService.toggleFavorite(testUserId, testEndpointId, modelId, `Test Model ${modelId}`);
      console.log(`✅ Toggle funcionou: ${result ? 'Adicionado' : 'Removido'}`);
      
      // Verificar status
      const status = await modelFavoritesService.isModelFavorited(testUserId, testEndpointId, modelId);
      console.log(`✅ Status verificado: ${status ? 'Favoritado' : 'Não favoritado'}`);
      
      // Remover para limpar
      if (status) {
        await modelFavoritesService.removeFromFavorites(testUserId, testEndpointId, modelId);
        console.log(`✅ Removido com sucesso`);
      }
      
    } catch (error) {
      console.error(`❌ Erro com ${modelId}:`, error);
    }
  }

  console.log('🎉 Teste de IDs problemáticos concluído!');
}

// Exportar funções para uso
export { testModelFavorites, testProblematicModelIds };

// Se executado diretamente
if (typeof window !== 'undefined') {
  console.log('Para testar, execute no console:');
  console.log('import { testModelFavorites, testProblematicModelIds } from "./test/modelFavorites.test";');
  console.log('testModelFavorites();');
  console.log('testProblematicModelIds();');
}
