'use client';

import React from 'react';

interface ThinkingBubbleProps {
  model?: string;
}

const ThinkingBubble = ({ model = 'IA' }: ThinkingBubbleProps) => {

  return (
    <div className="flex mb-6 justify-start">
      <div className="flex items-start space-x-4 max-w-[75%]">
        {/* Avatar com animação melhorada */}
        <div className="relative w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0">
          {/* Anel de carregamento externo */}
          <div className="absolute inset-0 rounded-full border-2 border-blue-500/20">
            <div className="absolute inset-0 rounded-full border-t-2 border-blue-400 animate-spin"></div>
          </div>

          {/* Avatar principal */}
          <div className="w-8 h-8 rounded-full flex items-center justify-center bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-600 avatar-glow">
            <svg className="w-4 h-4 text-white animate-pulse" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
        </div>

        {/* Bubble de pensamento melhorado */}
        <div className="flex flex-col space-y-2">
          <div className="relative group">
            {/* Background principal */}
            <div className="relative px-6 py-5 rounded-2xl backdrop-blur-md border bg-gradient-to-br from-blue-900/90 via-blue-800/85 to-blue-900/90 border-blue-500/30 shadow-2xl">
              {/* Efeito de brilho interno */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-400/10 via-transparent to-cyan-400/10"></div>

              {/* Borda brilhante animada */}
              <div className="absolute inset-0 rounded-2xl shadow-[0_0_30px_rgba(59,130,246,0.3)] transition-shadow duration-500"></div>



              {/* Conteúdo */}
              <div className="relative z-10">
                {/* Header com ícone e texto principal */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {/* Ícone principal com animação */}
                    <div className="relative">
                      <div className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-400 to-cyan-400 flex items-center justify-center">
                        <svg className="w-3.5 h-3.5 text-white animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                      </div>
                      {/* Pulso de energia */}
                      <div className="absolute inset-0 rounded-full bg-blue-400/30 animate-ping"></div>
                    </div>

                    {/* Texto principal */}
                    <div className="flex flex-col">
                      <span className="text-sm font-semibold text-blue-100 tracking-wide">
                        IA está processando
                      </span>
                      <span className="text-xs text-blue-300/80 font-medium">
                        Gerando resposta inteligente
                      </span>
                    </div>
                  </div>

                  {/* Indicador de atividade */}
                  <div className="flex items-center space-x-1">
                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-xs text-green-300/80 font-medium">Ativo</span>
                  </div>
                </div>

                {/* Informações do modelo */}
                <div className="flex items-center justify-between mb-4 p-2 rounded-lg bg-blue-800/40 border border-blue-600/20">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                    <span className="text-xs text-blue-200 font-medium">Modelo:</span>
                    <span className="text-xs text-cyan-300 font-mono bg-blue-900/50 px-2 py-0.5 rounded">
                      {model}
                    </span>
                  </div>
                </div>

                {/* Barra de progresso animada */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-blue-300/80 font-medium">Progresso</span>
                    <span className="text-xs text-blue-300/80 font-medium">Analisando...</span>
                  </div>
                  <div className="w-full h-2 bg-blue-900/50 rounded-full overflow-hidden relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-400 rounded-full progress-wave"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full progress-wave" style={{ animationDelay: '0.5s' }}></div>
                  </div>
                </div>

                {/* Dots de carregamento melhorados */}
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-2.5 h-2.5 rounded-full dot-sequence-1"></div>
                  <div className="w-2.5 h-2.5 rounded-full dot-sequence-2"></div>
                  <div className="w-2.5 h-2.5 rounded-full dot-sequence-3"></div>
                  <div className="w-2.5 h-2.5 rounded-full dot-sequence-4"></div>
                </div>

                {/* Texto de status */}
                <div className="text-center mt-4">
                  <span className="text-xs text-blue-300/80 italic font-medium">
                    Processando sua solicitação com cuidado...
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Timestamp melhorado */}
          <div className="flex items-center space-x-2 ml-1">
            <div className="w-1 h-1 bg-blue-400/60 rounded-full"></div>
            <span className="text-xs text-blue-400/70 font-medium">
              Agora
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThinkingBubble;
