'use client';

import { GeneralStatistics } from '@/lib/generalStatisticsService';
import { useRouter } from 'next/navigation';

interface StatsHeaderProps {
  statistics: GeneralStatistics;
  onRefresh: () => void;
  refreshing: boolean;
}

export default function StatsHeader({ statistics, onRefresh, refreshing }: StatsHeaderProps) {
  const router = useRouter();

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="relative">
      {/* Glass morphism header */}
      <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8 shadow-2xl">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.push('/dashboard')}
                className="p-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200 group"
                title="Voltar ao Dashboard"
              >
                <svg className="w-6 h-6 text-white group-hover:text-purple-200 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
              </button>
              <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
                  Analytics
                </h1>
                <p className="text-lg text-white/70 font-medium">
                  Insights inteligentes sobre suas conversas
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-white/60">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>Última atualização: {formatDate(statistics.lastUpdatedAt)}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Quick stats preview */}
            <div className="hidden lg:flex items-center space-x-6 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-white">{statistics.totalChats}</div>
                <div className="text-xs text-white/60 uppercase tracking-wide">Chats</div>
              </div>
              <div className="w-px h-12 bg-white/20"></div>
              <div className="space-y-1">
                <div className="text-2xl font-bold text-white">{statistics.totalMessages.toLocaleString()}</div>
                <div className="text-xs text-white/60 uppercase tracking-wide">Mensagens</div>
              </div>
              <div className="w-px h-12 bg-white/20"></div>
              <div className="space-y-1">
                <div className="text-2xl font-bold text-white">${(statistics.totalCost).toFixed(2)}</div>
                <div className="text-xs text-white/60 uppercase tracking-wide">Custo Total</div>
              </div>
            </div>

            {/* Refresh button */}
            <button
              onClick={onRefresh}
              disabled={refreshing}
              className="group relative p-3 bg-white/10 hover:bg-white/20 border border-white/20 rounded-2xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Atualizar dados"
            >
              <svg 
                className={`w-5 h-5 text-white transition-transform duration-500 ${refreshing ? 'animate-spin' : 'group-hover:rotate-180'}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
