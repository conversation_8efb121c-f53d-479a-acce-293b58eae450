/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configurações para evitar cache em produção
  generateEtags: false,
  poweredByHeader: false,

  // Headers personalizados para controle de cache
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate, max-age=0',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;