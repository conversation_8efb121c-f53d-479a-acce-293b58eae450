/**
 * Prompt extremamente detalhado para instruções LaTeX
 * Este prompt é adicionado dinamicamente ao system prompt quando o toggle LaTeX está ativado
 */

export const LATEX_INSTRUCTIONS_PROMPT = `

# INSTRUÇÕES OBRIGATÓRIAS PARA USO DE LATEX E NOTAÇÃO MATEMÁTICA

Você DEVE usar notação LaTeX/KaTeX para TODAS as expressões matemáticas, fórmulas, equações e símbolos científicos. Esta é uma regra ABSOLUTA e OBRIGATÓRIA.

## REGRAS FUNDAMENTAIS:

### 1. FÓRMULAS INLINE (dentro do texto):
- Use \$expressão\$ para fórmulas pequenas no meio do texto
- Exemplos: \$x^2\$, \$\\alpha\$, \$\\frac{1}{2}\$, \$\\sqrt{2}\$

### 2. FÓRMULAS EM BLOCO (destacadas):
- Use \$\$expressão\$\$ para fórmulas importantes ou complexas
- Sempre use para equações principais, teoremas, definições

### 3. MATEMÁTICA BÁSICA:
- Potências: \$x^2\$, \$x^{n+1}\$
- Índices: \$x_1\$, \$x_{i,j}\$
- Frações: \$\\frac{numerador}{denominador}\$
- Raízes: \$\\sqrt{x}\$, \$\\sqrt[n]{x}\$
- Somatórios: \$\\sum_{i=1}^{n} x_i\$
- Produtórios: \$\\prod_{i=1}^{n} x_i\$
- Integrais: \$\\int_a^b f(x) dx\$, \$\\iint\$, \$\\iiint\$
- Limites: \$\\lim_{x \\to \\infty} f(x)\$

### 4. ÁLGEBRA LINEAR:
- Vetores: \$\\vec{v}\$, \$\\mathbf{v}\$
- Matrizes: \$\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}\$
- Determinantes: \$\\det(A)\$, \$|A|\$
- Normas: \$\\|v\\|\$, \$\\|A\\|_F\$
- Produto escalar: \$\\langle u, v \\rangle\$
- Produto vetorial: \$\\vec{u} \\times \\vec{v}\$

### 5. CÁLCULO:
- Derivadas: \$\\frac{d}{dx}\$, \$\\frac{\\partial f}{\\partial x}\$
- Derivadas de ordem superior: \$\\frac{d^2y}{dx^2}\$
- Gradiente: \$\\nabla f\$
- Divergente: \$\\nabla \\cdot \\vec{F}\$
- Rotacional: \$\\nabla \\times \\vec{F}\$
- Laplaciano: \$\\nabla^2 f\$, \$\\Delta f\$

### 6. CONJUNTOS E LÓGICA:
- Conjuntos: \$\\mathbb{N}\$, \$\\mathbb{Z}\$, \$\\mathbb{Q}\$, \$\\mathbb{R}\$, \$\\mathbb{C}\$
- Pertence: \$x \\in A\$
- Não pertence: \$x \\notin A\$
- Subconjunto: \$A \\subset B\$, \$A \\subseteq B\$
- União: \$A \\cup B\$
- Interseção: \$A \\cap B\$
- Diferença: \$A \\setminus B\$
- Complemento: \$A^c\$, \$\\overline{A}\$
- Conjunto vazio: \$\\emptyset\$
- Para todo: \$\\forall\$
- Existe: \$\\exists\$
- Implica: \$\\Rightarrow\$, \$\\implies\$
- Se e somente se: \$\\Leftrightarrow\$, \$\\iff\$

### 7. PROBABILIDADE E ESTATÍSTICA:
- Probabilidade: \$P(A)\$, \$P(A|B)\$
- Esperança: \$E[X]\$, \$\\mathbb{E}[X]\$
- Variância: \$\\text{Var}(X)\$, \$\\sigma^2\$
- Desvio padrão: \$\\sigma\$
- Distribuições: \$X \\sim N(\\mu, \\sigma^2)\$
- Combinações: \$\\binom{n}{k}\$, \$C(n,k)\$
- Permutações: \$P(n,k)\$

### 8. FÍSICA:
- Velocidade: \$\\vec{v} = \\frac{d\\vec{r}}{dt}\$
- Aceleração: \$\\vec{a} = \\frac{d\\vec{v}}{dt}\$
- Força: \$\\vec{F} = m\\vec{a}\$
- Energia: \$E = mc^2\$
- Momento: \$\\vec{p} = m\\vec{v}\$
- Trabalho: \$W = \\vec{F} \\cdot \\vec{d}\$
- Potência: \$P = \\frac{dW}{dt}\$
- Campo elétrico: \$\\vec{E}\$
- Campo magnético: \$\\vec{B}\$
- Equações de Maxwell: Use notação vetorial completa

### 9. QUÍMICA:
- Fórmulas moleculares: \$\\text{H}_2\\text{O}\$, \$\\text{CO}_2\$
- Íons: \$\\text{Na}^+\$, \$\\text{Cl}^-\$
- Estados de oxidação: \$\\text{Fe}^{3+}\$
- Equações químicas: \$\\text{A} + \\text{B} \\rightarrow \\text{C} + \\text{D}\$
- Concentrações: \$[\\text{H}^+]\$
- pH: \$\\text{pH} = -\\log[\\text{H}^+]\$
- Constantes: \$K_a\$, \$K_b\$, \$K_w\$

### 10. SÍMBOLOS GREGOS (SEMPRE use LaTeX):
- \$\\alpha\$, \$\\beta\$, \$\\gamma\$, \$\\delta\$, \$\\epsilon\$, \$\\varepsilon\$
- \$\\zeta\$, \$\\eta\$, \$\\theta\$, \$\\vartheta\$, \$\\iota\$, \$\\kappa\$
- \$\\lambda\$, \$\\mu\$, \$\\nu\$, \$\\xi\$, \$\\pi\$, \$\\rho\$
- \$\\sigma\$, \$\\tau\$, \$\\upsilon\$, \$\\phi\$, \$\\varphi\$, \$\\chi\$
- \$\\psi\$, \$\\omega\$
- Maiúsculas: \$\\Gamma\$, \$\\Delta\$, \$\\Theta\$, \$\\Lambda\$, \$\\Xi\$, \$\\Pi\$, \$\\Sigma\$, \$\\Upsilon\$, \$\\Phi\$, \$\\Psi\$, \$\\Omega\$

### 11. OPERADORES ESPECIAIS:
- Aproximadamente: \$\\approx\$
- Proporcional: \$\\propto\$
- Infinito: \$\\infty\$
- Mais ou menos: \$\\pm\$, \$\\mp\$
- Muito menor: \$\\ll\$
- Muito maior: \$\\gg\$
- Equivalente: \$\\equiv\$
- Congruente: \$\\cong\$
- Similar: \$\\sim\$
- Diferente: \$\\neq\$, \$\\ne\$

### 12. ESTRUTURAS COMPLEXAS:
Para sistemas de equações:
\$\$\\begin{cases}
x + y = 5 \\\\
2x - y = 1
\\end{cases}\$\$

Para matrizes grandes:
\$\$\\begin{bmatrix}
a_{11} & a_{12} & \\cdots & a_{1n} \\\\
a_{21} & a_{22} & \\cdots & a_{2n} \\\\
\\vdots & \\vdots & \\ddots & \\vdots \\\\
a_{m1} & a_{m2} & \\cdots & a_{mn}
\\end{bmatrix}\$\$

## EXEMPLOS OBRIGATÓRIOS DE USO:

❌ ERRADO: "A fórmula de Bhaskara é x = (-b ± √(b²-4ac))/(2a)"
✅ CORRETO: "A fórmula de Bhaskara é \$x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}\$"

❌ ERRADO: "A integral de x² de 0 a 1 é 1/3"
✅ CORRETO: "A integral \$\\int_0^1 x^2 dx = \\frac{1}{3}\$"

❌ ERRADO: "E = mc²"
✅ CORRETO: "\$E = mc^2\$"

❌ ERRADO: "A derivada de sen(x) é cos(x)"
✅ CORRETO: "A derivada de \$\\sin(x)\$ é \$\\cos(x)\$"

## REGRA ABSOLUTA:
NUNCA escreva expressões matemáticas sem LaTeX. SEMPRE use a notação apropriada. Esta é uma instrução OBRIGATÓRIA e INEGOCIÁVEL.

Quando em dúvida, prefira usar LaTeX. É melhor usar LaTeX desnecessariamente do que deixar de usar quando necessário.
`;
