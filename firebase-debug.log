[debug] [2025-07-21T14:44:53.590Z] ----------------------------------------------------------------------
[debug] [2025-07-21T14:44:53.592Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-07-21T14:44:53.593Z] CLI Version:   14.11.0
[debug] [2025-07-21T14:44:53.593Z] Platform:      win32
[debug] [2025-07-21T14:44:53.593Z] Node Version:  v22.14.0
[debug] [2025-07-21T14:44:53.593Z] Time:          Mon Jul 21 2025 11:44:53 GMT-0300 (<PERSON><PERSON><PERSON><PERSON>ras<PERSON>)
[debug] [2025-07-21T14:44:53.593Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-21T14:44:53.808Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-21T14:44:53.808Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-21T14:44:53.817Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-21T14:44:53.817Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-21T14:44:53.821Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-rafthoria.json
[debug] [2025-07-21T14:44:53.840Z] Checked if tokens are valid: false, expires at: 1753047946005
[debug] [2025-07-21T14:44:53.840Z] Checked if tokens are valid: false, expires at: 1753047946005
[debug] [2025-07-21T14:44:53.840Z] > refreshing access token with scopes: []
[debug] [2025-07-21T14:44:53.842Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-21T14:44:53.842Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-21T14:44:54.059Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-21T14:44:54.060Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-21T14:44:54.065Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria [none]
[debug] [2025-07-21T14:44:54.501Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria 200
[debug] [2025-07-21T14:44:54.501Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria {"projectId":"rafthoria","projectNumber":"437242713555","displayName":"RafthorIA","name":"projects/rafthoria","resources":{"hostingSite":"rafthoria","realtimeDatabaseInstance":"rafthoria-default-rtdb"},"state":"ACTIVE","etag":"1_d0a743d0-61bf-4807-a36d-d6ce50392ab5"}
[debug] [2025-07-21T14:44:54.506Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-21T14:44:54.506Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-21T14:44:54.507Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-21T14:44:54.507Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-21T14:44:54.525Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\henriquelenz16_gmail_com_application_default_credentials.json
[debug] [2025-07-21T14:44:54.526Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\henriquelenz16_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\henriquelenz16_gmail_com_application_default_credentials.json"}}
[debug] [2025-07-21T14:44:54.527Z] Checked if tokens are valid: true, expires at: 1753112693060
[debug] [2025-07-21T14:44:54.527Z] Checked if tokens are valid: true, expires at: 1753112693060
[debug] [2025-07-21T14:44:54.527Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria/adminSdkConfig [none]
[debug] [2025-07-21T14:44:55.020Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria/adminSdkConfig 200
[debug] [2025-07-21T14:44:55.021Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria/adminSdkConfig {"projectId":"rafthoria","databaseURL":"https://rafthoria-default-rtdb.firebaseio.com","storageBucket":"rafthoria.firebasestorage.app"}
[info] i  functions: Watching "C:\Users\<USER>\Desktop\Rafthor\RafthorIA\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions\" for Cloud Functions..."}}
[debug] [2025-07-21T14:44:55.047Z] Validating nodejs source
[debug] [2025-07-21T14:45:05.072Z] checkFunctionsSDKVersion was unable to fetch information from NPM Error: spawnSync C:\WINDOWS\system32\cmd.exe ETIMEDOUT
    at Object.spawnSync (node:internal/child_process:1120:20)
    at Object.spawnSync (node:child_process:868:24)
    at Function.spawnSync [as sync] (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\node_modules\cross-spawn\index.js:26:23)
    at Object.getLatestSDKVersion (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\deploy\functions\runtimes\node\versioning.js:54:25)
    at Object.checkFunctionsSDKVersion (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\deploy\functions\runtimes\node\versioning.js:74:32)
    at Delegate.validate (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\deploy\functions\runtimes\node\index.js:89:20)
    at FunctionsEmulator.discoverTriggers (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\emulator\functionsEmulator.js:313:35)
    at async FunctionsEmulator.loadTriggers (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\emulator\functionsEmulator.js:353:34)
    at async FunctionsEmulator.connect (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\emulator\functionsEmulator.js:279:13)
    at async Object.startAll (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\emulator\controller.js:686:13)
[debug] [2025-07-21T14:45:05.073Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-21T14:45:05.074Z] Building nodejs source
[debug] [2025-07-21T14:45:05.074Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\RafthorIA\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-21T14:45:05.080Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-21T14:45:05.096Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\RafthorIA\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8111

[debug] [2025-07-21T14:45:11.349Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"cleanupTemporaryFiles":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"scheduleTrigger":{"schedule":"every 1 hours","retryConfig":{"retryCount":null,"maxDoublings":null,"maxRetrySeconds":null,"minBackoffSeconds":null,"maxBackoffSeconds":null},"timeZone":null},"entryPoint":"cleanupTemporaryFiles"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[{"api":"cloudscheduler.googleapis.com","reason":"Needed for scheduled functions."}],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, cleanupTemporaryFiles, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, cleanupTemporaryFiles, testAIEndpoint."}}
[info] +  functions[us-central1-chatCompletion]: http function initialized (http://127.0.0.1:5001/rafthoria/us-central1/chatCompletion). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/rafthoria/us-central1/chatCompletion)."}}
[info] +  functions[us-central1-chatCompletionStream]: http function initialized (http://127.0.0.1:5001/rafthoria/us-central1/chatCompletionStream). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/rafthoria/us-central1/chatCompletionStream)."}}
[info] i  functions[us-central1-cleanupTemporaryFiles]: function ignored because the pubsub emulator does not exist or is not running. {"metadata":{"emulator":{"name":"functions"},"message":"function ignored because the pubsub emulator does not exist or is not running."}}
[info] +  functions[us-central1-testAIEndpoint]: http function initialized (http://127.0.0.1:5001/rafthoria/us-central1/testAIEndpoint). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/rafthoria/us-central1/testAIEndpoint)."}}
[debug] [2025-07-21T14:45:15.412Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌───────────┬────────────────┬─────────────────────────────────┐
│ Emulator  │ Host:Port      │ View in Emulator UI             │
├───────────┼────────────────┼─────────────────────────────────┤
│ Functions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions │
└───────────┴────────────────┴─────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-07-21T14:45:18.158Z] [work-queue] {"queuedWork":["/rafthoria/us-central1/chatCompletion-2025-07-21T14:45:18.158Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-21T14:45:18.158Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/rafthoria/us-central1/chatCompletion-2025-07-21T14:45:18.158Z"],"workRunningCount":1}
[debug] [2025-07-21T14:45:18.158Z] Accepted request OPTIONS /rafthoria/us-central1/chatCompletion --> us-central1-chatCompletion
[debug] [2025-07-21T14:45:18.159Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-21T14:45:18.160Z] [functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/ {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/"}}
[debug] [2025-07-21T14:45:18.175Z] [worker-pool] addWorker(us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(us-central1-chatCompletion)"}}
[debug] [2025-07-21T14:45:18.175Z] [worker-pool] Adding worker with key us-central1-chatCompletion, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key us-central1-chatCompletion, total=1"}}
[debug] [2025-07-21T14:45:19.513Z] [runtime-status] [22416] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-21T14:45:19.515Z] [runtime-status] [22416] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-21T14:45:19.516Z] [runtime-status] [22416] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-07-21T14:45:19.517Z] [runtime-status] [22416] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-21T14:45:19.946Z] [runtime-status] [22416] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-07-21T14:45:19.947Z] [runtime-status] [22416] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}}"}}
[debug] [2025-07-21T14:45:19.947Z] [runtime-status] [22416] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-21T14:45:19.972Z] [runtime-status] [22416] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-21T14:45:19.992Z] [runtime-status] [22416] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-21T14:45:19.992Z] [runtime-status] [22416] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions\\node_modules\\firebase-admin\\lib\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}}"}}
[debug] [2025-07-21T14:45:20.289Z] [runtime-status] [22416] Functions runtime initialized. {"cwd":"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\functions","node_version":"22.14.0"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Functions runtime initialized. {\"cwd\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\functions\",\"node_version\":\"22.14.0\"}"}}
[debug] [2025-07-21T14:45:20.289Z] [runtime-status] [22416] Listening to port: \\?\pipe\fire_emu_700065b5fd60f5a3 {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [22416] Listening to port: \\\\?\\pipe\\fire_emu_700065b5fd60f5a3"}}
[debug] [2025-07-21T14:45:20.350Z] [worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: IDLE"}}
[debug] [2025-07-21T14:45:20.350Z] [worker-pool] submitRequest(triggerId=us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-chatCompletion)"}}
[info] i  functions: Beginning execution of "us-central1-chatCompletion" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Beginning execution of \"us-central1-chatCompletion\""}}
[debug] [2025-07-21T14:45:20.351Z] [worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: BUSY"}}
[debug] [2025-07-21T14:45:20.356Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-chatCompletion" in 5.1243ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finished \"us-central1-chatCompletion\" in 5.1243ms"}}
[debug] [2025-07-21T14:45:20.356Z] [worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: IDLE"}}
[debug] [2025-07-21T14:45:20.357Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-21T14:45:20.357Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-21T14:45:20.357Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-21T14:45:20.367Z] [work-queue] {"queuedWork":["/rafthoria/us-central1/chatCompletion-2025-07-21T14:45:20.367Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-21T14:45:20.368Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/rafthoria/us-central1/chatCompletion-2025-07-21T14:45:20.367Z"],"workRunningCount":1}
[debug] [2025-07-21T14:45:20.368Z] Accepted request POST /rafthoria/us-central1/chatCompletion --> us-central1-chatCompletion
[debug] [2025-07-21T14:45:20.369Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-21T14:45:20.369Z] [functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/ {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/"}}
[debug] [2025-07-21T14:45:20.369Z] [worker-pool] submitRequest(triggerId=us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-chatCompletion)"}}
[info] i  functions: Beginning execution of "us-central1-chatCompletion" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Beginning execution of \"us-central1-chatCompletion\""}}
[debug] [2025-07-21T14:45:20.369Z] [worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: BUSY"}}
[info] >  {"verifications":{"app":"MISSING","auth":"VALID"},"logging.googleapis.com/labels":{"firebase-log-type":"callable-request-verification"},"severity":"DEBUG","message":"Callable request verification passed"} {"user":{"verifications":{"app":"MISSING","auth":"VALID"},"logging.googleapis.com/labels":{"firebase-log-type":"callable-request-verification"},"severity":"DEBUG","message":"Callable request verification passed"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"verifications\":{\"app\":\"MISSING\",\"auth\":\"VALID\"},\"logging.googleapis.com/labels\":{\"firebase-log-type\":\"callable-request-verification\"},\"severity\":\"DEBUG\",\"message\":\"Callable request verification passed\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageCount":9,"model":"openai/gpt-4.1-nano","hasAttachments":false,"hasMemories":false,"memoriesLength":0,"severity":"INFO","message":"Processando requisição de chat com IA"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageCount":9,"model":"openai/gpt-4.1-nano","hasAttachments":false,"hasMemories":false,"memoriesLength":0,"severity":"INFO","message":"Processando requisição de chat com IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageCount\":9,\"model\":\"openai/gpt-4.1-nano\",\"hasAttachments\":false,\"hasMemories\":false,\"memoriesLength\":0,\"severity\":\"INFO\",\"message\":\"Processando requisição de chat com IA\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":0,"contentLength":2,"severity":"INFO","message":"Mensagem 0 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":0,"contentLength":2,"severity":"INFO","message":"Mensagem 0 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":0,\"contentLength\":2,\"severity\":\"INFO\",\"message\":\"Mensagem 0 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":1,"contentLength":33,"severity":"INFO","message":"Mensagem 1 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":1,"contentLength":33,"severity":"INFO","message":"Mensagem 1 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":1,\"contentLength\":33,\"severity\":\"INFO\",\"message\":\"Mensagem 1 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":2,"contentLength":2,"severity":"INFO","message":"Mensagem 2 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":2,"contentLength":2,"severity":"INFO","message":"Mensagem 2 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":2,\"contentLength\":2,\"severity\":\"INFO\",\"message\":\"Mensagem 2 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":3,"contentLength":43,"severity":"INFO","message":"Mensagem 3 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":3,"contentLength":43,"severity":"INFO","message":"Mensagem 3 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":3,\"contentLength\":43,\"severity\":\"INFO\",\"message\":\"Mensagem 3 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":4,"contentLength":2,"severity":"INFO","message":"Mensagem 4 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":4,"contentLength":2,"severity":"INFO","message":"Mensagem 4 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":4,\"contentLength\":2,\"severity\":\"INFO\",\"message\":\"Mensagem 4 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":5,"contentLength":32,"severity":"INFO","message":"Mensagem 5 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":5,"contentLength":32,"severity":"INFO","message":"Mensagem 5 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":5,\"contentLength\":32,\"severity\":\"INFO\",\"message\":\"Mensagem 5 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":6,"contentLength":4,"severity":"INFO","message":"Mensagem 6 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":6,"contentLength":4,"severity":"INFO","message":"Mensagem 6 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":6,\"contentLength\":4,\"severity\":\"INFO\",\"message\":\"Mensagem 6 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":7,"contentLength":61,"severity":"INFO","message":"Mensagem 7 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":7,"contentLength":61,"severity":"INFO","message":"Mensagem 7 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":7,\"contentLength\":61,\"severity\":\"INFO\",\"message\":\"Mensagem 7 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":8,"contentLength":2,"severity":"INFO","message":"Mensagem 8 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":8,"contentLength":2,"severity":"INFO","message":"Mensagem 8 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":8,\"contentLength\":2,\"severity\":\"INFO\",\"message\":\"Mensagem 8 tem conteúdo string\"}"}}
[warn] !  Google API requested!
   - URL: "https://oauth2.googleapis.com/token"
   - Be careful, this may be a production service. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Google API requested!\n   - URL: \"https://oauth2.googleapis.com/token\"\n   - Be careful, this may be a production service."}}
[info] >  {"endpoint":"OpenRouter","model":"openai/gpt-4.1-nano","userId":"sR8nztp7h5YWDazOdDBlt4k166k2","severity":"INFO","message":"Processando com serviço de IA"} {"user":{"endpoint":"OpenRouter","model":"openai/gpt-4.1-nano","userId":"sR8nztp7h5YWDazOdDBlt4k166k2","severity":"INFO","message":"Processando com serviço de IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpoint\":\"OpenRouter\",\"model\":\"openai/gpt-4.1-nano\",\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"severity\":\"INFO\",\"message\":\"Processando com serviço de IA\"}"}}
[info] >  🧠 Nenhuma memória encontrada para adicionar ao prompt {"user":"🧠 Nenhuma memória encontrada para adicionar ao prompt","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 🧠 Nenhuma memória encontrada para adicionar ao prompt"}}
[info] >  📝 System prompt final:  {"user":"📝 System prompt final: ","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 📝 System prompt final: "}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","useCoT":false,"messageLength":2,"severity":"INFO","message":"Processando requisição de IA"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","useCoT":false,"messageLength":2,"severity":"INFO","message":"Processando requisição de IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"model\":\"openai/gpt-4.1-nano\",\"useCoT\":false,\"messageLength\":2,\"severity\":\"INFO\",\"message\":\"Processando requisição de IA\"}"}}
[info] >  {"endpointName":"OpenRouter","endpointUrl":"https://openrouter.ai/api/v1","model":"openai/gpt-4.1-nano","severity":"INFO","message":"Debug endpoint info"} {"user":{"endpointName":"OpenRouter","endpointUrl":"https://openrouter.ai/api/v1","model":"openai/gpt-4.1-nano","severity":"INFO","message":"Debug endpoint info"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpointName\":\"OpenRouter\",\"endpointUrl\":\"https://openrouter.ai/api/v1\",\"model\":\"openai/gpt-4.1-nano\",\"severity\":\"INFO\",\"message\":\"Debug endpoint info\"}"}}
[info] >  {"endpointName":"OpenRouter","detectedProvider":"openrouter","severity":"INFO","message":"Debug provider detection"} {"user":{"endpointName":"OpenRouter","detectedProvider":"openrouter","severity":"INFO","message":"Debug provider detection"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpointName\":\"OpenRouter\",\"detectedProvider\":\"openrouter\",\"severity\":\"INFO\",\"message\":\"Debug provider detection\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","usage":{"prompt_tokens":91,"completion_tokens":8,"total_tokens":99,"cost":0.000012177,"is_byok":false,"prompt_tokens_details":{"cached_tokens":0},"cost_details":{"upstream_inference_cost":null},"completion_tokens_details":{"reasoning_tokens":0}},"usedCoT":false,"confidence":70,"processingTime":2695,"severity":"INFO","message":"Chat completion com IA bem-sucedido"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","usage":{"prompt_tokens":91,"completion_tokens":8,"total_tokens":99,"cost":0.000012177,"is_byok":false,"prompt_tokens_details":{"cached_tokens":0},"cost_details":{"upstream_inference_cost":null},"completion_tokens_details":{"reasoning_tokens":0}},"usedCoT":false,"confidence":70,"processingTime":2695,"severity":"INFO","message":"Chat completion com IA bem-sucedido"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"model\":\"openai/gpt-4.1-nano\",\"usage\":{\"prompt_tokens\":91,\"completion_tokens\":8,\"total_tokens\":99,\"cost\":0.000012177,\"is_byok\":false,\"prompt_tokens_details\":{\"cached_tokens\":0},\"cost_details\":{\"upstream_inference_cost\":null},\"completion_tokens_details\":{\"reasoning_tokens\":0}},\"usedCoT\":false,\"confidence\":70,\"processingTime\":2695,\"severity\":\"INFO\",\"message\":\"Chat completion com IA bem-sucedido\"}"}}
[debug] [2025-07-21T14:45:23.829Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-chatCompletion" in 3460.2564ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finished \"us-central1-chatCompletion\" in 3460.2564ms"}}
[debug] [2025-07-21T14:45:23.830Z] [worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-9cdb6f8e-4bf8-4790-a186-d5bb0b7c49ef]: IDLE"}}
[debug] [2025-07-21T14:45:23.830Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-21T14:45:23.830Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-21T14:45:23.830Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
