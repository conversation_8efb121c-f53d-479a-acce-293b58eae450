import { NextRequest, NextResponse } from 'next/server';
import { chatStorageService } from '@/lib/chatStorageService';
import { chatStatisticsService } from '@/lib/chatStatisticsService';

export const dynamic = 'force-dynamic';

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, chatId, messageId, content } = body;

    // Validar parâmetros obrigatórios
    if (!userId || !chatId || !messageId || !content) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, chatId, messageId, content' },
        { status: 400 }
      );
    }

    // Obter dados atuais do chat
    const currentData = await chatStorageService.getChatData(userId, chatId);
    
    if (!currentData) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    // Encontrar e atualizar a mensagem
    const messageIndex = currentData.messages.findIndex(message => message.id === messageId);

    if (messageIndex === -1) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    // Obter dados da mensagem original para estatísticas
    const originalMessage = currentData.messages[messageIndex];
    const originalLength = originalMessage.content.length;
    const newLength = content.trim().length;

    // Atualizar o conteúdo da mensagem
    const updatedMessages = [...currentData.messages];
    updatedMessages[messageIndex] = {
      ...updatedMessages[messageIndex],
      content: content.trim(),
      timestamp: Date.now() // Atualizar timestamp para indicar edição
    };

    // Atualizar dados do chat
    const updatedChatData = {
      ...currentData,
      messages: updatedMessages
    };

    await chatStorageService.updateChatData(userId, chatId, updatedChatData);

    // Atualizar estatísticas de edição
    try {
      await chatStatisticsService.updateStatisticsOnMessageEdit(
        userId,
        chatId,
        originalMessage.role,
        originalLength,
        newLength
      );
    } catch (error) {
      console.error('Error updating edit statistics:', error);
      // Não falhar a operação se as estatísticas falharem
    }

    return NextResponse.json({
      success: true,
      message: 'Message updated successfully'
    });

  } catch (error) {
    console.error('Error updating message:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
