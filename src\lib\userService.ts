import { db } from './firebase';
import { doc, setDoc, getDoc } from 'firebase/firestore';

interface UserData {
  username: string;
  email: string;
  profilePhotoURL?: string;
  chatAppearance?: {
    fontSize: number;
    fontFamily: string;
  };
  streaming?: {
    enabled: boolean;
  };
  chatSessions?: {
    enabled: boolean;
    wordsPerSession: number;
  };
}

// Salva os dados do usuário no Firestore
export const saveUserData = async (userId: string, data: UserData): Promise<void> => {
  try {
    console.log('🔥 saveUserData called with:', { userId, data });

    // Incluir configurações padrão se não fornecidas
    const userData = {
      ...data,
      chatAppearance: data.chatAppearance || {
        fontSize: 14,
        fontFamily: 'Inter'
      }
    };

    console.log('🔥 userData to save:', userData);

    const userRef = doc(db, 'usuarios', userId);
    console.log('🔥 userRef path:', userRef.path);

    await setDoc(userRef, userData);
    console.log('🔥 User data saved successfully to Firestore');
  } catch (error) {
    console.error('❌ Erro ao salvar dados do usuário:', error);
    throw error;
  }
};

// Obtém os dados do usuário do Firestore
export const getUserData = async (userId: string): Promise<UserData | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'usuarios', userId));
    
    if (userDoc.exists()) {
      return userDoc.data() as UserData;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Erro ao obter dados do usuário:', error);
    throw error;
  }
}; 