/**
 * Teste para verificar se a deleção de anexos funciona corretamente
 * quando um chat é deletado
 */

import { attachmentService } from '../lib/attachmentService';
import { deleteConversation } from '../lib/conversationService';

// Mock do Firebase Storage para testes
jest.mock('../lib/firebase', () => ({
  storage: {},
  db: {}
}));

jest.mock('firebase/storage', () => ({
  ref: jest.fn(),
  uploadBytes: jest.fn(),
  getDownloadURL: jest.fn(),
  listAll: jest.fn(),
  getMetadata: jest.fn(),
  deleteObject: jest.fn()
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  addDoc: jest.fn(),
  getDocs: jest.fn(),
  query: jest.fn(),
  orderBy: jest.fn(),
  serverTimestamp: jest.fn(),
  Timestamp: { now: jest.fn() },
  updateDoc: jest.fn(),
  getDoc: jest.fn(),
  deleteDoc: jest.fn()
}));

describe('Attachment Deletion Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('deleteChatAttachments should handle empty attachment folder', async () => {
    const { listAll } = require('firebase/storage');
    
    // Mock para pasta vazia
    listAll.mockResolvedValue({
      items: []
    });

    const result = await attachmentService.deleteChatAttachments('user123', 'chat456');

    expect(result.success).toBe(true);
    expect(result.deletedCount).toBe(0);
    expect(result.errors).toHaveLength(0);
  });

  test('deleteChatAttachments should handle non-existent folder', async () => {
    const { listAll } = require('firebase/storage');
    
    // Mock para pasta que não existe
    const error = new Error('Object not found');
    (error as any).code = 'storage/object-not-found';
    listAll.mockRejectedValue(error);

    const result = await attachmentService.deleteChatAttachments('user123', 'chat456');

    expect(result.success).toBe(true);
    expect(result.deletedCount).toBe(0);
    expect(result.errors).toHaveLength(0);
  });

  test('deleteChatAttachments should delete multiple attachments', async () => {
    const { listAll, deleteObject } = require('firebase/storage');
    
    // Mock para pasta com anexos
    const mockAttachments = [
      { name: 'attachment1.jpg', fullPath: 'usuarios/user123/conversas/chat456/anexos/attachment1.jpg' },
      { name: 'attachment2.pdf', fullPath: 'usuarios/user123/conversas/chat456/anexos/attachment2.pdf' }
    ];
    
    listAll.mockResolvedValue({
      items: mockAttachments
    });
    
    deleteObject.mockResolvedValue(undefined);

    const result = await attachmentService.deleteChatAttachments('user123', 'chat456');

    expect(result.success).toBe(true);
    expect(result.deletedCount).toBe(2);
    expect(result.errors).toHaveLength(0);
    expect(deleteObject).toHaveBeenCalledTimes(2);
  });

  test('deleteChatAttachments should handle partial failures', async () => {
    const { listAll, deleteObject } = require('firebase/storage');
    
    // Mock para pasta com anexos
    const mockAttachments = [
      { name: 'attachment1.jpg', fullPath: 'usuarios/user123/conversas/chat456/anexos/attachment1.jpg' },
      { name: 'attachment2.pdf', fullPath: 'usuarios/user123/conversas/chat456/anexos/attachment2.pdf' }
    ];
    
    listAll.mockResolvedValue({
      items: mockAttachments
    });
    
    // Primeiro anexo deleta com sucesso, segundo falha
    deleteObject
      .mockResolvedValueOnce(undefined)
      .mockRejectedValueOnce(new Error('Delete failed'));

    const result = await attachmentService.deleteChatAttachments('user123', 'chat456');

    expect(result.success).toBe(true);
    expect(result.deletedCount).toBe(1);
    expect(result.errors).toHaveLength(1);
    expect(result.errors[0]).toContain('attachment2.pdf');
  });

  test('deleteConversation should call deleteChatAttachments', async () => {
    const { deleteDoc } = require('firebase/firestore');
    const { listAll } = require('firebase/storage');
    
    // Mock para pasta vazia (sem anexos)
    listAll.mockResolvedValue({
      items: []
    });
    
    deleteDoc.mockResolvedValue(undefined);

    // Mock do chatStorageService
    const mockDeleteChatFile = jest.fn().mockResolvedValue(undefined);
    jest.doMock('../lib/chatStorageService', () => ({
      chatStorageService: {
        deleteChatFile: mockDeleteChatFile
      }
    }));

    // Importar após o mock
    const { deleteConversation } = require('../lib/conversationService');

    await deleteConversation('user123', 'chat456');

    // Verificar se listAll foi chamado (parte do deleteChatAttachments)
    expect(listAll).toHaveBeenCalled();
    expect(deleteDoc).toHaveBeenCalled();
    expect(mockDeleteChatFile).toHaveBeenCalledWith('user123', 'chat456');
  });
});
