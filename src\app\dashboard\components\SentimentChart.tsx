'use client';

interface SentimentTrend {
  timestamp: number;
  score: number;
}

interface SentimentChartProps {
  sentimentTrend: SentimentTrend[];
  className?: string;
}

export default function SentimentChart({ sentimentTrend, className = '' }: SentimentChartProps) {
  if (!sentimentTrend || sentimentTrend.length === 0) {
    return (
      <div className={`bg-slate-800/30 rounded-xl p-6 border border-slate-600/20 ${className}`}>
        <h4 className="text-md font-medium text-white mb-4">📈 Evolução do Sentimento</h4>
        <div className="text-center py-8">
          <p className="text-slate-400">Dados insuficientes para gerar gráfico</p>
        </div>
      </div>
    );
  }

  // Normalizar dados para o gráfico
  const maxPoints = 20; // Mostrar últimos 20 pontos
  const data = sentimentTrend.slice(-maxPoints);
  
  // Calcular dimensões do gráfico
  const width = 300;
  const height = 120;
  const padding = 20;
  const chartWidth = width - (padding * 2);
  const chartHeight = height - (padding * 2);

  // Calcular pontos do gráfico
  const points = data.map((point, index) => {
    const x = padding + (index / (data.length - 1)) * chartWidth;
    const y = padding + ((1 - (point.score + 1) / 2) * chartHeight); // Normalizar de [-1,1] para [0,1]
    return { x, y, score: point.score, timestamp: point.timestamp };
  });

  // Criar path para a linha
  const pathData = points.reduce((path, point, index) => {
    const command = index === 0 ? 'M' : 'L';
    return `${path} ${command} ${point.x} ${point.y}`;
  }, '');

  // Linha de referência (sentimento neutro)
  const neutralY = padding + (chartHeight / 2);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit'
    });
  };

  const getSentimentColor = (score: number) => {
    if (score > 0.1) return '#10b981'; // green-500
    if (score < -0.1) return '#ef4444'; // red-500
    return '#f59e0b'; // amber-500
  };

  const getSentimentLabel = (score: number) => {
    if (score > 0.1) return 'Positivo';
    if (score < -0.1) return 'Negativo';
    return 'Neutro';
  };

  return (
    <div className={`bg-slate-800/30 rounded-xl p-6 border border-slate-600/20 ${className}`}>
      <h4 className="text-md font-medium text-white mb-4">📈 Evolução do Sentimento</h4>
      
      {/* Gráfico SVG */}
      <div className="relative">
        <svg width={width} height={height} className="overflow-visible">
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(148, 163, 184, 0.1)" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width={width} height={height} fill="url(#grid)" />
          
          {/* Linha de referência (neutro) */}
          <line
            x1={padding}
            y1={neutralY}
            x2={width - padding}
            y2={neutralY}
            stroke="rgba(148, 163, 184, 0.3)"
            strokeWidth="1"
            strokeDasharray="5,5"
          />
          
          {/* Área sob a curva */}
          <path
            d={`${pathData} L ${points[points.length - 1]?.x || 0} ${height - padding} L ${padding} ${height - padding} Z`}
            fill="url(#sentimentGradient)"
            opacity="0.2"
          />
          
          {/* Linha principal */}
          <path
            d={pathData}
            fill="none"
            stroke="#8b5cf6"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Pontos */}
          {points.map((point, index) => (
            <g key={index}>
              <circle
                cx={point.x}
                cy={point.y}
                r="4"
                fill={getSentimentColor(point.score)}
                stroke="white"
                strokeWidth="2"
                className="hover:r-6 transition-all cursor-pointer"
              />
              
              {/* Tooltip invisível para hover */}
              <circle
                cx={point.x}
                cy={point.y}
                r="12"
                fill="transparent"
                className="hover:fill-slate-600/20"
              >
                <title>
                  {formatDate(point.timestamp)}: {getSentimentLabel(point.score)} ({point.score.toFixed(2)})
                </title>
              </circle>
            </g>
          ))}
          
          {/* Gradiente para área */}
          <defs>
            <linearGradient id="sentimentGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#10b981" stopOpacity="0.3"/>
              <stop offset="50%" stopColor="#f59e0b" stopOpacity="0.2"/>
              <stop offset="100%" stopColor="#ef4444" stopOpacity="0.3"/>
            </linearGradient>
          </defs>
        </svg>
        
        {/* Labels dos eixos */}
        <div className="absolute -left-2 top-2 text-xs text-green-400">+1</div>
        <div className="absolute -left-2 top-1/2 text-xs text-yellow-400">0</div>
        <div className="absolute -left-2 bottom-2 text-xs text-red-400">-1</div>
      </div>
      
      {/* Legenda */}
      <div className="mt-4 flex justify-center space-x-4 text-xs">
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span className="text-green-400">Positivo</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <span className="text-yellow-400">Neutro</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <span className="text-red-400">Negativo</span>
        </div>
      </div>
      
      {/* Estatísticas resumidas */}
      <div className="mt-4 grid grid-cols-3 gap-2 text-center text-xs">
        <div>
          <div className="text-slate-400">Último</div>
          <div className={`font-medium ${points.length > 0 ? (points[points.length - 1].score > 0.1 ? 'text-green-400' : points[points.length - 1].score < -0.1 ? 'text-red-400' : 'text-yellow-400') : 'text-slate-400'}`}>
            {points.length > 0 ? getSentimentLabel(points[points.length - 1].score) : 'N/A'}
          </div>
        </div>
        <div>
          <div className="text-slate-400">Média</div>
          <div className="text-white font-medium">
            {data.length > 0 ? (data.reduce((sum, p) => sum + p.score, 0) / data.length).toFixed(2) : 'N/A'}
          </div>
        </div>
        <div>
          <div className="text-slate-400">Pontos</div>
          <div className="text-blue-400 font-medium">{data.length}</div>
        </div>
      </div>
    </div>
  );
}
