'use client';

import { GeneralStatistics } from '@/lib/generalStatisticsService';

interface InsightsSectionProps {
  statistics: GeneralStatistics;
}

export default function InsightsSection({ statistics }: InsightsSectionProps) {
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // Generate insights based on data
  const generateInsights = () => {
    const insights = [];
    
    // Cost efficiency insight
    const costPerMessage = statistics.totalCost / Math.max(statistics.totalMessages, 1);
    if (costPerMessage < 0.01) {
      insights.push({
        type: 'positive',
        title: 'Excelente Eficiência de Custo',
        description: `Você está gastando apenas $${costPerMessage.toFixed(4)} por mensagem, muito abaixo da média.`,
        icon: '💰'
      });
    }

    // Activity pattern insight
    const peakHour = statistics.activityHeatmap.hourOfDay.indexOf(Math.max(...statistics.activityHeatmap.hourOfDay));
    if (peakHour >= 9 && peakHour <= 17) {
      insights.push({
        type: 'info',
        title: 'Padrão de Trabalho Diurno',
        description: `Sua maior atividade é às ${peakHour}h, indicando uso profissional durante horário comercial.`,
        icon: '🌅'
      });
    } else if (peakHour >= 20 || peakHour <= 6) {
      insights.push({
        type: 'info',
        title: 'Usuário Noturno',
        description: `Você é mais ativo às ${peakHour}h, preferindo conversas durante a noite.`,
        icon: '🌙'
      });
    }

    // Message complexity insight
    if (statistics.messageComplexity.averageWordsPerMessage > 25) {
      insights.push({
        type: 'positive',
        title: 'Conversas Detalhadas',
        description: `Suas mensagens têm em média ${statistics.messageComplexity.averageWordsPerMessage.toFixed(1)} palavras, indicando conversas profundas e detalhadas.`,
        icon: '📝'
      });
    }

    // Response time insight
    if (statistics.averageResponseTime < 5000) {
      insights.push({
        type: 'positive',
        title: 'Respostas Rápidas',
        description: `Tempo médio de resposta de ${(statistics.averageResponseTime / 1000).toFixed(1)}s demonstra alta eficiência.`,
        icon: '⚡'
      });
    }

    // Usage frequency insight
    const messagesPerDay = statistics.totalMessages / Math.max(1, Math.ceil((Date.now() - statistics.firstMessageAt) / (1000 * 60 * 60 * 24)));
    if (messagesPerDay > 10) {
      insights.push({
        type: 'info',
        title: 'Usuário Ativo',
        description: `Com ${messagesPerDay.toFixed(1)} mensagens por dia, você é um usuário muito engajado.`,
        icon: '🔥'
      });
    }

    return insights.slice(0, 4); // Limit to 4 insights
  };

  const insights = generateInsights();

  const getInsightStyle = (type: string) => {
    switch (type) {
      case 'positive':
        return 'from-green-500/10 to-emerald-500/10 border-green-400/20';
      case 'warning':
        return 'from-yellow-500/10 to-orange-500/10 border-yellow-400/20';
      case 'info':
        return 'from-blue-500/10 to-indigo-500/10 border-blue-400/20';
      default:
        return 'from-gray-500/10 to-slate-500/10 border-gray-400/20';
    }
  };

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center space-x-3">
        <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl shadow-lg">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        </div>
        <div>
          <h2 className="text-2xl font-bold text-white">Insights Inteligentes</h2>
          <p className="text-white/60">Descobertas baseadas nos seus padrões de uso</p>
        </div>
      </div>

      {/* Insights Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {insights.map((insight, index) => (
          <div
            key={index}
            className={`bg-gradient-to-r ${getInsightStyle(insight.type)} backdrop-blur-xl border rounded-2xl p-6 hover:scale-105 transition-all duration-300`}
          >
            <div className="flex items-start space-x-4">
              <div className="text-3xl">{insight.icon}</div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-2">{insight.title}</h3>
                <p className="text-white/70 text-sm leading-relaxed">{insight.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Feature Usage */}
      {(statistics.featureUsage.codeLanguages.length > 0 || statistics.featureUsage.attachmentsUsed.images > 0) && (
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-white mb-6">Uso de Recursos</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Code Languages */}
            {statistics.featureUsage.codeLanguages.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-white/80 mb-4">Linguagens de Programação</h4>
                <div className="space-y-3">
                  {statistics.featureUsage.codeLanguages.slice(0, 5).map((lang, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-white/70 text-sm">{lang.language}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 h-2 bg-white/10 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                            style={{ width: `${(lang.count / statistics.featureUsage.codeLanguages[0].count) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-white font-medium text-sm">{lang.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Attachments */}
            {statistics.featureUsage.attachmentsUsed.images > 0 && (
              <div>
                <h4 className="text-md font-medium text-white/80 mb-4">Anexos Utilizados</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">Imagens</span>
                    <span className="text-white font-medium">{statistics.featureUsage.attachmentsUsed.images}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">PDFs</span>
                    <span className="text-white font-medium">{statistics.featureUsage.attachmentsUsed.pdfs}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Word Cloud Preview */}
      {statistics.mostUsedWords.length > 0 && (
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-white mb-6">Palavras Mais Utilizadas</h3>
          
          <div className="flex flex-wrap gap-3">
            {statistics.mostUsedWords.slice(0, 20).map((word, index) => {
              const size = Math.max(12, Math.min(24, (word.count / statistics.mostUsedWords[0].count) * 20 + 12));
              const opacity = Math.max(0.4, (word.count / statistics.mostUsedWords[0].count));
              
              return (
                <span
                  key={index}
                  className="px-3 py-1 bg-white/10 rounded-full text-white hover:bg-white/20 transition-all duration-300 cursor-pointer"
                  style={{ 
                    fontSize: `${size}px`,
                    opacity: opacity
                  }}
                  title={`${word.word}: ${word.count} vezes`}
                >
                  {word.word}
                </span>
              );
            })}
          </div>
        </div>
      )}

      {/* Summary Stats */}
      <div className="bg-gradient-to-r from-indigo-500/10 to-purple-500/10 backdrop-blur-xl border border-indigo-400/20 rounded-2xl p-6">
        <h3 className="text-lg font-semibold text-white mb-6">Resumo Geral</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-indigo-300">
              {Math.ceil((Date.now() - statistics.firstMessageAt) / (1000 * 60 * 60 * 24))}
            </div>
            <div className="text-sm text-white/60">Dias de uso</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-300">
              {((statistics.totalWords / statistics.totalMessages) || 0).toFixed(1)}
            </div>
            <div className="text-sm text-white/60">Palavras por mensagem</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-pink-300">
              ${(statistics.totalCost / Math.max(statistics.totalChats, 1)).toFixed(3)}
            </div>
            <div className="text-sm text-white/60">Custo médio por chat</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-cyan-300">
              {((statistics.totalMessages / Math.max(1, Math.ceil((Date.now() - statistics.firstMessageAt) / (1000 * 60 * 60 * 24)))) || 0).toFixed(1)}
            </div>
            <div className="text-sm text-white/60">Mensagens por dia</div>
          </div>
        </div>
      </div>
    </div>
  );
}
