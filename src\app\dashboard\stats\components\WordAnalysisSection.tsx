'use client';

import { GeneralStatistics } from '@/lib/generalStatisticsService';

interface WordAnalysisSectionProps {
  statistics: GeneralStatistics;
}

export default function WordAnalysisSection({ statistics }: WordAnalysisSectionProps) {
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const userWordsPercentage = (statistics.totalUserWords / Math.max(statistics.totalWords, 1)) * 100;
  const aiWordsPercentage = (statistics.totalAssistantWords / Math.max(statistics.totalWords, 1)) * 100;

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent mb-2">
          Aná<PERSON><PERSON> de Palavras
        </h2>
        <p className="text-white/70 text-lg">
          Distribuição e padrões de comunicação
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Word Distribution Chart */}
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
          <h3 className="text-xl font-semibold text-white mb-6">Distribuição de Palavras</h3>
          
          <div className="space-y-6">
            {/* Visual Bar Chart */}
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-emerald-300">Usuário</span>
                  <span className="text-sm text-white/60">{userWordsPercentage.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-white/10 rounded-full h-3">
                  <div 
                    className="bg-gradient-to-r from-emerald-500 to-teal-500 h-3 rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${userWordsPercentage}%` }}
                  ></div>
                </div>
                <div className="text-right mt-1">
                  <span className="text-lg font-bold text-emerald-400">
                    {formatNumber(statistics.totalUserWords)}
                  </span>
                  <span className="text-sm text-white/60 ml-1">palavras</span>
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-violet-300">IA</span>
                  <span className="text-sm text-white/60">{aiWordsPercentage.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-white/10 rounded-full h-3">
                  <div 
                    className="bg-gradient-to-r from-violet-500 to-purple-500 h-3 rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${aiWordsPercentage}%` }}
                  ></div>
                </div>
                <div className="text-right mt-1">
                  <span className="text-lg font-bold text-violet-400">
                    {formatNumber(statistics.totalAssistantWords)}
                  </span>
                  <span className="text-sm text-white/60 ml-1">palavras</span>
                </div>
              </div>
            </div>

            {/* Total */}
            <div className="pt-4 border-t border-white/10">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-white">Total</span>
                <span className="text-2xl font-bold text-blue-400">
                  {formatNumber(statistics.totalWords)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Word Efficiency Metrics */}
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
          <h3 className="text-xl font-semibold text-white mb-6">Métricas de Eficiência</h3>
          
          <div className="space-y-6">
            {/* Average words per message */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-emerald-500/10 rounded-xl border border-emerald-500/20">
                <div className="text-2xl font-bold text-emerald-400 mb-1">
                  {(statistics.totalUserWords / Math.max(statistics.totalUserMessages, 1)).toFixed(1)}
                </div>
                <div className="text-xs text-white/60">Palavras/mensagem (Usuário)</div>
              </div>
              
              <div className="text-center p-4 bg-violet-500/10 rounded-xl border border-violet-500/20">
                <div className="text-2xl font-bold text-violet-400 mb-1">
                  {(statistics.totalAssistantWords / Math.max(statistics.totalAssistantMessages, 1)).toFixed(1)}
                </div>
                <div className="text-xs text-white/60">Palavras/resposta (IA)</div>
              </div>
            </div>

            {/* Communication ratio */}
            <div className="p-4 bg-blue-500/10 rounded-xl border border-blue-500/20">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">
                  {(statistics.totalAssistantWords / Math.max(statistics.totalUserWords, 1)).toFixed(1)}x
                </div>
                <div className="text-sm text-white/60">
                  Proporção de resposta da IA
                </div>
                <div className="text-xs text-white/40 mt-1">
                  A IA escreve {(statistics.totalAssistantWords / Math.max(statistics.totalUserWords, 1)).toFixed(1)} palavras para cada palavra sua
                </div>
              </div>
            </div>

            {/* Most used words preview */}
            {statistics.mostUsedWords.length > 0 && (
              <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                <div className="text-sm font-medium text-white mb-3">Palavras Mais Usadas</div>
                <div className="flex flex-wrap gap-2">
                  {statistics.mostUsedWords.slice(0, 8).map((word, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-gradient-to-r from-pink-500/20 to-purple-500/20 border border-pink-500/30 rounded-full text-xs text-pink-200"
                    >
                      {word.word} ({word.count})
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
