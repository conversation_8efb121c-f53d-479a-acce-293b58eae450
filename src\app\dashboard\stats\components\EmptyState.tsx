'use client';

export default function EmptyState() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 text-center max-w-lg mx-auto p-6">
        {/* Empty state illustration */}
        <div className="mb-8">
          <div className="w-32 h-32 mx-auto bg-white/5 rounded-full flex items-center justify-center border border-white/10">
            <svg className="w-16 h-16 text-white/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
        </div>

        {/* Empty state content */}
        <div className="space-y-6">
          <div>
            <h2 className="text-3xl font-bold text-white mb-4">Nenhuma conversa encontrada</h2>
            <p className="text-white/60 text-lg leading-relaxed">
              Você ainda não tem conversas para analisar. Comece uma nova conversa para ver suas estatísticas aqui!
            </p>
          </div>

          {/* Features preview */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6 text-left">
            <h3 className="text-lg font-semibold text-white mb-4">O que você verá aqui:</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span className="text-white/70 text-sm">Métricas detalhadas de suas conversas</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span className="text-white/70 text-sm">Padrões de atividade e heatmaps</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-white/70 text-sm">Análise de performance e eficiência</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-pink-400 rounded-full"></div>
                <span className="text-white/70 text-sm">Insights inteligentes personalizados</span>
              </div>
            </div>
          </div>

          {/* Action button */}
          <div className="space-y-3">
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="w-full px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 hover:scale-105 shadow-lg"
            >
              Começar Nova Conversa
            </button>
            
            <p className="text-white/40 text-xs">
              Suas estatísticas aparecerão automaticamente após suas primeiras mensagens
            </p>
          </div>
        </div>

        {/* Floating decorative elements */}
        <div className="absolute -top-10 -left-10 w-4 h-4 bg-purple-500/20 rounded-full animate-float"></div>
        <div className="absolute -top-5 right-10 w-3 h-3 bg-pink-500/20 rounded-full animate-float delay-1000"></div>
        <div className="absolute bottom-10 -left-5 w-5 h-5 bg-blue-500/20 rounded-full animate-float delay-500"></div>
        <div className="absolute bottom-5 right-5 w-2 h-2 bg-indigo-500/20 rounded-full animate-float delay-1500"></div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
}
