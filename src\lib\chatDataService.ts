import { ChatData } from './types/chat';

/**
 * Service for managing chat data through API routes (avoiding CORS issues)
 */
class ChatDataService {
  
  /**
   * Obtém os dados de um chat através da API
   */
  async getChatData(userId: string, chatId: string): Promise<ChatData | null> {
    try {
      const response = await fetch(`/api/chat/data?userId=${userId}&chatId=${chatId}`);
      const data = await response.json();

      if (data.success) {
        return data.chatData;
      } else if (response.status === 404) {
        return null; // Chat não encontrado
      } else {
        throw new Error(data.error || 'Failed to get chat data');
      }
    } catch (error) {
      console.error('Error getting chat data:', error);
      throw error;
    }
  }

  /**
   * Atualiza os dados de um chat através da API
   */
  async updateChatData(userId: string, chatId: string, chatData: ChatData): Promise<void> {
    try {
      const response = await fetch('/api/chat/data', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          chatId,
          chatData,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to update chat data');
      }
    } catch (error) {
      console.error('Error updating chat data:', error);
      throw error;
    }
  }
}

// Exportar uma instância singleton do serviço
export const chatDataService = new ChatDataService();
export default chatDataService;
