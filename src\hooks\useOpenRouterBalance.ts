import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBalance } from '@/contexts/BalanceContext';
import { getUserAPIEndpoints } from '@/lib/settingsService';

interface OpenRouterCredits {
  total_credits: number;
  total_usage: number;
  balance: number;
}

export const useOpenRouterBalance = () => {
  const [balance, setBalance] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { registerRefreshCallback } = useBalance();

  const fetchBalance = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // Get user's API endpoints
      const endpoints = await getUserAPIEndpoints(user.uid);

      // Find OpenRouter endpoint
      const openRouterEndpoint = endpoints.find(
        endpoint => endpoint.name.toLowerCase().includes('openrouter') && endpoint.isActive
      );

      if (!openRouterEndpoint) {
        setError('OpenRouter endpoint not configured');
        setBalance(null);
        return;
      }

      // Fetch credits from OpenRouter
      const response = await fetch('/api/openrouter/credits', {
        method: 'GET',
        headers: {
          'x-api-key': openRouterEndpoint.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch OpenRouter credits');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch credits');
      }

      setBalance(result.data.balance);
    } catch (err) {
      console.error('Error fetching OpenRouter balance:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setBalance(null);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchBalance();
  }, [fetchBalance]);

  // Registrar o callback de refresh no contexto
  useEffect(() => {
    registerRefreshCallback(fetchBalance);
  }, [registerRefreshCallback, fetchBalance]);

  return {
    balance,
    loading,
    error,
    refetch: fetchBalance
  };
};
