'use client';

import { useState, useEffect } from 'react';
import { adminService, SiteMaintenanceSettings } from '@/lib/adminService';

interface UseMaintenanceModeReturn {
  isMaintenanceActive: boolean;
  maintenanceMessage: string;
  isLoading: boolean;
  error: string | null;
  bypassMaintenance: () => void;
  isBypassed: boolean;
}

export const useMaintenanceMode = (): UseMaintenanceModeReturn => {
  const [isMaintenanceActive, setIsMaintenanceActive] = useState(false);
  const [maintenanceMessage, setMaintenanceMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBypassed, setIsBypassed] = useState(false);

  useEffect(() => {
    checkMaintenanceStatus();
    
    // Verificar se já foi feito bypass na sessão atual
    const bypassed = sessionStorage.getItem('maintenance_bypassed');
    if (bypassed === 'true') {
      setIsBypassed(true);
    }
  }, []);

  const checkMaintenanceStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const settings = await adminService.getMaintenanceSettings();
      
      if (settings) {
        setIsMaintenanceActive(settings.isActive);
        setMaintenanceMessage(settings.message || 'Site em manutenção. Tente novamente mais tarde.');
      } else {
        setIsMaintenanceActive(false);
        setMaintenanceMessage('');
      }
    } catch (err) {
      console.error('Erro ao verificar status de manutenção:', err);
      setError('Erro ao verificar status de manutenção');
      // Em caso de erro, assumir que não está em manutenção para não bloquear o acesso
      setIsMaintenanceActive(false);
    } finally {
      setIsLoading(false);
    }
  };

  const bypassMaintenance = () => {
    setIsBypassed(true);
    // Salvar no sessionStorage para manter durante a sessão
    sessionStorage.setItem('maintenance_bypassed', 'true');
  };

  return {
    isMaintenanceActive: isMaintenanceActive && !isBypassed,
    maintenanceMessage,
    isLoading,
    error,
    bypassMaintenance,
    isBypassed
  };
};
