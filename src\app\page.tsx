'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import MaintenanceWrapper from '@/components/MaintenanceWrapper';

export default function HomePage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (user) {
        router.push('/dashboard');
      } else {
        router.push('/auth');
      }
    }
  }, [user, loading, router]);

  return (
    <MaintenanceWrapper>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
          <h1 className="mt-6 text-3xl font-bold text-white">RafthorIA</h1>
          <p className="mt-2 text-indigo-300">Carregando...</p>
        </div>
      </div>
    </MaintenanceWrapper>
  );
}
