'use client';

import { useState } from 'react';
import { ChatMessage } from '@/lib/types/chat';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeHighlight from 'rehype-highlight';
import rehypeKatex from 'rehype-katex';
import 'highlight.js/styles/github-dark.css';
import 'katex/dist/katex.min.css';

// Type definitions for React Markdown components
interface MarkdownComponentProps {
  children?: React.ReactNode;
  className?: string;
  inline?: boolean;
  href?: string;
  [key: string]: any;
}

interface MessageBubbleProps {
  message: ChatMessage;
  onAction: (messageId: string, action: 'delete' | 'edit' | 'copy' | 'regenerate' | 'favorite' | 'unfavorite') => void;
  onEditSave?: (messageId: string, content: string) => void;
  isFavorite?: boolean;
}

const MessageBubble = ({ message, onAction, onEditSave, isFavorite = false }: MessageBubbleProps) => {
  const isUser = message.role === 'user';
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const [showCopyNotification, setShowCopyNotification] = useState(false);

  // Calcular altura aproximada baseada no conteúdo
  const calculateTextareaHeight = () => {
    const lines = editContent.split('\n');
    const lineHeight = 30; // Altura por linha em pixels (reduzida um pouco)
    const padding = 48; // Padding top + bottom (reduzido um pouco)
    const minHeight = 350; // Altura mínima menor, mas ainda grande

    // Contar linhas reais + linhas que quebram por largura
    let totalLines = 0;
    const avgCharsPerLine = 100; // Caracteres por linha ajustado

    lines.forEach((line: string) => {
      if (line.length === 0) {
        totalLines += 1; // Linha vazia
      } else {
        totalLines += Math.max(1, Math.ceil(line.length / avgCharsPerLine));
      }
    });

    const calculatedHeight = (totalLines * lineHeight) + padding;
    return Math.max(minHeight, Math.min(calculatedHeight, 600)); // Máximo de 600px (reduzido)
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleAction = async (action: 'delete' | 'edit' | 'copy' | 'regenerate' | 'favorite' | 'unfavorite') => {
    if (action === 'copy') {
      try {
        await navigator.clipboard.writeText(message.content);
        setShowCopyNotification(true);
        setTimeout(() => setShowCopyNotification(false), 2000);
      } catch (error) {
        console.error('Erro ao copiar:', error);
      }
    } else if (action === 'edit') {
      setIsEditing(true);
      setEditContent(message.content);
    } else {
      onAction(message.id, action);
    }
  };

  const handleSaveEdit = () => {
    if (onEditSave && editContent.trim() !== message.content) {
      onEditSave(message.id, editContent.trim());
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent(message.content);
  };

  // Função para pré-processar conteúdo LaTeX
  const preprocessLatex = (content: string): string => {
    // Remove comandos de documento LaTeX não suportados pelo KaTeX
    const unsupportedCommands = [
      /\\documentclass\{[^}]*\}/g,
      /\\usepackage\{[^}]*\}/g,
      /\\begin\{document\}/g,
      /\\end\{document\}/g,
      /\\maketitle/g,
      /\\tableofcontents/g,
      /\\newpage/g,
      /\\clearpage/g,
      /\\pagebreak/g,
    ];

    let processedContent = content;

    // Remove comandos não suportados
    unsupportedCommands.forEach(regex => {
      processedContent = processedContent.replace(regex, '');
    });

    // Converte comandos de seção para markdown
    processedContent = processedContent
      .replace(/\\section\*?\{([^}]+)\}/g, '## $1')
      .replace(/\\subsection\*?\{([^}]+)\}/g, '### $1')
      .replace(/\\subsubsection\*?\{([^}]+)\}/g, '#### $1')
      .replace(/\\paragraph\{([^}]+)\}/g, '##### $1')
      .replace(/\\subparagraph\{([^}]+)\}/g, '###### $1');

    // Converte comandos de formatação básica
    processedContent = processedContent
      .replace(/\\textbf\{([^}]+)\}/g, '**$1**')
      .replace(/\\textit\{([^}]+)\}/g, '*$1*')
      .replace(/\\emph\{([^}]+)\}/g, '*$1*')
      .replace(/\\underline\{([^}]+)\}/g, '<u>$1</u>')
      .replace(/\\texttt\{([^}]+)\}/g, '`$1`');

    // Converte listas LaTeX para markdown
    processedContent = processedContent
      .replace(/\\begin\{itemize\}/g, '')
      .replace(/\\end\{itemize\}/g, '')
      .replace(/\\begin\{enumerate\}/g, '')
      .replace(/\\end\{enumerate\}/g, '')
      .replace(/\\item\s+/g, '- ');

    // Converte ambientes matemáticos especiais (usando função auxiliar para multiline)
    const convertMathEnvironments = (text: string) => {
      const environments = [
        { start: '\\begin{theorem}', end: '\\end{theorem}', label: '**Teorema:**' },
        { start: '\\begin{definition}', end: '\\end{definition}', label: '**Definição:**' },
        { start: '\\begin{lemma}', end: '\\end{lemma}', label: '**Lema:**' },
        { start: '\\begin{proof}', end: '\\end{proof}', label: '**Demonstração:**' },
        { start: '\\begin{example}', end: '\\end{example}', label: '**Exemplo:**' },
        { start: '\\begin{remark}', end: '\\end{remark}', label: '**Observação:**' }
      ];

      let result = text;
      environments.forEach(env => {
        const startIndex = result.indexOf(env.start);
        const endIndex = result.indexOf(env.end);
        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
          const content = result.substring(startIndex + env.start.length, endIndex).trim();
          const replacement = `> ${env.label} ${content}`;
          result = result.substring(0, startIndex) + replacement + result.substring(endIndex + env.end.length);
        }
      });
      return result;
    };

    processedContent = convertMathEnvironments(processedContent);

    // Converte comandos matemáticos específicos
    processedContent = processedContent
      .replace(/\\qed/g, '∎')
      .replace(/\\therefore/g, '∴')
      .replace(/\\because/g, '∵')
      .replace(/\\implies/g, '⟹')
      .replace(/\\iff/g, '⟺');

    // Remove linhas vazias excessivas
    processedContent = processedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

    return processedContent.trim();
  };

  return (
    <div
      className={`flex mb-4 sm:mb-6 group ${isEditing ? 'justify-center' : (isUser ? 'justify-end' : 'justify-start')} w-full max-w-full`}
    >
      <div className={`flex items-start space-x-2 sm:space-x-4 ${isEditing ? 'w-full max-w-none' : 'max-w-[90%] sm:max-w-[75%]'} relative ${isUser ? 'flex-row-reverse space-x-reverse' : ''} break-words overflow-hidden`}>
        {/* Avatar */}
        <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg ${
          isUser
            ? 'bg-gradient-to-br from-green-500 to-emerald-600 shadow-green-500/30'
            : 'bg-gradient-to-br from-blue-500 to-cyan-600 shadow-blue-500/30'
        }`}>
          {isUser ? (
            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
            </svg>
          ) : (
            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"/>
            </svg>
          )}
        </div>

        {/* Message Content */}
        <div className="flex flex-col space-y-1.5 sm:space-y-2 flex-1 min-w-0 overflow-hidden">
          <div className={`relative px-3 sm:px-6 py-3 sm:py-4 rounded-xl sm:rounded-2xl backdrop-blur-sm border shadow-lg overflow-hidden ${
            isUser
              ? 'bg-gradient-to-br from-green-900/80 to-emerald-900/80 border-green-600/30 shadow-green-900/50'
              : 'bg-gradient-to-br from-blue-900/80 to-blue-800/80 border-blue-600/30 shadow-blue-900/50'
          }`}>
            {/* Neumorphic glow effect */}
            <div className={`absolute inset-0 rounded-2xl ${
              isUser
                ? 'shadow-[inset_0_1px_0_rgba(34,197,94,0.1),0_0_20px_rgba(34,197,94,0.15)]'
                : 'shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]'
            }`}></div>
            
            <div
              className={`relative z-10 leading-normal message-content message-bubble-content ${
                isUser ? 'text-slate-100' : 'text-slate-200'
              }`}
              style={{
                fontSize: 'var(--chat-font-size)',
                fontFamily: 'var(--chat-font-family)',
                maxWidth: '100%',
                overflowWrap: 'anywhere',
                wordBreak: 'break-word',
                userSelect: 'text',
                WebkitUserSelect: 'text',
                MozUserSelect: 'text',
                msUserSelect: 'text'
              }}
            >
              {/* Attachments */}
              {message.attachments && message.attachments.length > 0 && (
                <div className="mb-4 space-y-2">
                  {message.attachments.map((attachment) => (
                    <div key={attachment.id} className="border border-slate-600/30 rounded-lg overflow-hidden">
                      {attachment.type === 'image' ? (
                        <div className="relative">
                          <img
                            src={attachment.url}
                            alt={attachment.filename}
                            className="w-full max-w-sm rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                            onClick={() => window.open(attachment.url, '_blank')}
                          />
                          <div className="absolute bottom-2 left-2 bg-black/70 backdrop-blur-sm rounded px-2 py-1">
                            <span className="text-xs text-white">{attachment.filename}</span>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-3 p-3 bg-slate-700/30 hover:bg-slate-600/30 transition-colors cursor-pointer"
                             onClick={() => window.open(attachment.url, '_blank')}>
                          <div className="flex-shrink-0">
                            <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-slate-200 truncate">{attachment.filename}</p>
                            <p className="text-xs text-slate-400">PDF • {(attachment.size / 1024 / 1024).toFixed(1)} MB</p>
                          </div>
                          <div className="flex-shrink-0">
                            <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {isEditing ? (
                <div className="space-y-4 w-full max-w-none p-6 bg-slate-900/30 rounded-xl border border-slate-700/30">
                  <textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    style={{
                      height: `${calculateTextareaHeight()}px`,
                      transition: 'height 0.2s ease',
                      width: '100%',
                      maxWidth: 'none',
                      fontSize: 'var(--chat-font-size)',
                      fontFamily: 'var(--chat-font-family)'
                    }}
                    className="w-full max-w-none p-6 bg-slate-800/50 border border-slate-600/30 rounded-lg text-white resize-none focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 leading-8 text-base"
                    placeholder="Digite sua mensagem..."
                    autoFocus
                  />
                  <div className="flex justify-end space-x-4 mt-4">
                    <button
                      onClick={handleCancelEdit}
                      className="px-6 py-3 text-lg bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 rounded-lg transition-colors font-medium"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={handleSaveEdit}
                      className="px-6 py-3 text-lg bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium shadow-lg hover:shadow-purple-500/25"
                    >
                      Salvar
                    </button>
                  </div>
                </div>
              ) : (
                <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkMath]}
                rehypePlugins={[rehypeHighlight, rehypeKatex]}
                components={{
                  // Headings com estilo matemático aprimorado
                  h1: ({ children }: MarkdownComponentProps) => (
                    <h1 className="text-2xl font-bold mb-4 text-blue-100 border-b border-blue-600/50 pb-2">
                      {children}
                    </h1>
                  ),
                  h2: ({ children }: MarkdownComponentProps) => (
                    <h2 className="text-xl font-semibold mb-3 text-blue-100 border-l-4 border-blue-500 pl-3">
                      {children}
                    </h2>
                  ),
                  h3: ({ children }: MarkdownComponentProps) => (
                    <h3 className="text-lg font-medium mb-2 text-blue-200 border-l-2 border-cyan-500 pl-2">
                      {children}
                    </h3>
                  ),
                  h4: ({ children }: MarkdownComponentProps) => (
                    <h4 className="text-base font-medium mb-2 text-blue-200">
                      {children}
                    </h4>
                  ),
                  h5: ({ children }: MarkdownComponentProps) => (
                    <h5 className="text-sm font-medium mb-1 text-blue-300">
                      {children}
                    </h5>
                  ),
                  h6: ({ children }: MarkdownComponentProps) => (
                    <h6 className="text-sm font-medium mb-1 text-blue-300">
                      {children}
                    </h6>
                  ),

                  // Paragraphs - usando div para evitar problemas de nesting
                  p: ({ children }: MarkdownComponentProps) => (
                    <div className="mb-3 last:mb-0 whitespace-pre-wrap">
                      {children}
                    </div>
                  ),

                  // Lists
                  ul: ({ children }: MarkdownComponentProps) => (
                    <ul className="list-disc list-inside mb-3 space-y-1 ml-4">
                      {children}
                    </ul>
                  ),
                  ol: ({ children }: MarkdownComponentProps) => (
                    <ol className="list-decimal list-inside mb-3 space-y-1 ml-4">
                      {children}
                    </ol>
                  ),
                  li: ({ children }: MarkdownComponentProps) => (
                    <li className="text-blue-200">
                      {children}
                    </li>
                  ),

                  // Code blocks
                  code: ({ inline, className, children, ...props }: MarkdownComponentProps) => {
                    const match = /language-(\w+)/.exec(className || '');
                    return !inline ? (
                      <div className="relative mb-4">
                        <div className="bg-blue-950/80 rounded-lg border border-blue-700/50 overflow-hidden">
                          {match && (
                            <div className="bg-blue-900/80 px-4 py-2 text-xs text-blue-300 border-b border-blue-700/50 font-mono">
                              {match[1]}
                            </div>
                          )}
                          <pre className="p-4 overflow-x-auto">
                            <code className={className} {...props}>
                              {children}
                            </code>
                          </pre>
                        </div>
                      </div>
                    ) : (
                      <code className="bg-blue-900/60 text-blue-200 px-2 py-1 rounded text-sm font-mono border border-blue-700/30" {...props}>
                        {children}
                      </code>
                    );
                  },

                  // Blockquotes com estilo matemático
                  blockquote: ({ children }: MarkdownComponentProps) => (
                    <blockquote className="border-l-4 border-blue-500/50 pl-4 py-2 mb-3 bg-blue-900/30 rounded-r-lg italic text-blue-200 relative">
                      <div className="absolute -left-2 top-2 w-4 h-4 bg-blue-500/20 rounded-full"></div>
                      {children}
                    </blockquote>
                  ),

                  // Links
                  a: ({ href, children }: MarkdownComponentProps) => (
                    <a
                      href={href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-400 hover:text-blue-300 underline decoration-blue-500/50 hover:decoration-blue-400 transition-colors"
                    >
                      {children}
                    </a>
                  ),

                  // Tables
                  table: ({ children }: MarkdownComponentProps) => (
                    <div className="overflow-x-auto mb-4">
                      <table className="min-w-full border border-blue-700/50 rounded-lg overflow-hidden">
                        {children}
                      </table>
                    </div>
                  ),
                  thead: ({ children }: MarkdownComponentProps) => (
                    <thead className="bg-blue-900/60">
                      {children}
                    </thead>
                  ),
                  tbody: ({ children }: MarkdownComponentProps) => (
                    <tbody className="bg-blue-950/20">
                      {children}
                    </tbody>
                  ),
                  tr: ({ children }: MarkdownComponentProps) => (
                    <tr className="border-b border-blue-700/30 hover:bg-blue-900/30 transition-colors">
                      {children}
                    </tr>
                  ),
                  th: ({ children }: MarkdownComponentProps) => (
                    <th className="px-4 py-2 text-left text-blue-200 font-medium">
                      {children}
                    </th>
                  ),
                  td: ({ children }: MarkdownComponentProps) => (
                    <td className="px-4 py-2 text-blue-200">
                      {children}
                    </td>
                  ),

                  // Horizontal rule
                  hr: () => (
                    <hr className="my-6 border-slate-600/50" />
                  ),

                  // Strong and emphasis
                  strong: ({ children }: MarkdownComponentProps) => (
                    <strong className="font-semibold text-slate-100">
                      {children}
                    </strong>
                  ),
                  em: ({ children }: MarkdownComponentProps) => (
                    <em className="italic text-slate-200">
                      {children}
                    </em>
                  ),

                  // Strikethrough
                  del: ({ children }: MarkdownComponentProps) => (
                    <del className="line-through text-slate-400">
                      {children}
                    </del>
                  ),
                }}
                >
                  {preprocessLatex(message.content)}
                </ReactMarkdown>
              )}
            </div>
          </div>

          {/* Timestamp and Actions */}
          <div className={`flex items-center justify-between ${isUser ? 'flex-row-reverse' : ''}`}>
            <span className={`text-xs ${
              isUser
                ? 'text-green-300/60'
                : 'text-blue-400/60'
            }`}>
              {formatTime(message.timestamp)}
            </span>

            {/* Message Actions */}
            <div className={`message-actions flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${isUser ? 'mr-2' : 'ml-2'}`}>
              {/* Copy Button */}
              <button
                onClick={() => handleAction('copy')}
                className={`p-1.5 rounded-md transition-all duration-200 hover:shadow-lg hover:scale-105 ${
                  isUser
                    ? 'bg-green-800/60 hover:bg-green-700/80 text-green-400 hover:text-green-300 hover:shadow-green-500/10'
                    : 'bg-blue-800/60 hover:bg-blue-700/80 text-blue-400 hover:text-blue-300 hover:shadow-blue-500/10'
                }`}
                title="Copiar"
              >
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </button>

              {/* Edit Button */}
              <button
                onClick={() => handleAction('edit')}
                className={`p-1.5 rounded-md transition-all duration-200 hover:shadow-lg hover:scale-105 ${
                  isUser
                    ? 'bg-green-800/60 hover:bg-green-700/80 text-green-400 hover:text-green-300 hover:shadow-green-500/10'
                    : 'bg-blue-800/60 hover:bg-blue-700/80 text-blue-400 hover:text-blue-300 hover:shadow-blue-500/10'
                }`}
                title="Editar"
              >
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>

              {/* Favorite Button */}
              <button
                onClick={() => handleAction(isFavorite ? 'unfavorite' : 'favorite')}
                className={`p-1.5 rounded-md transition-all duration-200 hover:shadow-lg hover:scale-105 ${
                  isFavorite
                    ? 'bg-yellow-800/60 hover:bg-yellow-700/80 text-yellow-400 hover:text-yellow-300 hover:shadow-yellow-500/10'
                    : isUser
                    ? 'bg-green-800/60 hover:bg-green-700/80 text-green-400 hover:text-green-300 hover:shadow-green-500/10'
                    : 'bg-blue-800/60 hover:bg-blue-700/80 text-blue-400 hover:text-blue-300 hover:shadow-blue-500/10'
                }`}
                title={isFavorite ? "Remover dos favoritos" : "Adicionar aos favoritos"}
              >
                {isFavorite ? (
                  <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                  </svg>
                ) : (
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                )}
              </button>

              {/* Regenerate Button (only for user messages) */}
              {isUser && (
                <button
                  onClick={() => handleAction('regenerate')}
                  className="p-1.5 rounded-md bg-green-800/60 hover:bg-green-700/80 text-green-400 hover:text-green-300 transition-all duration-200 hover:shadow-lg hover:shadow-green-500/10 hover:scale-105"
                  title="Regenerar resposta"
                >
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
              )}

              {/* Delete Button */}
              <button
                onClick={() => handleAction('delete')}
                className="p-1.5 rounded-md bg-red-900/40 hover:bg-red-800/60 text-red-500 hover:text-red-300 transition-all duration-200 hover:shadow-lg hover:shadow-red-500/10"
                title="Deletar"
              >
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Copy Notification */}
        {showCopyNotification && (
          <div className="absolute top-2 right-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-xl text-sm shadow-lg animate-fade-in-out border border-green-500/30 backdrop-blur-sm">
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>Copiado!</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;
