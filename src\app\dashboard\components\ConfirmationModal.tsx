'use client';

import { useEffect, useState } from 'react';

interface ConfirmationModalProps {
  isOpen: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  destructive?: boolean; // Nova prop para ações destrutivas
}

const ConfirmationModal = ({
  isOpen,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText = 'Confirmar',
  cancelText = 'Cancelar',
  destructive = false
}: ConfirmationModalProps) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || !isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center transition-opacity duration-300 z-[60]"
      onClick={onCancel}
    >
      <div
        className={`glass-effect rounded-xl shadow-2xl p-0 w-full max-w-md transform transition-all duration-300 ${
          destructive
            ? 'border border-red-500/30 shadow-red-500/20'
            : 'border border-blue-500/30 shadow-blue-500/20'
        }`}
        onClick={(e) => e.stopPropagation()}
        style={{
          background: destructive
            ? 'rgba(15, 23, 42, 0.95)'
            : 'rgba(30, 58, 138, 0.95)',
          backdropFilter: 'blur(20px)',
        }}
      >
        {/* Modal Header */}
        <div className={`flex justify-between items-center p-6 border-b ${destructive ? 'border-red-700/30' : 'border-blue-700/30'}`}>
          <h2 className="text-xl font-semibold text-white text-glow-sm flex items-center">
            {destructive ? (
              <svg className="w-6 h-6 mr-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            ) : (
              <svg className="w-6 h-6 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            )}
            {title}
          </h2>
          <button
            onClick={onCancel}
            className={`transition-colors duration-200 p-1 rounded-lg ${
              destructive
                ? 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                : 'text-blue-300 hover:text-white hover:bg-blue-700/50'
            }`}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className="p-6">
          <p className={`text-base leading-relaxed ${
            destructive ? 'text-slate-300' : 'text-blue-100'
          }`}>
            {message}
          </p>
        </div>

        {/* Modal Footer */}
        <div className={`flex justify-end space-x-3 p-6 border-t ${destructive ? 'border-red-700/30' : 'border-blue-700/30'}`}>
          <button
            onClick={onCancel}
            className={`px-6 py-2.5 rounded-lg transition-all duration-200 font-medium ${
              destructive
                ? 'bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-white'
                : 'bg-blue-700/50 hover:bg-blue-600/50 text-blue-200 hover:text-white'
            }`}
          >
            {cancelText}
          </button>
          <button
            onClick={() => {
              onConfirm();
              onCancel();
            }}
            className={`px-6 py-2.5 text-white rounded-lg transition-all duration-200 font-medium shadow-lg ${
              destructive
                ? 'bg-red-600 hover:bg-red-700 hover:shadow-red-500/25'
                : 'bg-blue-600 hover:bg-blue-700 hover:shadow-blue-500/25'
            }`}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
