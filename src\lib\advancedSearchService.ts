import { AIModel } from './types/chat';

export interface SearchResult {
  model: AIModel;
  score: number;
  matchedFields: string[];
  highlightedName?: string;
  highlightedDescription?: string;
}

export interface SearchOptions {
  fuzzyThreshold?: number; // 0-1, lower = more strict
  maxResults?: number;
  includeArchitecture?: boolean;
  includeModalidades?: boolean;
  boostFavorites?: boolean;
  boostRecent?: boolean;
}

export interface SearchSuggestion {
  text: string;
  type: 'model' | 'provider' | 'category' | 'correction' | 'tag' | 'feature';
  score: number;
  description?: string;
}

export interface ModelIndex {
  id: string;
  searchableText: string;
  tags: string[];
  provider: string;
  features: string[];
  priceCategory: string;
  contextCategory: string;
}

/**
 * Serviço avançado de busca para modelos de IA
 * Implementa busca fuzzy, busca por múltiplos campos, e sugestões inteligentes
 */
export class AdvancedSearchService {
  private searchHistory: string[] = [];
  private popularSearches: Map<string, number> = new Map();
  private modelIndex: Map<string, ModelIndex> = new Map();
  private lastIndexUpdate: number = 0;
  
  /**
   * Calcula a distância de Levenshtein entre duas strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }
  
  /**
   * Calcula similaridade fuzzy entre duas strings (0-1)
   */
  private fuzzyMatch(str1: string, str2: string): number {
    if (str1 === str2) return 1;
    if (str1.length === 0 || str2.length === 0) return 0;
    
    const maxLength = Math.max(str1.length, str2.length);
    const distance = this.levenshteinDistance(str1.toLowerCase(), str2.toLowerCase());
    return 1 - (distance / maxLength);
  }
  
  /**
   * Verifica se uma string contém todas as palavras de busca
   */
  private containsAllWords(text: string, searchWords: string[]): boolean {
    const lowerText = text.toLowerCase();
    return searchWords.every(word => lowerText.includes(word.toLowerCase()));
  }
  
  /**
   * Extrai palavras-chave de uma string de busca
   */
  private extractKeywords(searchTerm: string): string[] {
    return searchTerm
      .toLowerCase()
      .split(/[\s\-_\/]+/)
      .filter(word => word.length > 0)
      .filter(word => !['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'].includes(word));
  }
  
  /**
   * Extrai o provedor do ID do modelo (ex: "openai/gpt-4" -> "openai")
   */
  private extractProvider(modelId: string): string {
    const parts = modelId.split('/');
    return parts.length > 1 ? parts[0] : '';
  }
  
  /**
   * Extrai tags/categorias do modelo baseado em seu nome e descrição
   */
  private extractModelTags(model: AIModel): string[] {
    const tags: string[] = [];
    const text = `${model.name} ${model.description || ''}`.toLowerCase();

    // Tags baseadas em funcionalidades
    if (text.includes('vision') || text.includes('image') || text.includes('visual') || text.includes('multimodal')) {
      tags.push('multimodal', 'vision', 'image', 'visual');
    }
    if (text.includes('code') || text.includes('programming') || text.includes('coding') || text.includes('developer')) {
      tags.push('code', 'programming', 'coding', 'developer');
    }
    if (text.includes('chat') || text.includes('conversation') || text.includes('assistant')) {
      tags.push('chat', 'conversational', 'assistant');
    }
    if (text.includes('instruct') || text.includes('instruction') || text.includes('following')) {
      tags.push('instruction-following', 'instruct');
    }
    if (text.includes('reasoning') || text.includes('logic') || text.includes('think') || text.includes('analysis')) {
      tags.push('reasoning', 'logic', 'analytical');
    }
    if (text.includes('math') || text.includes('mathematical') || text.includes('calculation')) {
      tags.push('math', 'mathematical', 'calculation');
    }
    if (text.includes('creative') || text.includes('writing') || text.includes('story') || text.includes('content')) {
      tags.push('creative', 'writing', 'content-creation');
    }
    if (text.includes('translate') || text.includes('translation') || text.includes('language')) {
      tags.push('translation', 'multilingual', 'language');
    }
    if (text.includes('summariz') || text.includes('summary') || text.includes('extract')) {
      tags.push('summarization', 'extraction');
    }
    if (text.includes('search') || text.includes('web') || text.includes('browse')) {
      tags.push('web-search', 'browsing', 'research');
    }
    if (text.includes('fast') || text.includes('quick') || text.includes('speed')) {
      tags.push('fast', 'quick', 'speed');
    }
    if (text.includes('accurate') || text.includes('precision') || text.includes('quality')) {
      tags.push('accurate', 'high-quality', 'precision');
    }

    // Tags baseadas em tamanho/capacidade
    if (model.context_length >= 1000000) tags.push('ultra-long-context', 'million-tokens');
    else if (model.context_length >= 200000) tags.push('very-long-context', 'large-context');
    else if (model.context_length >= 100000) tags.push('long-context', 'extended-context');
    else if (model.context_length >= 32000) tags.push('medium-context');
    else tags.push('standard-context');

    // Tags baseadas em preço
    const totalPrice = parseFloat(model.pricing.prompt) + parseFloat(model.pricing.completion);
    if (totalPrice === 0) {
      tags.push('free', 'gratis', 'no-cost');
    } else if (totalPrice < 0.5) {
      tags.push('very-cheap', 'ultra-low-cost');
    } else if (totalPrice < 2) {
      tags.push('cheap', 'barato', 'low-cost', 'affordable');
    } else if (totalPrice < 10) {
      tags.push('moderate-cost', 'mid-range');
    } else if (totalPrice < 30) {
      tags.push('expensive', 'high-cost');
    } else {
      tags.push('very-expensive', 'premium', 'enterprise');
    }

    // Tags baseadas no provedor
    const provider = this.extractProvider(model.id);
    if (provider) {
      tags.push(provider);

      // Tags específicas por provedor
      switch (provider) {
        case 'openai':
          tags.push('gpt', 'chatgpt');
          break;
        case 'anthropic':
          tags.push('claude');
          break;
        case 'google':
          tags.push('gemini', 'bard');
          break;
        case 'meta':
          tags.push('llama', 'facebook');
          break;
        case 'microsoft':
          tags.push('copilot');
          break;
        case 'cohere':
          tags.push('command');
          break;
        case 'mistral':
          tags.push('mixtral');
          break;
      }
    }

    // Tags baseadas na arquitetura
    if (model.architecture) {
      // Modalidades de entrada
      if (model.architecture.input_modalities?.includes('text')) tags.push('text-input');
      if (model.architecture.input_modalities?.includes('image')) tags.push('image-input', 'vision');
      if (model.architecture.input_modalities?.includes('audio')) tags.push('audio-input', 'speech');
      if (model.architecture.input_modalities?.includes('video')) tags.push('video-input');

      // Modalidades de saída
      if (model.architecture.output_modalities?.includes('text')) tags.push('text-output');
      if (model.architecture.output_modalities?.includes('image')) tags.push('image-generation');
      if (model.architecture.output_modalities?.includes('audio')) tags.push('audio-generation');
      if (model.architecture.output_modalities?.includes('video')) tags.push('video-generation');

      // Tokenizer
      if (model.architecture.tokenizer) {
        tags.push(model.architecture.tokenizer.toLowerCase());
      }
    }

    // Tags baseadas no nome do modelo (padrões comuns)
    const modelName = model.name.toLowerCase();
    if (modelName.includes('turbo')) tags.push('turbo', 'fast');
    if (modelName.includes('preview') || modelName.includes('beta')) tags.push('preview', 'beta', 'experimental');
    if (modelName.includes('mini') || modelName.includes('small')) tags.push('mini', 'small', 'lightweight');
    if (modelName.includes('large') || modelName.includes('big')) tags.push('large', 'big', 'powerful');
    if (modelName.includes('instruct')) tags.push('instruction-tuned');
    if (modelName.includes('base')) tags.push('base-model', 'foundation');

    return [...new Set(tags)]; // Remove duplicatas
  }
  
  /**
   * Destaca termos de busca em um texto
   */
  private highlightMatches(text: string, searchTerms: string[]): string {
    let highlighted = text;

    searchTerms.forEach(term => {
      const regex = new RegExp(`(${term})`, 'gi');
      highlighted = highlighted.replace(regex, '<mark>$1</mark>');
    });

    return highlighted;
  }

  /**
   * Cria índice de busca para os modelos
   */
  private buildModelIndex(models: AIModel[]): void {
    this.modelIndex.clear();

    models.forEach(model => {
      const tags = this.extractModelTags(model);
      const provider = this.extractProvider(model.id);

      // Extrair features baseadas em tags
      const features = tags.filter(tag =>
        ['multimodal', 'vision', 'code', 'reasoning', 'math', 'creative', 'translation', 'web-search'].includes(tag)
      );

      // Categorizar preço
      const totalPrice = parseFloat(model.pricing.prompt) + parseFloat(model.pricing.completion);
      let priceCategory = 'medium';
      if (totalPrice === 0) priceCategory = 'free';
      else if (totalPrice < 2) priceCategory = 'low';
      else if (totalPrice > 10) priceCategory = 'high';

      // Categorizar contexto
      let contextCategory = 'standard';
      if (model.context_length >= 200000) contextCategory = 'ultra-long';
      else if (model.context_length >= 100000) contextCategory = 'long';
      else if (model.context_length >= 32000) contextCategory = 'medium';

      // Criar texto pesquisável
      const searchableText = [
        model.name,
        model.id,
        model.description || '',
        provider,
        ...tags,
        ...features,
        priceCategory,
        contextCategory,
        model.architecture?.tokenizer || '',
        ...(model.architecture?.input_modalities || []),
        ...(model.architecture?.output_modalities || [])
      ].join(' ').toLowerCase();

      this.modelIndex.set(model.id, {
        id: model.id,
        searchableText,
        tags,
        provider,
        features,
        priceCategory,
        contextCategory
      });
    });

    this.lastIndexUpdate = Date.now();
  }

  /**
   * Verifica se o índice precisa ser atualizado
   */
  private shouldUpdateIndex(models: AIModel[]): boolean {
    return this.modelIndex.size !== models.length ||
           Date.now() - this.lastIndexUpdate > 10 * 60 * 1000; // 10 minutos
  }
  
  /**
   * Busca avançada com suporte a fuzzy matching e múltiplos campos
   */
  searchModels(models: AIModel[], searchTerm: string, options: SearchOptions = {}): SearchResult[] {
    if (!searchTerm.trim()) return models.map(model => ({ model, score: 1, matchedFields: [] }));

    // Atualizar índice se necessário
    if (this.shouldUpdateIndex(models)) {
      this.buildModelIndex(models);
    }

    const {
      fuzzyThreshold = 0.6,
      maxResults = 50,
      includeArchitecture = true,
      includeModalidades = true,
      boostFavorites = true,
      boostRecent = false
    } = options;

    const keywords = this.extractKeywords(searchTerm);
    const results: SearchResult[] = [];

    // Adicionar ao histórico
    this.addToHistory(searchTerm);
    
    models.forEach(model => {
      let totalScore = 0;
      const matchedFields: string[] = [];
      
      // Busca no nome (peso maior)
      const nameScore = this.fuzzyMatch(searchTerm, model.name);
      if (nameScore >= fuzzyThreshold) {
        totalScore += nameScore * 3;
        matchedFields.push('name');
      }
      
      // Busca exata no nome (peso ainda maior)
      if (model.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        totalScore += 2;
        if (!matchedFields.includes('name')) matchedFields.push('name');
      }
      
      // Busca por palavras-chave no nome
      if (this.containsAllWords(model.name, keywords)) {
        totalScore += 1.5;
        if (!matchedFields.includes('name')) matchedFields.push('name');
      }
      
      // Busca no ID
      const idScore = this.fuzzyMatch(searchTerm, model.id);
      if (idScore >= fuzzyThreshold) {
        totalScore += idScore * 2;
        matchedFields.push('id');
      }
      
      // Busca exata no ID
      if (model.id.toLowerCase().includes(searchTerm.toLowerCase())) {
        totalScore += 1.5;
        if (!matchedFields.includes('id')) matchedFields.push('id');
      }
      
      // Busca na descrição
      if (model.description) {
        const descScore = this.fuzzyMatch(searchTerm, model.description);
        if (descScore >= fuzzyThreshold * 0.8) { // threshold menor para descrição
          totalScore += descScore * 1;
          matchedFields.push('description');
        }
        
        if (model.description.toLowerCase().includes(searchTerm.toLowerCase())) {
          totalScore += 1;
          if (!matchedFields.includes('description')) matchedFields.push('description');
        }
        
        if (this.containsAllWords(model.description, keywords)) {
          totalScore += 0.8;
          if (!matchedFields.includes('description')) matchedFields.push('description');
        }
      }
      
      // Busca no provedor
      const provider = this.extractProvider(model.id);
      if (provider && this.fuzzyMatch(searchTerm, provider) >= fuzzyThreshold) {
        totalScore += 1;
        matchedFields.push('provider');
      }
      
      // Busca em tags/categorias
      const tags = this.extractModelTags(model);
      tags.forEach(tag => {
        if (this.fuzzyMatch(searchTerm, tag) >= fuzzyThreshold) {
          totalScore += 0.8;
          if (!matchedFields.includes('tags')) matchedFields.push('tags');
        }
      });
      
      // Busca em arquitetura (se habilitada)
      if (includeArchitecture && model.architecture) {
        const archText = `${model.architecture.tokenizer} ${model.architecture.input_modalities?.join(' ')} ${model.architecture.output_modalities?.join(' ')}`;
        if (archText.toLowerCase().includes(searchTerm.toLowerCase())) {
          totalScore += 0.5;
          matchedFields.push('architecture');
        }
      }
      
      // Boost para favoritos
      if (boostFavorites && model.isFavorite) {
        totalScore *= 1.2;
      }
      
      // Boost para modelos recentes
      if (boostRecent && model.created && model.created > Date.now() - 30 * 24 * 60 * 60 * 1000) {
        totalScore *= 1.1;
      }
      
      if (totalScore > 0) {
        results.push({
          model,
          score: totalScore,
          matchedFields,
          highlightedName: this.highlightMatches(model.name, keywords),
          highlightedDescription: model.description ? this.highlightMatches(model.description, keywords) : undefined
        });
      }
    });
    
    // Ordenar por score e limitar resultados
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, maxResults);
  }
  
  /**
   * Adiciona termo ao histórico de busca
   */
  private addToHistory(searchTerm: string): void {
    const term = searchTerm.trim().toLowerCase();
    if (term.length === 0) return;
    
    // Remove duplicatas e adiciona no início
    this.searchHistory = [term, ...this.searchHistory.filter(t => t !== term)].slice(0, 20);
    
    // Atualiza popularidade
    this.popularSearches.set(term, (this.popularSearches.get(term) || 0) + 1);
  }
  
  /**
   * Gera correções ortográficas para um termo
   */
  private generateSpellingCorrections(term: string, models: AIModel[]): SearchSuggestion[] {
    const corrections: SearchSuggestion[] = [];
    const maxDistance = Math.max(1, Math.floor(term.length * 0.3));

    // Verificar nomes de modelos
    models.forEach(model => {
      const modelWords = model.name.toLowerCase().split(/[\s\-_\/]+/);
      modelWords.forEach(word => {
        if (word.length >= 3) {
          const distance = this.levenshteinDistance(term, word);
          if (distance <= maxDistance && distance > 0) {
            corrections.push({
              text: word,
              type: 'correction',
              score: 1 - (distance / word.length),
              description: `Você quis dizer "${word}"?`
            });
          }
        }
      });
    });

    // Verificar provedores comuns
    const commonProviders = ['openai', 'anthropic', 'google', 'meta', 'microsoft', 'cohere', 'mistral'];
    commonProviders.forEach(provider => {
      const distance = this.levenshteinDistance(term, provider);
      if (distance <= maxDistance && distance > 0) {
        corrections.push({
          text: provider,
          type: 'correction',
          score: 1 - (distance / provider.length),
          description: `Você quis dizer "${provider}"?`
        });
      }
    });

    return corrections
      .sort((a, b) => b.score - a.score)
      .slice(0, 3);
  }

  /**
   * Gera sugestões de busca baseadas no termo parcial
   */
  generateSuggestions(partialTerm: string, models: AIModel[]): SearchSuggestion[] {
    if (partialTerm.length < 1) return [];

    // Atualizar índice se necessário
    if (this.shouldUpdateIndex(models)) {
      this.buildModelIndex(models);
    }

    const suggestions: SearchSuggestion[] = [];
    const term = partialTerm.toLowerCase().trim();

    // 1. Sugestões do histórico (alta prioridade)
    this.searchHistory
      .filter(h => h.includes(term) && h !== term)
      .slice(0, 3)
      .forEach((h, index) => {
        suggestions.push({
          text: h,
          type: 'model',
          score: 0.95 - (index * 0.05),
          description: 'Busca recente'
        });
      });

    // 2. Correspondências exatas em nomes de modelos
    models
      .filter(m => m.name.toLowerCase().includes(term))
      .sort((a, b) => {
        // Priorizar correspondências no início do nome
        const aStartsWith = a.name.toLowerCase().startsWith(term);
        const bStartsWith = b.name.toLowerCase().startsWith(term);
        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;
        return a.name.length - b.name.length; // Nomes mais curtos primeiro
      })
      .slice(0, 5)
      .forEach((m, index) => {
        const startsWithTerm = m.name.toLowerCase().startsWith(term);
        suggestions.push({
          text: m.name,
          type: 'model',
          score: startsWithTerm ? 0.9 - (index * 0.02) : 0.8 - (index * 0.02),
          description: `${this.extractProvider(m.id) || 'Modelo'}`
        });
      });

    // 3. Sugestões de provedores
    const providers = new Set(models.map(m => this.extractProvider(m.id)).filter(p => p));
    Array.from(providers)
      .filter(p => p.includes(term))
      .sort((a, b) => {
        const aStarts = a.startsWith(term);
        const bStarts = b.startsWith(term);
        if (aStarts && !bStarts) return -1;
        if (!aStarts && bStarts) return 1;
        return a.length - b.length;
      })
      .slice(0, 3)
      .forEach((p, index) => {
        suggestions.push({
          text: p,
          type: 'provider',
          score: 0.75 - (index * 0.05),
          description: 'Provedor'
        });
      });

    // 4. Sugestões de tags/features
    const allTags = new Set<string>();
    this.modelIndex.forEach(index => {
      index.tags.forEach(tag => {
        if (tag.includes(term) && tag.length >= 3) {
          allTags.add(tag);
        }
      });
    });

    Array.from(allTags)
      .sort((a, b) => {
        const aStarts = a.startsWith(term);
        const bStarts = b.startsWith(term);
        if (aStarts && !bStarts) return -1;
        if (!aStarts && bStarts) return 1;
        return a.length - b.length;
      })
      .slice(0, 4)
      .forEach((tag, index) => {
        suggestions.push({
          text: tag,
          type: 'tag',
          score: 0.7 - (index * 0.03),
          description: 'Categoria'
        });
      });

    // 5. Sugestões de features específicas
    const features = ['multimodal', 'vision', 'code', 'reasoning', 'math', 'creative', 'translation', 'web-search', 'fast', 'cheap', 'free', 'premium'];
    features
      .filter(f => f.includes(term))
      .slice(0, 3)
      .forEach((f, index) => {
        suggestions.push({
          text: f,
          type: 'feature',
          score: 0.65 - (index * 0.03),
          description: 'Funcionalidade'
        });
      });

    // 6. Correções ortográficas (apenas se não houver muitas sugestões)
    if (suggestions.length < 5 && term.length >= 3) {
      const corrections = this.generateSpellingCorrections(term, models);
      suggestions.push(...corrections);
    }

    // 7. Sugestões populares (se ainda precisar de mais)
    if (suggestions.length < 6) {
      const popular = this.getPopularSearches()
        .filter(p => p.term.includes(term) && !suggestions.some(s => s.text === p.term))
        .slice(0, 2);

      popular.forEach(p => {
        suggestions.push({
          text: p.term,
          type: 'model',
          score: 0.6,
          description: `Popular (${p.count} buscas)`
        });
      });
    }

    // Remover duplicatas e ordenar por score
    const uniqueSuggestions = suggestions.reduce((acc, current) => {
      const exists = acc.find(item => item.text === current.text);
      if (!exists) {
        acc.push(current);
      } else if (current.score > exists.score) {
        // Substituir se o score for maior
        const index = acc.indexOf(exists);
        acc[index] = current;
      }
      return acc;
    }, [] as SearchSuggestion[]);

    return uniqueSuggestions
      .sort((a, b) => b.score - a.score)
      .slice(0, 8);
  }
  
  /**
   * Limpa o histórico de busca
   */
  clearHistory(): void {
    this.searchHistory = [];
    this.popularSearches.clear();
  }
  
  /**
   * Obtém o histórico de busca
   */
  getSearchHistory(): string[] {
    return [...this.searchHistory];
  }
  
  /**
   * Obtém as buscas mais populares
   */
  getPopularSearches(): Array<{ term: string; count: number }> {
    return Array.from(this.popularSearches.entries())
      .map(([term, count]) => ({ term, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }
}

// Instância singleton
export const advancedSearchService = new AdvancedSearchService();
