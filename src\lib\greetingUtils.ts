/**
 * Utilitários para gerar saudações dinâmicas baseadas no horário
 */

export const getTimeBasedGreeting = (): string => {
  const hour = new Date().getHours();
  
  if (hour >= 5 && hour < 12) {
    return 'Bom dia';
  } else if (hour >= 12 && hour < 18) {
    return 'Boa tarde';
  } else {
    return 'Boa noite';
  }
};

export const getPersonalizedGreeting = (userName?: string): string => {
  const timeGreeting = getTimeBasedGreeting();
  
  if (userName) {
    return `${timeGreeting}, ${userName}`;
  }
  
  return timeGreeting;
};

export const getWelcomeMessage = (userName?: string): string => {
  const greeting = getPersonalizedGreeting(userName);

  const messages = [
    `${greeting}! Como posso ajudá-lo hoje?`,
    `${greeting}! Estou aqui para auxiliá-lo em qualquer questão.`,
    `${greeting}! Pronto para uma nova conversa interessante?`,
    `${greeting}! O que gostaria de explorar ou descobrir hoje?`
  ];

  // Retorna uma mensagem aleatória baseada no horário atual
  const index = new Date().getHours() % messages.length;
  return messages[index];
};

export const getTemporaryChatDescription = (): string => {
  return 'Área de conversa temporária - experimente livremente! Suas mensagens ficam apenas na memória, mas você pode salvar como chat permanente quando quiser.';
};
