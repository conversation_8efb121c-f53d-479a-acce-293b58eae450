import { useState, useEffect, useCallback } from 'react';
import { FavoriteMessage, ChatMessage } from '@/lib/types/chat';

interface UseFavoritesProps {
  userId: string | null;
}

interface UseFavoritesReturn {
  favorites: FavoriteMessage[];
  loading: boolean;
  error: string | null;
  addToFavorites: (messageId: string, chatId: string, chatName: string, message: ChatMessage) => Promise<void>;
  removeFromFavorites: (messageId: string, chatId: string) => Promise<void>;
  isFavoriteSync: (messageId: string, chatId: string) => boolean;
  getFavorites: (filters?: {
    chatId?: string;
    role?: 'user' | 'assistant';
    startDate?: number;
    endDate?: number;
  }) => Promise<void>;
  refreshFavorites: () => Promise<void>;
  favoritesByChat: (chatId: string) => FavoriteMessage[];
}

export const useFavorites = ({ userId }: UseFavoritesProps): UseFavoritesReturn => {
  const [favorites, setFavorites] = useState<FavoriteMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Função para obter favoritos com filtros opcionais
  const getFavorites = useCallback(async (filters?: {
    chatId?: string;
    role?: 'user' | 'assistant';
    startDate?: number;
    endDate?: number;
  }) => {
    if (!userId) {
      setError('User ID é obrigatório');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({ userId });
      
      if (filters?.chatId) params.append('chatId', filters.chatId);
      if (filters?.role) params.append('role', filters.role);
      if (filters?.startDate) params.append('startDate', filters.startDate.toString());
      if (filters?.endDate) params.append('endDate', filters.endDate.toString());

      const response = await fetch(`/api/favorites?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setFavorites(data.favorites);
      } else {
        setError(data.error || 'Erro ao obter favoritos');
      }
    } catch (err) {
      setError('Erro de rede ao obter favoritos');
      console.error('Erro ao obter favoritos:', err);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Função para adicionar aos favoritos
  const addToFavorites = useCallback(async (
    messageId: string, 
    chatId: string, 
    chatName: string, 
    message: ChatMessage
  ) => {
    if (!userId) {
      setError('User ID é obrigatório');
      return;
    }

    setError(null);

    try {
      const response = await fetch('/api/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          messageId,
          chatId,
          chatName,
          message
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Atualizar lista local de favoritos
        const newFavorite: FavoriteMessage = {
          id: `${chatId}_${messageId}`,
          messageId,
          chatId,
          chatName,
          role: message.role,
          content: message.content,
          timestamp: message.timestamp,
          favoritedAt: Date.now(),
          attachments: message.attachments
        };
        
        setFavorites(prev => [newFavorite, ...prev]);
      } else {
        setError(data.error || 'Erro ao adicionar favorito');
      }
    } catch (err) {
      setError('Erro de rede ao adicionar favorito');
      console.error('Erro ao adicionar favorito:', err);
    }
  }, [userId]);

  // Função para remover dos favoritos
  const removeFromFavorites = useCallback(async (messageId: string, chatId: string) => {
    if (!userId) {
      setError('User ID é obrigatório');
      return;
    }

    setError(null);

    try {
      const params = new URLSearchParams({
        userId,
        messageId,
        chatId
      });

      const response = await fetch(`/api/favorites?${params.toString()}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        // Remover da lista local de favoritos
        const favoriteId = `${chatId}_${messageId}`;
        setFavorites(prev => prev.filter(fav => fav.id !== favoriteId));
      } else {
        setError(data.error || 'Erro ao remover favorito');
      }
    } catch (err) {
      setError('Erro de rede ao remover favorito');
      console.error('Erro ao remover favorito:', err);
    }
  }, [userId]);

  // Função síncrona para verificar se uma mensagem está nos favoritos (baseada na lista carregada)
  const isFavoriteSync = useCallback((messageId: string, chatId: string): boolean => {
    if (!userId) return false;

    const favoriteId = `${chatId}_${messageId}`;
    return favorites.some(fav => fav.id === favoriteId);
  }, [userId, favorites]);

  // Função para obter favoritos de um chat específico
  const favoritesByChat = useCallback((chatId: string): FavoriteMessage[] => {
    return favorites.filter(fav => fav.chatId === chatId);
  }, [favorites]);

  // Função para atualizar favoritos
  const refreshFavorites = useCallback(async () => {
    await getFavorites();
  }, [getFavorites]);

  // Carregar favoritos inicialmente
  useEffect(() => {
    if (userId) {
      getFavorites();
    }
  }, [userId, getFavorites]);

  return {
    favorites,
    loading,
    error,
    addToFavorites,
    removeFromFavorites,
    isFavoriteSync,
    getFavorites,
    refreshFavorites,
    favoritesByChat
  };
};
