'use client';

import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeHighlight from 'rehype-highlight';
import rehypeKatex from 'rehype-katex';

const LaTeXTest = () => {
  const testContent = `
# Teste de LaTeX

## Fórmulas Inline
Aqui temos uma fórmula inline: $x^2 + y^2 = z^2$

## Fórmulas de Bloco
Aqui temos uma fórmula de bloco:

$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

## Fórmula Quadrática
A fórmula quadrática é:

$$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$

## Equação de Bhaskara
Para resolver $ax^2 + bx + c = 0$:

$$x = \\frac{-b \\pm \\sqrt{\\Delta}}{2a}$$

onde $\\Delta = b^2 - 4ac$.

## Símbolos Gregos
- Alpha: $\\alpha$
- Beta: $\\beta$
- Gamma: $\\gamma$
- Delta: $\\delta$
- Pi: $\\pi$

## Frações e Raízes
- Fração: $\\frac{1}{2}$
- Raiz quadrada: $\\sqrt{x}$
- Raiz cúbica: $\\sqrt[3]{x}$

## Somatórios e Integrais
- Somatório: $\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}$
- Integral: $\\int_0^1 x^2 dx = \\frac{1}{3}$
`;

  return (
    <div className="p-8 max-w-4xl mx-auto bg-slate-800 rounded-lg">
      <h1 className="text-2xl font-bold mb-6 text-white">Teste de Renderização LaTeX</h1>
      <div className="prose prose-invert max-w-none">
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkMath]}
          rehypePlugins={[rehypeHighlight, rehypeKatex]}
        >
          {testContent}
        </ReactMarkdown>
      </div>
    </div>
  );
};

export default LaTeXTest;
