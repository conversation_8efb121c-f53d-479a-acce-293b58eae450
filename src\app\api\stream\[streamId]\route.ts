import { NextRequest, NextResponse } from 'next/server';

// Store compartilhado com o route principal
declare global {
  var activeStreams: Map<string, any> | undefined;
}

// Usar o mesmo store do route principal
const getActiveStreams = () => {
  if (!global.activeStreams) {
    global.activeStreams = new Map();
  }
  return global.activeStreams;
};

// DELETE: Cancelar streaming específico
export async function DELETE(
  request: NextRequest,
  { params }: { params: { streamId: string } }
) {
  try {
    const { streamId } = params;

    if (!streamId) {
      return NextResponse.json({ error: 'streamId é obrigatório' }, { status: 400 });
    }

    const activeStreams = getActiveStreams();
    const streamData = activeStreams.get(streamId);
    
    if (streamData) {
      streamData.aborted = true;
      
      if (streamData.controller) {
        try {
          const encoder = new TextEncoder();
          streamData.controller.enqueue(encoder.encode('data: {"type":"cancelled","isComplete":true}\n\n'));
          streamData.controller.close();
        } catch (error) {
          console.error('Erro ao fechar controller:', error);
        }
      }
      
      activeStreams.delete(streamId);
      console.log(`Stream ${streamId} cancelado com sucesso`);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Erro ao cancelar streaming:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
