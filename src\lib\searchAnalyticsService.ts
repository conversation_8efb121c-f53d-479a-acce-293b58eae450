import { db } from '@/lib/firebase';
import { doc, setDoc, getDoc, updateDoc, increment, collection, query, orderBy, limit, getDocs, where, Timestamp } from 'firebase/firestore';

export interface SearchAnalytics {
  term: string;
  count: number;
  lastSearched: number;
  userId?: string;
  resultsCount: number;
  avgResultsCount: number;
  clickedResults: string[]; // IDs dos modelos clicados
}

export interface UserSearchHistory {
  id: string;
  term: string;
  timestamp: number;
  resultsCount: number;
  selectedModel?: string;
  sessionId: string;
}

export interface SearchTrends {
  term: string;
  count: number;
  growth: number; // % de crescimento na última semana
  category: 'model' | 'provider' | 'feature' | 'other';
}

/**
 * Serviço para analytics e histórico de busca
 */
export class SearchAnalyticsService {
  private sessionId: string;
  
  constructor() {
    // Gerar ID de sessão único
    this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Registra uma busca no analytics
   */
  async trackSearch(
    userId: string | null,
    searchTerm: string,
    resultsCount: number,
    selectedModel?: string
  ): Promise<void> {
    if (!searchTerm.trim()) return;
    
    const term = searchTerm.toLowerCase().trim();
    
    try {
      // 1. Atualizar analytics globais
      await this.updateGlobalAnalytics(term, resultsCount);
      
      // 2. Salvar no histórico do usuário (se logado)
      if (userId) {
        await this.saveToUserHistory(userId, term, resultsCount, selectedModel);
        await this.updateUserAnalytics(userId, term, resultsCount);
      }
      
      // 3. Atualizar analytics de sessão
      this.updateSessionAnalytics(term, resultsCount, selectedModel);
      
    } catch (error) {
      console.error('Erro ao registrar analytics de busca:', error);
    }
  }
  
  /**
   * Registra quando um modelo é selecionado após uma busca
   */
  async trackModelSelection(
    userId: string | null,
    searchTerm: string,
    modelId: string
  ): Promise<void> {
    if (!searchTerm.trim()) return;
    
    const term = searchTerm.toLowerCase().trim();
    
    try {
      // Atualizar analytics globais com o clique
      const globalRef = doc(db, 'search_analytics', 'global', 'terms', term);
      await updateDoc(globalRef, {
        clickedResults: increment(1),
        [`clickedModels.${modelId}`]: increment(1)
      });
      
      // Atualizar histórico do usuário se logado
      if (userId) {
        const userRef = doc(db, 'search_analytics', 'users', userId, 'recent_searches');
        const userDoc = await getDoc(userRef);
        
        if (userDoc.exists()) {
          const searches = userDoc.data().searches || [];
          const updatedSearches = searches.map((search: any) => {
            if (search.term === term && !search.selectedModel) {
              return { ...search, selectedModel: modelId };
            }
            return search;
          });
          
          await updateDoc(userRef, { searches: updatedSearches });
        }
      }
      
    } catch (error) {
      console.error('Erro ao registrar seleção de modelo:', error);
    }
  }
  
  /**
   * Atualiza analytics globais
   */
  private async updateGlobalAnalytics(term: string, resultsCount: number): Promise<void> {
    const globalRef = doc(db, 'search_analytics', 'global', 'terms', term);
    
    try {
      const docSnap = await getDoc(globalRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        const newAvg = ((data.avgResultsCount * data.count) + resultsCount) / (data.count + 1);
        
        await updateDoc(globalRef, {
          count: increment(1),
          lastSearched: Date.now(),
          avgResultsCount: newAvg,
          resultsCount: resultsCount
        });
      } else {
        await setDoc(globalRef, {
          term,
          count: 1,
          lastSearched: Date.now(),
          resultsCount,
          avgResultsCount: resultsCount,
          clickedResults: 0,
          clickedModels: {}
        });
      }
    } catch (error) {
      console.error('Erro ao atualizar analytics globais:', error);
    }
  }
  
  /**
   * Salva busca no histórico do usuário
   */
  private async saveToUserHistory(
    userId: string,
    term: string,
    resultsCount: number,
    selectedModel?: string
  ): Promise<void> {
    const userRef = doc(db, 'search_analytics', 'users', userId, 'recent_searches');
    
    try {
      const docSnap = await getDoc(userRef);
      const newSearch: UserSearchHistory = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        term,
        timestamp: Date.now(),
        resultsCount,
        selectedModel,
        sessionId: this.sessionId
      };
      
      let searches: UserSearchHistory[] = [];
      if (docSnap.exists()) {
        searches = docSnap.data().searches || [];
      }
      
      // Adicionar nova busca no início e manter apenas as últimas 100
      searches.unshift(newSearch);
      searches = searches.slice(0, 100);
      
      await setDoc(userRef, { searches, lastUpdated: Date.now() });
      
    } catch (error) {
      console.error('Erro ao salvar histórico do usuário:', error);
    }
  }
  
  /**
   * Atualiza analytics pessoais do usuário
   */
  private async updateUserAnalytics(
    userId: string,
    term: string,
    resultsCount: number
  ): Promise<void> {
    const userAnalyticsRef = doc(db, 'search_analytics', 'users', userId, 'analytics');
    
    try {
      const docSnap = await getDoc(userAnalyticsRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        const termCounts = data.termCounts || {};
        
        await updateDoc(userAnalyticsRef, {
          totalSearches: increment(1),
          lastSearch: Date.now(),
          [`termCounts.${term}`]: increment(1)
        });
      } else {
        await setDoc(userAnalyticsRef, {
          totalSearches: 1,
          lastSearch: Date.now(),
          termCounts: { [term]: 1 }
        });
      }
    } catch (error) {
      console.error('Erro ao atualizar analytics do usuário:', error);
    }
  }
  
  /**
   * Atualiza analytics de sessão (local)
   */
  private updateSessionAnalytics(
    term: string,
    resultsCount: number,
    selectedModel?: string
  ): void {
    try {
      const sessionKey = 'search_session_analytics';
      const existing = localStorage.getItem(sessionKey);
      let sessionData = existing ? JSON.parse(existing) : { searches: [], sessionId: this.sessionId };
      
      sessionData.searches.push({
        term,
        timestamp: Date.now(),
        resultsCount,
        selectedModel
      });
      
      // Manter apenas as últimas 50 buscas da sessão
      sessionData.searches = sessionData.searches.slice(-50);
      
      localStorage.setItem(sessionKey, JSON.stringify(sessionData));
    } catch (error) {
      console.error('Erro ao atualizar analytics de sessão:', error);
    }
  }
  
  /**
   * Obtém histórico de busca do usuário
   */
  async getUserSearchHistory(userId: string, limit: number = 20): Promise<UserSearchHistory[]> {
    try {
      const userRef = doc(db, 'search_analytics', 'users', userId, 'recent_searches');
      const docSnap = await getDoc(userRef);
      
      if (docSnap.exists()) {
        const searches = docSnap.data().searches || [];
        return searches.slice(0, limit);
      }
      
      return [];
    } catch (error) {
      console.error('Erro ao obter histórico do usuário:', error);
      return [];
    }
  }
  
  /**
   * Obtém termos de busca mais populares globalmente
   */
  async getPopularSearchTerms(limitCount: number = 10): Promise<SearchAnalytics[]> {
    try {
      const termsRef = collection(db, 'search_analytics', 'global', 'terms');
      const q = query(termsRef, orderBy('count', 'desc'), limit(limitCount));
      const querySnapshot = await getDocs(q);
      
      const results: SearchAnalytics[] = [];
      querySnapshot.forEach((doc) => {
        results.push(doc.data() as SearchAnalytics);
      });
      
      return results;
    } catch (error) {
      console.error('Erro ao obter termos populares:', error);
      return [];
    }
  }
  
  /**
   * Obtém tendências de busca
   */
  async getSearchTrends(days: number = 7): Promise<SearchTrends[]> {
    try {
      const cutoffDate = Date.now() - (days * 24 * 60 * 60 * 1000);
      const termsRef = collection(db, 'search_analytics', 'global', 'terms');
      const q = query(
        termsRef, 
        where('lastSearched', '>=', cutoffDate),
        orderBy('count', 'desc'),
        limit(20)
      );
      
      const querySnapshot = await getDocs(q);
      const trends: SearchTrends[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        trends.push({
          term: data.term,
          count: data.count,
          growth: 0, // Calcular crescimento seria mais complexo
          category: this.categorizeSearchTerm(data.term)
        });
      });
      
      return trends;
    } catch (error) {
      console.error('Erro ao obter tendências:', error);
      return [];
    }
  }
  
  /**
   * Categoriza um termo de busca
   */
  private categorizeSearchTerm(term: string): 'model' | 'provider' | 'feature' | 'other' {
    const lowerTerm = term.toLowerCase();
    
    // Provedores conhecidos
    const providers = ['openai', 'anthropic', 'google', 'meta', 'microsoft', 'cohere', 'mistral'];
    if (providers.some(p => lowerTerm.includes(p))) return 'provider';
    
    // Features/funcionalidades
    const features = ['vision', 'code', 'chat', 'reasoning', 'math', 'creative', 'multimodal', 'fast', 'cheap'];
    if (features.some(f => lowerTerm.includes(f))) return 'feature';
    
    // Nomes de modelos comuns
    const models = ['gpt', 'claude', 'gemini', 'llama', 'deepseek'];
    if (models.some(m => lowerTerm.includes(m))) return 'model';
    
    return 'other';
  }
  
  /**
   * Limpa dados antigos (para manutenção)
   */
  async cleanupOldData(daysToKeep: number = 90): Promise<void> {
    // Esta função seria implementada para limpeza periódica
    // Por enquanto, apenas um placeholder
    console.log(`Limpeza de dados mais antigos que ${daysToKeep} dias seria executada aqui`);
  }
  
  /**
   * Obtém analytics de sessão atual
   */
  getSessionAnalytics(): any {
    try {
      const sessionKey = 'search_session_analytics';
      const existing = localStorage.getItem(sessionKey);
      return existing ? JSON.parse(existing) : null;
    } catch (error) {
      console.error('Erro ao obter analytics de sessão:', error);
      return null;
    }
  }
}

// Instância singleton
export const searchAnalyticsService = new SearchAnalyticsService();
