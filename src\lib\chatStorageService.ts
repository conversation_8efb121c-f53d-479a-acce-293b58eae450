import { storage } from './firebase';
import { ref, uploadBytes, getDownloadURL, deleteObject, getBytes } from 'firebase/storage';
import { ChatData, ChatMessage, ChatStorageService, MessageInput } from './types/chat';

class ChatStorageServiceImpl implements ChatStorageService {
  
  /**
   * Cria um arquivo JSON vazio para um novo chat no Storage
   * Agora usa estrutura de pastas: usuarios/{userId}/conversas/{chatId}/chat.json
   */
  async createChatFile(userId: string, chatId: string, chatData: Omit<ChatData, 'messages'>): Promise<void> {
    try {
      const initialChatData: ChatData = {
        ...chatData,
        messages: [] // Inicializar sem mensagens conforme solicitado
      };

      const chatPath = `usuarios/${userId}/conversas/${chatId}/chat.json`;
      const chatRef = ref(storage, chatPath);

      const jsonData = JSON.stringify(initialChatData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });

      await uploadBytes(chatRef, blob);
      console.log(`Chat file created successfully: ${chatPath}`);
    } catch (error) {
      console.error('Error creating chat file:', error);
      throw error;
    }
  }

  /**
   * Obtém os dados de um chat do Storage
   * Agora usa estrutura de pastas: usuarios/{userId}/conversas/{chatId}/chat.json
   */
  async getChatData(userId: string, chatId: string): Promise<ChatData | null> {
    try {
      const chatPath = `usuarios/${userId}/conversas/${chatId}/chat.json`;
      const chatRef = ref(storage, chatPath);

      const bytes = await getBytes(chatRef);
      const jsonString = new TextDecoder().decode(bytes);
      const chatData: ChatData = JSON.parse(jsonString);

      return chatData;
    } catch (error) {
      console.error('Error getting chat data:', error);
      // Se o arquivo não existir, retornar null
      if ((error as any)?.code === 'storage/object-not-found') {
        return null;
      }
      throw error;
    }
  }

  /**
   * Atualiza os dados completos de um chat no Storage
   * Agora usa estrutura de pastas: usuarios/{userId}/conversas/{chatId}/chat.json
   */
  async updateChatData(userId: string, chatId: string, chatData: ChatData): Promise<void> {
    try {
      // Atualizar o timestamp de última atualização
      chatData.lastUpdatedAt = Date.now();

      const chatPath = `usuarios/${userId}/conversas/${chatId}/chat.json`;
      const chatRef = ref(storage, chatPath);

      const jsonData = JSON.stringify(chatData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });

      await uploadBytes(chatRef, blob);
      console.log(`Chat data updated successfully: ${chatPath}`);
    } catch (error) {
      console.error('Error updating chat data:', error);
      throw error;
    }
  }

  /**
   * Adiciona uma nova mensagem ao chat
   */
  async addMessage(userId: string, chatId: string, message: MessageInput): Promise<void> {
    try {
      // Primeiro, obter os dados atuais do chat
      const currentData = await this.getChatData(userId, chatId);
      
      if (!currentData) {
        throw new Error(`Chat not found: ${chatId}`);
      }

      // Criar a nova mensagem com ID único e timestamp
      const newMessage: ChatMessage = {
        id: `${message.role}-${Date.now()}-${Math.random()}`,
        role: message.role,
        content: message.content,
        timestamp: Date.now(),
        ...(message.attachments && { attachments: message.attachments }),
        ...((message as any).usage && { usage: (message as any).usage }),
        ...((message as any).responseTime && { responseTime: (message as any).responseTime }),
        ...((message as any).typingSpeed && { typingSpeed: (message as any).typingSpeed })
      };

      // Adicionar a nova mensagem ao array
      currentData.messages.push(newMessage);
      
      // Atualizar o arquivo completo
      await this.updateChatData(userId, chatId, currentData);
      
      console.log(`Message added successfully to chat: ${chatId}`);
    } catch (error) {
      console.error('Error adding message:', error);
      throw error;
    }
  }

  /**
   * Deleta um arquivo de chat do Storage
   * Agora usa estrutura de pastas: usuarios/{userId}/conversas/{chatId}/chat.json
   */
  async deleteChatFile(userId: string, chatId: string): Promise<void> {
    try {
      const chatPath = `usuarios/${userId}/conversas/${chatId}/chat.json`;
      const chatRef = ref(storage, chatPath);

      await deleteObject(chatRef);
      console.log(`Chat file deleted successfully: ${chatPath}`);
    } catch (error) {
      console.error('Error deleting chat file:', error);
      throw error;
    }
  }

  /**
   * Gera um ID único para mensagens
   */
  private generateMessageId(role: 'user' | 'assistant'): string {
    return `${role}-${Date.now()}`;
  }

  /**
   * Valida se os dados do chat estão no formato correto
   */
  private validateChatData(chatData: ChatData): boolean {
    return (
      typeof chatData.id === 'string' &&
      typeof chatData.name === 'string' &&
      Array.isArray(chatData.messages) &&
      typeof chatData.context === 'string' &&
      typeof chatData.system_prompt === 'string' &&
      typeof chatData.temperature === 'number' &&
      typeof chatData.maxTokens === 'number' &&
      typeof chatData.frequency_penalty === 'number' &&
      typeof chatData.repetition_penalty === 'number' &&
      typeof chatData.createdAt === 'number' &&
      typeof chatData.lastUpdatedAt === 'number'
    );
  }
}

// Exportar uma instância singleton do serviço
export const chatStorageService = new ChatStorageServiceImpl();
export default chatStorageService;
