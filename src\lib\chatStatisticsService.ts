import { db } from './firebase';
import { doc, getDoc, setDoc, updateDoc, collection, getDocs, query, orderBy } from 'firebase/firestore';
import { ChatStatistics, ChatMessage, SessionData } from './types/chat';

class ChatStatisticsServiceImpl {
  private readonly INACTIVITY_THRESHOLD = 30 * 60 * 1000; // 30 minutos em ms
  private readonly SESSION_TIMEOUT = 60 * 60 * 1000; // 1 hora em ms

  /**
   * Obtém as estatísticas de um chat
   */
  async getChatStatistics(userId: string, chatId: string): Promise<ChatStatistics | null> {
    try {
      const statsRef = doc(db, 'usuarios', userId, 'conversas', chatId, 'estatisticas', 'dados');
      const statsDoc = await getDoc(statsRef);

      if (statsDoc.exists()) {
        const data = statsDoc.data() as ChatStatistics;
        // Migrar estatísticas antigas para incluir novas propriedades
        return this.migrateStatistics(data);
      }

      return null;
    } catch (error) {
      console.error('Error getting chat statistics:', error);
      throw error;
    }
  }

  /**
   * Migra estatísticas antigas para incluir novas propriedades
   */
  private migrateStatistics(stats: any): ChatStatistics {
    // Garantir que todas as propriedades básicas existam
    if (!stats.totalWords) stats.totalWords = 0;
    if (!stats.userWords) stats.userWords = 0;
    if (!stats.assistantWords) stats.assistantWords = 0;

    // Garantir que todas as novas propriedades existam
    if (!stats.messageEdits) {
      stats.messageEdits = {
        totalEdits: 0,
        userMessageEdits: 0,
        averageEditsPerMessage: 0,
        mostEditedMessageLength: 0
      };
    }

    if (!stats.messageRegenerations) {
      stats.messageRegenerations = {
        totalRegenerations: 0,
        averageRegenerationsPerAssistantMessage: 0,
        regenerationReasons: []
      };
    }

    if (!stats.sentimentAnalysis) {
      stats.sentimentAnalysis = {
        positive: 0,
        neutral: 0,
        negative: 0,
        averageSentimentScore: 0,
        sentimentTrend: []
      };
    }

    if (!stats.conversationPatterns) {
      stats.conversationPatterns = {
        averageConversationLength: 0,
        longestConversationStreak: 0,
        shortestResponseTime: 0,
        longestResponseTime: 0,
        questionsAsked: 0,
        codeBlocksShared: 0,
        mathExpressionsUsed: 0
      };
    }

    if (!stats.messageComplexity) {
      stats.messageComplexity = {
        averageWordsPerMessage: 0,
        averageSentencesPerMessage: 0,
        averageSyllablesPerWord: 0,
        readabilityScore: 0,
        technicalTermsUsed: 0
      };
    }

    if (!stats.readingMetrics) {
      stats.readingMetrics = {
        estimatedReadingTime: 0,
        averageTimeToRespond: 0,
        quickResponses: 0,
        thoughtfulResponses: 0
      };
    }

    if (!stats.featureUsage) {
      stats.featureUsage = {
        attachmentsUsed: {
          images: 0,
          pdfs: 0,
          totalSizeMB: 0
        },
        latexUsage: 0,
        codeLanguages: [],
        topicsDiscussed: []
      };
    }

    if (!stats.conversationEfficiency) {
      stats.conversationEfficiency = {
        goalAchievementRate: 0,
        clarificationRequests: 0,
        followUpQuestions: 0,
        satisfactionIndicators: 0
      };
    }

    return stats as ChatStatistics;
  }

  /**
   * Inicializa as estatísticas de um chat
   */
  async initializeChatStatistics(userId: string, chatId: string): Promise<ChatStatistics> {
    const initialStats: ChatStatistics = {
      totalMessages: 0,
      userMessages: 0,
      assistantMessages: 0,
      totalAttachments: 0,
      totalWords: 0,
      userWords: 0,
      assistantWords: 0,
      totalPromptTokens: 0,
      totalCompletionTokens: 0,
      totalTokens: 0,
      totalCost: 0,
      averageResponseTime: 0,
      averageMessageLength: 0,
      averageUserTypingSpeed: 0,
      averageTimeBetweenMessages: 0,
      totalSessions: 0,
      averageSessionDuration: 0,
      averageInactivityPeriod: 0,
      activityHeatmap: {
        hourOfDay: new Array(24).fill(0),
        dayOfWeek: new Array(7).fill(0)
      },
      mostUsedWords: [],
      modelPerformanceHistory: [],

      // Novas estatísticas únicas
      messageEdits: {
        totalEdits: 0,
        userMessageEdits: 0,
        averageEditsPerMessage: 0,
        mostEditedMessageLength: 0
      },
      messageRegenerations: {
        totalRegenerations: 0,
        averageRegenerationsPerAssistantMessage: 0,
        regenerationReasons: []
      },
      sentimentAnalysis: {
        positive: 0,
        neutral: 0,
        negative: 0,
        averageSentimentScore: 0,
        sentimentTrend: []
      },
      conversationPatterns: {
        averageConversationLength: 0,
        longestConversationStreak: 0,
        shortestResponseTime: 0,
        longestResponseTime: 0,
        questionsAsked: 0,
        codeBlocksShared: 0,
        mathExpressionsUsed: 0
      },
      messageComplexity: {
        averageWordsPerMessage: 0,
        averageSentencesPerMessage: 0,
        averageSyllablesPerWord: 0,
        readabilityScore: 0,
        technicalTermsUsed: 0
      },
      readingMetrics: {
        estimatedReadingTime: 0,
        averageTimeToRespond: 0,
        quickResponses: 0,
        thoughtfulResponses: 0
      },
      featureUsage: {
        attachmentsUsed: {
          images: 0,
          pdfs: 0,
          totalSizeMB: 0
        },
        latexUsage: 0,
        codeLanguages: [],
        topicsDiscussed: []
      },
      conversationEfficiency: {
        goalAchievementRate: 0,
        clarificationRequests: 0,
        followUpQuestions: 0,
        satisfactionIndicators: 0
      },

      firstMessageAt: Date.now(),
      lastMessageAt: Date.now(),
      lastUpdatedAt: Date.now()
    };

    await this.saveChatStatistics(userId, chatId, initialStats);
    return initialStats;
  }

  /**
   * Salva as estatísticas de um chat
   */
  async saveChatStatistics(userId: string, chatId: string, stats: ChatStatistics): Promise<void> {
    try {
      const statsRef = doc(db, 'usuarios', userId, 'conversas', chatId, 'estatisticas', 'dados');
      stats.lastUpdatedAt = Date.now();
      await setDoc(statsRef, stats);
      console.log('✅ Estatísticas salvas com sucesso!', { totalMessages: stats.totalMessages });
    } catch (error) {
      console.error('❌ Error saving chat statistics:', error);
      throw error;
    }
  }

  /**
   * Atualiza as estatísticas quando uma nova mensagem é adicionada
   */
  async updateStatisticsOnNewMessage(
    userId: string,
    chatId: string,
    message: ChatMessage,
    previousMessage?: ChatMessage
  ): Promise<void> {
    try {
      let stats = await this.getChatStatistics(userId, chatId);

      if (!stats) {
        stats = await this.initializeChatStatistics(userId, chatId);
      }

      // Atualizar contadores básicos
      stats.totalMessages++;
      if (message.role === 'user') {
        stats.userMessages++;
      } else {
        stats.assistantMessages++;
      }

      if (message.attachments && message.attachments.length > 0) {
        stats.totalAttachments += message.attachments.length;
      }



      // Atualizar tokens e custos (com lógica especial para acumulação)
      if (message.usage) {
        if (message.role === 'assistant' && previousMessage?.role === 'user') {
          // Para mensagens do assistente, usar a lógica de diferença
          const promptTokensDiff = message.usage.prompt_tokens - (previousMessage.usage?.prompt_tokens || 0);
          const completionTokensDiff = message.usage.completion_tokens;
          const costDiff = message.usage.cost - (previousMessage.usage?.cost || 0);

          stats.totalPromptTokens += Math.max(0, promptTokensDiff);
          stats.totalCompletionTokens += completionTokensDiff;
          stats.totalCost += Math.max(0, costDiff);
        } else {
          // Para mensagens do usuário, usar valores diretos
          stats.totalPromptTokens += message.usage.prompt_tokens;
          stats.totalCompletionTokens += message.usage.completion_tokens;
          stats.totalCost += message.usage.cost;
        }
        
        stats.totalTokens = stats.totalPromptTokens + stats.totalCompletionTokens;
      }

      // Atualizar métricas de tempo
      if (message.responseTime && message.role === 'assistant') {
        stats.averageResponseTime = this.calculateNewAverage(
          stats.averageResponseTime,
          message.responseTime,
          stats.assistantMessages
        );

        // Adicionar ao histórico de performance
        stats.modelPerformanceHistory.push({
          timestamp: message.timestamp,
          responseTime: message.responseTime,
          model: 'current' // TODO: pegar o modelo atual
        });

        // Manter apenas os últimos 100 registros
        if (stats.modelPerformanceHistory.length > 100) {
          stats.modelPerformanceHistory = stats.modelPerformanceHistory.slice(-100);
        }
      }

      // Atualizar comprimento médio das mensagens
      const messageLength = message.content.length;
      stats.averageMessageLength = this.calculateNewAverage(
        stats.averageMessageLength,
        messageLength,
        stats.totalMessages
      );

      // Velocidade de digitação removida - não é mais calculada

      // Atualizar tempo entre mensagens
      if (previousMessage) {
        const timeBetween = message.timestamp - previousMessage.timestamp;
        stats.averageTimeBetweenMessages = this.calculateNewAverage(
          stats.averageTimeBetweenMessages,
          timeBetween,
          stats.totalMessages - 1
        );
      }

      // Atualizar heatmap de atividade
      const messageDate = new Date(message.timestamp);
      const hour = messageDate.getHours();
      const dayOfWeek = messageDate.getDay();
      
      stats.activityHeatmap.hourOfDay[hour]++;
      stats.activityHeatmap.dayOfWeek[dayOfWeek]++;

      // Atualizar palavras mais usadas
      if (message.role === 'user') {
        stats.mostUsedWords = this.updateMostUsedWords(stats.mostUsedWords, message.content);
      }

      // === ATUALIZAR NOVAS ESTATÍSTICAS ===

      // Análise de sentimento (apenas mensagens do usuário)
      if (message.role === 'user') {
        const sentiment = this.analyzeSentiment(message.content);

        if (sentiment > 0.1) stats.sentimentAnalysis.positive++;
        else if (sentiment < -0.1) stats.sentimentAnalysis.negative++;
        else stats.sentimentAnalysis.neutral++;

        stats.sentimentAnalysis.averageSentimentScore = this.calculateNewAverage(
          stats.sentimentAnalysis.averageSentimentScore,
          sentiment,
          stats.userMessages
        );

        // Adicionar ao trend (manter últimos 50)
        stats.sentimentAnalysis.sentimentTrend.push({
          timestamp: message.timestamp,
          score: sentiment
        });
        if (stats.sentimentAnalysis.sentimentTrend.length > 50) {
          stats.sentimentAnalysis.sentimentTrend = stats.sentimentAnalysis.sentimentTrend.slice(-50);
        }
      }

      // Padrões de conversa
      if (message.role === 'user') {
        stats.conversationPatterns.questionsAsked += this.countQuestions(message.content);
        stats.conversationPatterns.codeBlocksShared += this.countCodeBlocks(message.content);
        stats.conversationPatterns.mathExpressionsUsed += this.countMathExpressions(message.content);

        // Detectar linguagens de código
        const languages = this.detectCodeLanguages(message.content);
        languages.forEach(lang => {
          const existing = stats.featureUsage.codeLanguages.find(cl => cl.language === lang);
          if (existing) {
            existing.count++;
          } else {
            stats.featureUsage.codeLanguages.push({ language: lang, count: 1 });
          }
        });

        // Eficiência da conversa
        stats.conversationEfficiency.satisfactionIndicators += this.countSatisfactionIndicators(message.content);
        stats.conversationEfficiency.clarificationRequests += this.countClarificationRequests(message.content);
      }

      // Complexidade das mensagens
      const sentenceCount = this.countSentences(message.content);
      const wordCount = this.countWords(message.content);

      stats.messageComplexity.averageWordsPerMessage = this.calculateNewAverage(
        stats.messageComplexity.averageWordsPerMessage,
        wordCount,
        stats.totalMessages
      );

      stats.messageComplexity.averageSentencesPerMessage = this.calculateNewAverage(
        stats.messageComplexity.averageSentencesPerMessage,
        sentenceCount,
        stats.totalMessages
      );

      // Métricas de tempo de resposta
      if (previousMessage && message.role === 'user') {
        const responseTime = message.timestamp - previousMessage.timestamp;

        if (responseTime < 30000) { // 30 segundos
          stats.readingMetrics.quickResponses++;
        } else if (responseTime > 120000) { // 2 minutos
          stats.readingMetrics.thoughtfulResponses++;
        }

        stats.readingMetrics.averageTimeToRespond = this.calculateNewAverage(
          stats.readingMetrics.averageTimeToRespond,
          responseTime,
          stats.userMessages
        );

        // Atualizar tempos de resposta extremos
        if (stats.conversationPatterns.shortestResponseTime === 0 || responseTime < stats.conversationPatterns.shortestResponseTime) {
          stats.conversationPatterns.shortestResponseTime = responseTime;
        }
        if (responseTime > stats.conversationPatterns.longestResponseTime) {
          stats.conversationPatterns.longestResponseTime = responseTime;
        }
      }

      // Uso de recursos específicos
      if (message.attachments && message.attachments.length > 0) {
        message.attachments.forEach(attachment => {
          if (attachment.type === 'image') {
            stats.featureUsage.attachmentsUsed.images++;
          } else if (attachment.type === 'pdf') {
            stats.featureUsage.attachmentsUsed.pdfs++;
          }
          stats.featureUsage.attachmentsUsed.totalSizeMB += attachment.size / (1024 * 1024);
        });
      }

      // Detectar uso de LaTeX
      if (this.countMathExpressions(message.content) > 0) {
        stats.featureUsage.latexUsage++;
      }

      // Atualizar timestamps
      if (stats.totalMessages === 1) {
        stats.firstMessageAt = message.timestamp;
      }
      stats.lastMessageAt = message.timestamp;

      await this.saveChatStatistics(userId, chatId, stats);
    } catch (error) {
      console.error('Error updating statistics on new message:', error);
      throw error;
    }
  }

  /**
   * Calcula uma nova média
   */
  private calculateNewAverage(currentAverage: number, newValue: number, count: number): number {
    if (count === 1) return newValue;
    return ((currentAverage * (count - 1)) + newValue) / count;
  }

  /**
   * Análise de sentimento simples baseada em palavras-chave
   */
  private analyzeSentiment(text: string): number {
    const positiveWords = [
      'obrigado', 'obrigada', 'perfeito', 'excelente', 'ótimo', 'bom', 'legal', 'incrível',
      'fantástico', 'maravilhoso', 'adorei', 'gostei', 'parabéns', 'sucesso', 'feliz',
      'alegre', 'satisfeito', 'contente', 'positivo', 'sim', 'certo', 'correto'
    ];

    const negativeWords = [
      'ruim', 'péssimo', 'horrível', 'terrível', 'erro', 'problema', 'difícil', 'complicado',
      'frustrado', 'irritado', 'chateado', 'triste', 'decepcionado', 'não', 'errado',
      'incorreto', 'falso', 'impossível', 'confuso', 'perdido', 'ajuda'
    ];

    const words = text.toLowerCase().split(/\s+/);
    let score = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) score += 1;
      if (negativeWords.includes(word)) score -= 1;
    });

    // Normalizar entre -1 e 1
    const maxWords = words.length;
    return maxWords > 0 ? Math.max(-1, Math.min(1, score / maxWords)) : 0;
  }

  /**
   * Conta palavras em um texto
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Conta sentenças em um texto
   */
  private countSentences(text: string): number {
    return text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length;
  }

  /**
   * Detecta blocos de código
   */
  private countCodeBlocks(text: string): number {
    const codeBlockRegex = /```[\s\S]*?```|`[^`]+`/g;
    return (text.match(codeBlockRegex) || []).length;
  }

  /**
   * Detecta expressões matemáticas
   */
  private countMathExpressions(text: string): number {
    const mathRegex = /\$[\s\S]*?\$|\\\([\s\S]*?\\\)|\\\[[\s\S]*?\\\]/g;
    return (text.match(mathRegex) || []).length;
  }

  /**
   * Detecta perguntas
   */
  private countQuestions(text: string): number {
    return (text.match(/\?/g) || []).length;
  }

  /**
   * Detecta linguagens de programação em blocos de código
   */
  private detectCodeLanguages(text: string): string[] {
    const languages: string[] = [];
    const codeBlockRegex = /```(\w+)/g;
    let match;

    while ((match = codeBlockRegex.exec(text)) !== null) {
      const lang = match[1].toLowerCase();
      if (!languages.includes(lang)) {
        languages.push(lang);
      }
    }

    return languages;
  }

  /**
   * Detecta indicadores de satisfação
   */
  private countSatisfactionIndicators(text: string): number {
    const satisfactionWords = [
      'obrigado', 'obrigada', 'perfeito', 'excelente', 'ótimo', 'funcionou',
      'resolveu', 'solucionou', 'ajudou', 'esclareceu', 'entendi', 'compreendi'
    ];

    const words = text.toLowerCase().split(/\s+/);
    return words.filter(word => satisfactionWords.includes(word)).length;
  }

  /**
   * Detecta pedidos de esclarecimento
   */
  private countClarificationRequests(text: string): number {
    const clarificationPhrases = [
      'não entendi', 'pode explicar', 'como assim', 'o que significa',
      'pode repetir', 'não compreendi', 'confuso', 'não ficou claro'
    ];

    const lowerText = text.toLowerCase();
    return clarificationPhrases.filter(phrase => lowerText.includes(phrase)).length;
  }

  /**
   * Atualiza a lista de palavras mais usadas
   */
  private updateMostUsedWords(
    currentWords: Array<{ word: string; count: number }>, 
    content: string
  ): Array<{ word: string; count: number }> {
    // Extrair palavras (remover pontuação e converter para minúsculas)
    const words = content
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2); // Ignorar palavras muito curtas

    // Contar palavras
    const wordCount = new Map<string, number>();
    
    // Adicionar palavras existentes
    currentWords.forEach(({ word, count }) => {
      wordCount.set(word, count);
    });

    // Adicionar novas palavras
    words.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });

    // Converter para array e ordenar
    const sortedWords = Array.from(wordCount.entries())
      .map(([word, count]) => ({ word, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10

    return sortedWords;
  }

  /**
   * Atualiza estatísticas de sessão
   */
  async updateSessionStatistics(userId: string, chatId: string, sessionData: SessionData): Promise<void> {
    try {
      let stats = await this.getChatStatistics(userId, chatId);
      
      if (!stats) {
        stats = await this.initializeChatStatistics(userId, chatId);
      }

      if (sessionData.duration) {
        stats.totalSessions++;
        stats.averageSessionDuration = this.calculateNewAverage(
          stats.averageSessionDuration,
          sessionData.duration,
          stats.totalSessions
        );
      }

      await this.saveChatStatistics(userId, chatId, stats);
    } catch (error) {
      console.error('Error updating session statistics:', error);
      throw error;
    }
  }

  /**
   * Recalcula todas as estatísticas baseado nas mensagens existentes
   */
  async recalculateStatistics(userId: string, chatId: string, messages: ChatMessage[]): Promise<ChatStatistics> {
    try {
      const stats = await this.initializeChatStatistics(userId, chatId);

      for (let i = 0; i < messages.length; i++) {
        const message = messages[i];
        const previousMessage = i > 0 ? messages[i - 1] : undefined;

        await this.updateStatisticsOnNewMessage(userId, chatId, message, previousMessage);
      }

      return await this.getChatStatistics(userId, chatId) || stats;
    } catch (error) {
      console.error('Error recalculating statistics:', error);
      throw error;
    }
  }

  /**
   * Atualiza estatísticas quando uma mensagem é editada
   */
  async updateStatisticsOnMessageEdit(
    userId: string,
    chatId: string,
    messageRole: 'user' | 'assistant',
    originalLength: number,
    newLength: number
  ): Promise<void> {
    try {
      let stats = await this.getChatStatistics(userId, chatId);

      if (!stats) {
        stats = await this.initializeChatStatistics(userId, chatId);
      }

      // Atualizar estatísticas de edição
      stats.messageEdits.totalEdits++;

      if (messageRole === 'user') {
        stats.messageEdits.userMessageEdits++;
      }

      // Atualizar comprimento da mensagem mais editada
      if (newLength > stats.messageEdits.mostEditedMessageLength) {
        stats.messageEdits.mostEditedMessageLength = newLength;
      }

      // Recalcular média de edições por mensagem
      const totalMessages = stats.totalMessages;
      if (totalMessages > 0) {
        stats.messageEdits.averageEditsPerMessage = stats.messageEdits.totalEdits / totalMessages;
      }

      await this.saveChatStatistics(userId, chatId, stats);
    } catch (error) {
      console.error('Error updating edit statistics:', error);
      throw error;
    }
  }

  /**
   * Atualiza estatísticas quando uma mensagem é regenerada
   */
  async updateStatisticsOnMessageRegeneration(
    userId: string,
    chatId: string,
    reason?: string
  ): Promise<void> {
    try {
      let stats = await this.getChatStatistics(userId, chatId);

      if (!stats) {
        stats = await this.initializeChatStatistics(userId, chatId);
      }

      // Atualizar estatísticas de regeneração
      stats.messageRegenerations.totalRegenerations++;

      // Atualizar razões de regeneração
      if (reason) {
        const existingReason = stats.messageRegenerations.regenerationReasons.find(r => r.reason === reason);
        if (existingReason) {
          existingReason.count++;
        } else {
          stats.messageRegenerations.regenerationReasons.push({ reason, count: 1 });
        }
      }

      // Recalcular média de regenerações por mensagem do assistente
      if (stats.assistantMessages > 0) {
        stats.messageRegenerations.averageRegenerationsPerAssistantMessage =
          stats.messageRegenerations.totalRegenerations / stats.assistantMessages;
      }

      await this.saveChatStatistics(userId, chatId, stats);
    } catch (error) {
      console.error('Error updating regeneration statistics:', error);
      throw error;
    }
  }

  /**
   * Atualiza métricas de tempo de leitura
   */
  async updateReadingMetrics(
    userId: string,
    chatId: string,
    estimatedReadingTime: number
  ): Promise<void> {
    try {
      let stats = await this.getChatStatistics(userId, chatId);

      if (!stats) {
        stats = await this.initializeChatStatistics(userId, chatId);
      }

      // Atualizar tempo estimado de leitura
      stats.readingMetrics.estimatedReadingTime += estimatedReadingTime;

      await this.saveChatStatistics(userId, chatId, stats);
    } catch (error) {
      console.error('Error updating reading metrics:', error);
      throw error;
    }
  }

  /**
   * Atualiza estatísticas de uma nova mensagem (sem contagem de palavras)
   * A contagem de palavras é feita separadamente pelo useWordCount hook
   */
  async updateStatisticsOnNewMessageWithoutWordCount(
    userId: string,
    chatId: string,
    message: ChatMessage,
    previousMessage?: ChatMessage
  ): Promise<void> {
    try {
      let stats = await this.getChatStatistics(userId, chatId);

      if (!stats) {
        stats = await this.initializeChatStatistics(userId, chatId);
      }

      // Migrar estatísticas se necessário
      stats = this.migrateStatistics(stats);

      // Atualizar contadores básicos
      stats.totalMessages++;
      if (message.role === 'user') {
        stats.userMessages++;
      } else {
        stats.assistantMessages++;
      }

      if (message.attachments && message.attachments.length > 0) {
        stats.totalAttachments += message.attachments.length;
      }

      // Continuar com o resto das estatísticas (tokens, tempo, etc.)
      // mas sem contagem de palavras...

      // Atualizar tokens e custos (com lógica especial para acumulação)
      if (message.usage) {
        if (message.role === 'assistant' && previousMessage?.role === 'user') {
          // Para mensagens do assistente, usar a lógica de diferença
          const promptTokensDiff = message.usage.prompt_tokens - (previousMessage.usage?.prompt_tokens || 0);
          const completionTokensDiff = message.usage.completion_tokens;
          const costDiff = message.usage.cost - (previousMessage.usage?.cost || 0);

          stats.totalPromptTokens += Math.max(0, promptTokensDiff);
          stats.totalCompletionTokens += completionTokensDiff;
          stats.totalCost += Math.max(0, costDiff);
        } else {
          // Para mensagens do usuário, usar valores diretos
          stats.totalPromptTokens += message.usage.prompt_tokens;
          stats.totalCompletionTokens += message.usage.completion_tokens;
          stats.totalCost += message.usage.cost;
        }

        stats.totalTokens = stats.totalPromptTokens + stats.totalCompletionTokens;
      }

      // Salvar estatísticas atualizadas
      await this.saveChatStatistics(userId, chatId, stats);
    } catch (error) {
      console.error('Error updating statistics without word count:', error);
      throw error;
    }
  }

  /**
   * Atualiza contagem de palavras baseada nas mensagens atuais do chat
   */
  async updateWordCountFromMessages(userId: string, chatId: string, messages: ChatMessage[]): Promise<void> {
    try {
      console.log(`🔄 Updating word count for chat ${chatId} with ${messages.length} messages`);

      let stats = await this.getChatStatistics(userId, chatId);

      if (!stats) {
        console.log(`📊 Initializing new statistics for chat ${chatId}`);
        stats = await this.initializeChatStatistics(userId, chatId);
      }

      // Calcular contagem de palavras das mensagens atuais
      let totalWords = 0;
      let userWords = 0;
      let assistantWords = 0;

      for (const message of messages) {
        const wordCount = this.countWords(message.content);
        totalWords += wordCount;

        if (message.role === 'user') {
          userWords += wordCount;
        } else {
          assistantWords += wordCount;
        }
      }

      console.log(`📝 Word count calculated: Total=${totalWords}, User=${userWords}, Assistant=${assistantWords}`);
      console.log(`📝 Current stats: Total=${stats.totalWords || 0}, User=${stats.userWords || 0}, Assistant=${stats.assistantWords || 0}`);

      // Atualizar apenas se houve mudança
      if (stats.totalWords !== totalWords ||
          stats.userWords !== userWords ||
          stats.assistantWords !== assistantWords) {

        console.log(`💾 Saving updated word count statistics to Firestore`);
        stats.totalWords = totalWords;
        stats.userWords = userWords;
        stats.assistantWords = assistantWords;

        await this.saveChatStatistics(userId, chatId, stats);
        console.log(`✅ Word count statistics saved successfully`);
      } else {
        console.log(`⏭️ No changes in word count, skipping save`);
      }
    } catch (error) {
      console.error('Error updating word count from messages:', error);
      throw error;
    }
  }

  /**
   * Migra estatísticas existentes para incluir contagem de palavras
   */
  async migrateWordCountStatistics(userId: string, chatId: string): Promise<void> {
    try {
      const stats = await this.getChatStatistics(userId, chatId);

      if (!stats) {
        return;
      }

      // Verificar se já tem contagem de palavras
      if (stats.totalWords !== undefined && stats.totalWords > 0) {
        return; // Já migrado
      }

      // Obter todas as mensagens do chat
      const messages = await this.getChatMessages(userId, chatId);

      if (!messages || messages.length === 0) {
        return;
      }

      // Calcular contagem de palavras das mensagens existentes
      let totalWords = 0;
      let userWords = 0;
      let assistantWords = 0;

      for (const message of messages) {
        const wordCount = this.countWords(message.content);
        totalWords += wordCount;

        if (message.role === 'user') {
          userWords += wordCount;
        } else {
          assistantWords += wordCount;
        }
      }

      // Atualizar estatísticas
      stats.totalWords = totalWords;
      stats.userWords = userWords;
      stats.assistantWords = assistantWords;

      await this.saveChatStatistics(userId, chatId, stats);

      console.log(`Migrated word count for chat ${chatId}: ${totalWords} total words`);
    } catch (error) {
      console.error('Error migrating word count statistics:', error);
      throw error;
    }
  }

  /**
   * Obtém mensagens de um chat (função auxiliar para migração)
   */
  private async getChatMessages(userId: string, chatId: string): Promise<any[]> {
    try {
      const messagesRef = collection(db, 'users', userId, 'conversations', chatId, 'messages');
      const messagesSnapshot = await getDocs(query(messagesRef, orderBy('timestamp', 'asc')));

      return messagesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting chat messages:', error);
      return [];
    }
  }
}

export const chatStatisticsService = new ChatStatisticsServiceImpl();
