'use client';

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBalance } from '@/contexts/BalanceContext';
import { useAIChat } from '@/hooks/useAIChat';
import { useStreamingChat } from '@/hooks/useStreamingChat';
import { useChatModel } from '@/hooks/useChatModel';
import { useTemporaryChat } from '@/hooks/useTemporaryChat';
import { getStreamingSettings } from '@/lib/settingsService';
import { getPersonalizedGreeting, getWelcomeMessage, getTemporaryChatDescription } from '@/lib/greetingUtils';
import { temporaryAttachmentService } from '@/lib/temporaryAttachmentService';
import { TemporaryAttachmentMetadata, AttachmentMetadata } from '@/lib/types/chat';
import MessageBubble from './MessageBubble';
import ThinkingBubble from './ThinkingBubble';
import StreamingBubble from './StreamingBubble';
import ChatDownBar from './ChatDownBar';

interface TemporaryChatProps {
  onCreatePermanentChat: (messages: any[], temporaryAttachments?: TemporaryAttachmentMetadata[]) => void;
}

const TemporaryChat = ({ onCreatePermanentChat }: TemporaryChatProps) => {
  const { user, userData } = useAuth();
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Estados para streaming
  const [streamingEnabled, setStreamingEnabled] = useState(false);
  const [showThinking, setShowThinking] = useState(false);
  const [currentStreamingContent, setCurrentStreamingContent] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [messageInput, setMessageInput] = useState('');
  const [isSavingChat, setIsSavingChat] = useState(false);

  // Estados para anexos temporários
  const [temporaryAttachments, setTemporaryAttachments] = useState<TemporaryAttachmentMetadata[]>([]);

  // Hook para mensagens temporárias
  const {
    messages,
    loading: messagesLoading,
    error: messagesError,
    addTemporaryMessage,
    updateTemporaryMessage,
    removeTemporaryMessage,
    editTemporaryMessage,
    clearMessages,
    getMessagesForAI
  } = useTemporaryChat();

  // Hook para modelo de chat
  const { selectedModel, setSelectedModel } = useChatModel({
    userId: user?.uid || null,
    chatId: null // Chat temporário não tem ID
  });

  // Hook para IA (sem streaming)
  const {
    generateResponseWithMetadata,
    loading: aiLoading,
    error: aiError
  } = useAIChat({
    model: selectedModel,
    temperature: 0.7,
    maxTokens: 2048,
    latexInstructions: false,
    webSearchEnabled: false
  });

  // Hook para streaming
  const {
    generateStreamingResponse,
    loading: streamingLoading,
    error: streamingError,
    streamingContent,
    isStreaming: streamingActive,
    cancelStreaming
  } = useStreamingChat({
    model: selectedModel,
    temperature: 0.7,
    maxTokens: 2048,
    latexInstructions: false,
    webSearchEnabled: false
  });

  // Verificar configurações de streaming
  useEffect(() => {
    const checkStreamingSettings = async () => {
      if (user?.uid) {
        try {
          const settings = await getStreamingSettings(user.uid);
          setStreamingEnabled(settings.enabled);
        } catch (error) {
          console.error('Erro ao carregar configurações de streaming:', error);
          setStreamingEnabled(false);
        }
      }
    };

    checkStreamingSettings();
  }, [user?.uid]);

  // Atualizar conteúdo de streaming
  useEffect(() => {
    setCurrentStreamingContent(streamingContent);
    setIsStreaming(streamingActive);
  }, [streamingContent, streamingActive]);

  // Auto-scroll para o final
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, currentStreamingContent, showThinking]);

  // Função para lidar com upload de anexos temporários
  const handleTemporaryAttachmentUpload = async (files: FileList | null) => {
    if (!files || !user) return;

    try {
      const fileArray = Array.from(files);
      const result = await temporaryAttachmentService.uploadMultipleTemporaryFiles(user.uid, fileArray);

      if (result.success && result.attachments.length > 0) {
        setTemporaryAttachments(prev => [...prev, ...result.attachments]);
        console.log('Temporary files uploaded successfully:', result.attachments);
      }

      if (result.errors.length > 0) {
        console.error('Upload errors:', result.errors);
        // TODO: Show error toast
      }
    } catch (error) {
      console.error('Error uploading temporary files:', error);
    }
  };

  // Função para remover anexo temporário
  const handleRemoveTemporaryAttachment = (attachmentId: string) => {
    setTemporaryAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const handleSendMessage = async (content: string, attachments?: any[]) => {
    if ((!content.trim() && !attachments?.length) || !user || (aiLoading && !streamingEnabled) || (streamingLoading && streamingEnabled)) return;

    // Limpar o input
    setMessageInput('');

    try {
      // Mostrar thinking bubble imediatamente para melhor UX (apenas no modo não-streaming)
      if (!streamingEnabled) {
        setShowThinking(true);
      }

      // Adicionar mensagem do usuário
      addTemporaryMessage('user', content, attachments);

      // Preparar histórico da conversa
      const conversationHistory = getMessagesForAI();

      if (streamingEnabled) {
        // Usar streaming
        const result = await generateStreamingResponse(
          content,
          conversationHistory,
          attachments
        );

        if (result.success && result.response) {
          // Transform usage data to expected format (streaming already returns snake_case)
          const transformedUsage = result.metadata?.usage ? {
            prompt_tokens: result.metadata.usage.prompt_tokens || 0,
            completion_tokens: result.metadata.usage.completion_tokens || 0,
            total_tokens: result.metadata.usage.total_tokens || 0,
            cost: result.metadata.usage.cost || 0
          } : undefined;

          // Adicionar resposta da IA
          addTemporaryMessage('assistant', result.response, undefined, transformedUsage, result.metadata?.processingTime);

          // Nota: No chat temporário, não atualizamos o saldo real
          // O saldo será atualizado apenas quando o chat for salvo como permanente
        }
      } else {
        // Usar modo não-streaming (ThinkingBubble já foi mostrado no início)

        const result = await generateResponseWithMetadata(content, conversationHistory, undefined, attachments);

        setShowThinking(false);

        if (result.success && result.response) {
          // Transform usage data to expected format (non-streaming returns camelCase)
          const transformedUsage = result.metadata?.usage ? {
            prompt_tokens: result.metadata.usage.promptTokens || 0,
            completion_tokens: result.metadata.usage.completionTokens || 0,
            total_tokens: result.metadata.usage.totalTokens || 0,
            cost: 0 // Temporary chat doesn't track real costs
          } : undefined;

          // Adicionar resposta da IA
          addTemporaryMessage('assistant', result.response, undefined, transformedUsage, result.metadata?.processingTime);

          // Nota: No chat temporário, não atualizamos o saldo real
          // O saldo será atualizado apenas quando o chat for salvo como permanente
        }
      }
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      setShowThinking(false);
    }
  };

  const handleMessageAction = async (messageId: string, action: string) => {
    switch (action) {
      case 'delete':
        removeTemporaryMessage(messageId);
        break;
      case 'edit':
        // Implementar edição se necessário
        break;
      case 'copy':
        const message = messages.find(m => m.id === messageId);
        if (message) {
          navigator.clipboard.writeText(message.content);
        }
        break;
      case 'regenerate':
        await handleRegenerateFromMessage(messageId);
        break;
    }
  };

  const handleEditMessage = (messageId: string, newContent: string) => {
    editTemporaryMessage(messageId, newContent);
  };

  const handleRegenerateFromMessage = async (messageId: string) => {
    try {
      const userMessage = messages.find(m => m.id === messageId);
      if (!userMessage || userMessage.role !== 'user') {
        console.error('Mensagem do usuário não encontrada ou inválida');
        return;
      }

      console.log('Iniciando regeneração para mensagem temporária:', messageId);

      // Remover visualmente as mensagens após a mensagem do usuário IMEDIATAMENTE
      const messageIndex = messages.findIndex(m => m.id === messageId);
      if (messageIndex === -1) return;

      // Manter apenas as mensagens até a mensagem do usuário (inclusive)
      const messagesToKeep = messages.slice(0, messageIndex + 1);

      // Atualizar o estado das mensagens imediatamente
      messages.splice(messageIndex + 1);

      // Mostrar ThinkingBubble imediatamente para melhor UX
      if (!streamingEnabled) {
        setShowThinking(true);
      }

      // Preparar contexto para regeneração
      const contextMessages = messagesToKeep.slice(0, -1); // Excluir a mensagem atual do contexto

      console.log('Contexto preparado para regeneração:', contextMessages.length, 'mensagens');

      if (streamingEnabled) {
        // Usar streaming
        const result = await generateStreamingResponse(
          userMessage.content,
          contextMessages,
          userMessage.attachments
        );

        if (result.success && result.response) {
          // Transform usage data to expected format (streaming already returns snake_case)
          const transformedUsage = result.metadata?.usage ? {
            prompt_tokens: result.metadata.usage.prompt_tokens || 0,
            completion_tokens: result.metadata.usage.completion_tokens || 0,
            total_tokens: result.metadata.usage.total_tokens || 0,
            cost: result.metadata.usage.cost || 0
          } : undefined;

          // Adicionar nova resposta da IA
          addTemporaryMessage('assistant', result.response, undefined, transformedUsage, result.metadata?.processingTime);
        }
      } else {
        // Usar modo não-streaming (ThinkingBubble já foi mostrado no início)

        const result = await generateResponseWithMetadata(
          userMessage.content,
          contextMessages,
          undefined,
          userMessage.attachments
        );

        setShowThinking(false);

        if (result.success && result.response) {
          // Transform usage data to expected format (non-streaming returns camelCase)
          const transformedUsage = result.metadata?.usage ? {
            prompt_tokens: result.metadata.usage.promptTokens || 0,
            completion_tokens: result.metadata.usage.completionTokens || 0,
            total_tokens: result.metadata.usage.totalTokens || 0,
            cost: 0 // Temporary chat doesn't track real costs
          } : undefined;

          // Adicionar nova resposta da IA
          addTemporaryMessage('assistant', result.response, undefined, transformedUsage, result.metadata?.processingTime);
        }
      }

      console.log('Regeneração concluída com sucesso');

    } catch (error) {
      console.error('Erro ao regenerar mensagem temporária:', error);
      setShowThinking(false);
    }
  };

  const handleCreatePermanentChat = async () => {
    if (messages.length > 0 && !isSavingChat) {
      setIsSavingChat(true);
      try {
        await onCreatePermanentChat(getMessagesForAI(), temporaryAttachments);
      } catch (error) {
        console.error('Erro ao salvar chat:', error);
      } finally {
        setIsSavingChat(false);
      }
    }
  };

  const greeting = getPersonalizedGreeting(userData?.username);
  const welcomeMessage = getWelcomeMessage(userData?.username);
  const description = getTemporaryChatDescription();

  return (
    <div className="flex-1 flex flex-col h-screen bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 relative w-full max-w-full overflow-hidden">
      {/* Efeito de brilho sutil */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"></div>

      {/* Header da área temporária */}
      <div className="relative z-10 px-6 py-4 border-b border-blue-600/20 bg-blue-900/20 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-white text-glow-sm">
              {greeting} - Área de Experimentação
            </h2>
            <p className="text-sm text-blue-300/80 mt-1">
              {description}
            </p>
          </div>
          
          {messages.length > 0 && (
            <div className="flex gap-3">
              <button
                onClick={clearMessages}
                className="px-4 py-2 bg-red-600/20 hover:bg-red-600/30 border border-red-500/30 text-red-300 rounded-lg transition-all duration-200 text-sm"
                title="Limpar todas as mensagens temporárias"
              >
                Limpar
              </button>
              <button
                onClick={handleCreatePermanentChat}
                disabled={isSavingChat || messages.length === 0}
                className="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200 text-sm font-medium shadow-lg"
                title={isSavingChat ? "Salvando chat..." : "Salvar esta conversa como um chat permanente"}
              >
                {isSavingChat ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Salvando...
                  </>
                ) : (
                  'Salvar Chat'
                )}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Messages Area */}
      <div ref={messagesContainerRef} className="flex-1 overflow-y-auto overflow-x-hidden px-6 py-8 space-y-6 min-h-0 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-blue-950/20 via-transparent to-blue-950/20 pointer-events-none"></div>
        
        {messages.length === 0 && (
          <div className="flex items-center justify-center h-full relative z-10">
            <div className="text-center max-w-md">
              <div className="mb-6">
                <div className="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-blue-500/20 to-cyan-600/20 flex items-center justify-center border border-blue-500/30">
                  <svg className="w-10 h-10 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-2xl text-white font-bold mb-3 text-glow-sm">
                {welcomeMessage}
              </h3>
              <p className="text-blue-300/80 leading-relaxed mb-4">
                Experimente livremente! Esta é sua área de testes onde você pode conversar sem compromisso.
              </p>
              <div className="text-sm text-blue-400/70 bg-blue-900/20 rounded-lg p-3 border border-blue-600/20 mb-3">
                💡 <strong>Dica:</strong> Suas mensagens ficam apenas na memória. Se gostar da conversa, use o botão "💾 Salvar Chat" para torná-la permanente.
              </div>
              {temporaryAttachments.length > 0 && (
                <div className="text-sm text-amber-400/70 bg-amber-900/20 rounded-lg p-3 border border-amber-600/20">
                  📎 <strong>Anexos Temporários:</strong> {temporaryAttachments.length} arquivo(s) anexado(s).
                  Os anexos serão automaticamente removidos após 10 horas.
                </div>
              )}
            </div>
          </div>
        )}

        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            onAction={handleMessageAction}
            onEditSave={handleEditMessage}
            isFavorite={false}
          />
        ))}

        {/* Thinking Bubble */}
        {showThinking && !streamingEnabled && (
          <ThinkingBubble model={selectedModel} />
        )}

        {/* Streaming Bubble */}
        {isStreaming && streamingEnabled && (
          <StreamingBubble
            content={currentStreamingContent}
            model={selectedModel}
            isStreaming={isStreaming}
          />
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <div className="relative z-10">
        <ChatDownBar
          messageInput={messageInput}
          setMessageInput={setMessageInput}
          onSendMessage={handleSendMessage}
          isLoading={(aiLoading && !streamingEnabled) || (streamingLoading && streamingEnabled)}
          selectedModel={selectedModel || 'google/gemini-2.5-flash'}
          onModelChange={setSelectedModel}
          isStreaming={isStreaming && streamingEnabled}
          onCancelStreaming={cancelStreaming}
          externalAttachments={temporaryAttachments}
          onAttachmentsChange={(attachments: AttachmentMetadata[]) => {
            // Convert AttachmentMetadata[] to TemporaryAttachmentMetadata[]
            const temporaryAttachments = attachments.map(att => ({
              ...att,
              expiresAt: Date.now() + (10 * 60 * 60 * 1000), // 10 hours from now
              isTemporary: true as const
            }));
            setTemporaryAttachments(temporaryAttachments);
          }}
          selectedAttachmentsCount={temporaryAttachments.length}
          chatId="temporary-chat"
          chatName="Área de Experimentação"
        />
      </div>
    </div>
  );
};

export default TemporaryChat;
