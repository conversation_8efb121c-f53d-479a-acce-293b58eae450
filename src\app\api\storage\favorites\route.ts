import { NextRequest, NextResponse } from 'next/server';
import { storage } from '@/lib/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, data } = body;

    // Validar parâmetros obrigatórios
    if (!userId || !data) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, data' },
        { status: 400 }
      );
    }

    // Converter dados para JSON
    const jsonData = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });

    // Criar referência no Storage
    const favoritesRef = ref(storage, `usuarios/${userId}/favoritos.json`);

    // Upload do arquivo
    await uploadBytes(favoritesRef, blob);

    console.log(`Favorites saved for user: ${userId}`);

    return NextResponse.json({ 
      success: true, 
      message: 'Favorites saved successfully' 
    });

  } catch (error) {
    console.error('Error saving favorites:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const userId = request.nextUrl.searchParams.get('userId');

    // Validar parâmetros obrigatórios
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }

    try {
      // Criar referência no Storage
      const favoritesRef = ref(storage, `usuarios/${userId}/favoritos.json`);
      
      // Obter URL de download
      const downloadURL = await getDownloadURL(favoritesRef);
      
      // Buscar dados
      const response = await fetch(downloadURL);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();

      return NextResponse.json({ 
        success: true, 
        data 
      });

    } catch (error) {
      // Se o arquivo não existir, retornar dados vazios
      if (error instanceof Error && error.message.includes('object-not-found')) {
        return NextResponse.json({ 
          success: true, 
          data: { favorites: [], lastUpdated: Date.now() }
        });
      }
      
      throw error;
    }

  } catch (error) {
    console.error('Error getting favorites:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
