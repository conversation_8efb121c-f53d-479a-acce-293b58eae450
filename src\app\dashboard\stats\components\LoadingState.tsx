'use client';

export default function LoadingState() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 text-center">
        {/* Loading animation */}
        <div className="mb-8">
          <div className="relative">
            {/* Outer ring */}
            <div className="w-20 h-20 border-4 border-white/20 rounded-full animate-spin">
              <div className="absolute top-0 left-0 w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
            </div>
            
            {/* Inner ring */}
            <div className="absolute top-2 left-2 w-16 h-16 border-4 border-white/10 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}>
              <div className="absolute top-0 left-0 w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Loading text */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-white">Carregando Analytics</h2>
          <p className="text-white/60 max-w-md mx-auto">
            Analisando suas conversas e gerando insights inteligentes...
          </p>
          
          {/* Loading steps */}
          <div className="space-y-2 mt-8">
            <div className="flex items-center justify-center space-x-2 text-sm text-white/50">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
              <span>Coletando dados das conversas</span>
            </div>
            <div className="flex items-center justify-center space-x-2 text-sm text-white/50">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-300"></div>
              <span>Calculando métricas de performance</span>
            </div>
            <div className="flex items-center justify-center space-x-2 text-sm text-white/50">
              <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse delay-700"></div>
              <span>Gerando insights personalizados</span>
            </div>
          </div>
        </div>

        {/* Floating elements */}
        <div className="absolute -top-10 -left-10 w-4 h-4 bg-purple-500/30 rounded-full animate-bounce delay-1000"></div>
        <div className="absolute -top-5 right-10 w-3 h-3 bg-pink-500/30 rounded-full animate-bounce delay-1500"></div>
        <div className="absolute bottom-0 -left-5 w-5 h-5 bg-blue-500/30 rounded-full animate-bounce delay-500"></div>
      </div>
    </div>
  );
}
