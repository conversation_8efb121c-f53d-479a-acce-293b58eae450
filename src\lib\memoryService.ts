import { db } from './firebase';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  getDocs,
  deleteDoc,
  query,
  where,
  orderBy
} from 'firebase/firestore';
import { Memory, MemoryCategory, MemoryFilters } from './settingsService';

class MemoryService {
  
  // ==================== CATEGORIAS ====================
  
  /**
   * Cria uma nova categoria de memória
   */
  async createCategory(userId: string, category: Omit<MemoryCategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const categoryId = `category-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const categoryData: any = {
        id: categoryId,
        name: category.name,
        backgroundColor: category.backgroundColor,
        isActive: category.isActive,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      // Adicionar descrição apenas se não for undefined
      if (category.description) {
        categoryData.description = category.description;
      }

      await setDoc(doc(db, 'usuarios', userId, 'memorias', 'categorias', 'data', categoryId), categoryData);
      return categoryId;
    } catch (error) {
      console.error('Erro ao criar categoria:', error);
      throw error;
    }
  }

  /**
   * Obtém todas as categorias do usuário
   */
  async getCategories(userId: string): Promise<MemoryCategory[]> {
    try {
      const categoriesRef = collection(db, 'usuarios', userId, 'memorias', 'categorias', 'data');
      const q = query(categoriesRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => doc.data() as MemoryCategory);
    } catch (error) {
      console.error('Erro ao buscar categorias:', error);
      throw error;
    }
  }

  /**
   * Atualiza uma categoria
   */
  async updateCategory(userId: string, categoryId: string, updates: Partial<MemoryCategory>): Promise<void> {
    try {
      const categoryRef = doc(db, 'usuarios', userId, 'memorias', 'categorias', 'data', categoryId);

      // Filtrar campos undefined
      const updateData: any = {
        updatedAt: Date.now()
      };

      // Adicionar apenas campos que não são undefined
      Object.keys(updates).forEach(key => {
        const value = (updates as any)[key];
        if (value !== undefined) {
          updateData[key] = value;
        }
      });

      await updateDoc(categoryRef, updateData);
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error);
      throw error;
    }
  }

  /**
   * Deleta uma categoria e todas as suas memórias
   */
  async deleteCategory(userId: string, categoryId: string): Promise<void> {
    try {
      // Primeiro, deletar todas as memórias da categoria
      const memories = await this.getMemories(userId, { categoryId });
      for (const memory of memories) {
        await this.deleteMemory(userId, memory.id);
      }

      // Depois, deletar a categoria
      await deleteDoc(doc(db, 'usuarios', userId, 'memorias', 'categorias', 'data', categoryId));
    } catch (error) {
      console.error('Erro ao deletar categoria:', error);
      throw error;
    }
  }

  /**
   * Ativa/desativa todas as memórias de uma categoria
   */
  async toggleCategoryMemories(userId: string, categoryId: string, isActive: boolean): Promise<void> {
    try {
      const memories = await this.getMemories(userId, { categoryId });
      
      // Atualizar todas as memórias da categoria
      const updatePromises = memories.map(memory => 
        this.updateMemory(userId, memory.id, { isActive })
      );
      
      await Promise.all(updatePromises);
      
      // Atualizar o status da categoria
      await this.updateCategory(userId, categoryId, { isActive });
    } catch (error) {
      console.error('Erro ao alternar memórias da categoria:', error);
      throw error;
    }
  }

  // ==================== MEMÓRIAS ====================

  /**
   * Cria uma nova memória
   */
  async createMemory(userId: string, memory: Omit<Memory, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const memoryId = `memory-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const memoryData: any = {
        id: memoryId,
        title: memory.title,
        content: memory.content,
        backgroundColor: memory.backgroundColor,
        isActive: memory.isActive,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      // Adicionar campos opcionais apenas se não forem undefined
      if (memory.categoryId) {
        memoryData.categoryId = memory.categoryId;
      }
      if (memory.chatId) {
        memoryData.chatId = memory.chatId;
      }

      await setDoc(doc(db, 'usuarios', userId, 'memorias', 'items', 'data', memoryId), memoryData);
      return memoryId;
    } catch (error) {
      console.error('Erro ao criar memória:', error);
      throw error;
    }
  }

  /**
   * Obtém memórias do usuário com filtros opcionais
   */
  async getMemories(userId: string, filters?: MemoryFilters): Promise<Memory[]> {
    try {
      const memoriesRef = collection(db, 'usuarios', userId, 'memorias', 'items', 'data');
      let q = query(memoriesRef, orderBy('createdAt', 'desc'));

      const snapshot = await getDocs(q);
      let memories = snapshot.docs.map(doc => doc.data() as Memory);

      // Aplicar filtros localmente (Firestore tem limitações com queries complexas)
      if (filters) {
        if (filters.categoryId !== undefined) {
          memories = memories.filter(memory => memory.categoryId === filters.categoryId);
        }
        if (filters.chatId !== undefined) {
          memories = memories.filter(memory => memory.chatId === filters.chatId);
        }
        if (filters.isActive !== undefined) {
          memories = memories.filter(memory => memory.isActive === filters.isActive);
        }
      }

      return memories;
    } catch (error) {
      console.error('Erro ao buscar memórias:', error);
      throw error;
    }
  }

  /**
   * Obtém uma memória específica
   */
  async getMemory(userId: string, memoryId: string): Promise<Memory | null> {
    try {
      const memoryRef = doc(db, 'usuarios', userId, 'memorias', 'items', 'data', memoryId);
      const snapshot = await getDoc(memoryRef);
      
      if (snapshot.exists()) {
        return snapshot.data() as Memory;
      }
      return null;
    } catch (error) {
      console.error('Erro ao buscar memória:', error);
      throw error;
    }
  }

  /**
   * Atualiza uma memória
   */
  async updateMemory(userId: string, memoryId: string, updates: Partial<Memory>): Promise<void> {
    try {
      const memoryRef = doc(db, 'usuarios', userId, 'memorias', 'items', 'data', memoryId);

      // Filtrar campos undefined
      const updateData: any = {
        updatedAt: Date.now()
      };

      // Adicionar apenas campos que não são undefined
      Object.keys(updates).forEach(key => {
        const value = (updates as any)[key];
        if (value !== undefined) {
          updateData[key] = value;
        }
      });

      await updateDoc(memoryRef, updateData);
    } catch (error) {
      console.error('Erro ao atualizar memória:', error);
      throw error;
    }
  }

  /**
   * Deleta uma memória
   */
  async deleteMemory(userId: string, memoryId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'usuarios', userId, 'memorias', 'items', 'data', memoryId));
    } catch (error) {
      console.error('Erro ao deletar memória:', error);
      throw error;
    }
  }

  // ==================== UTILITÁRIOS ====================

  /**
   * Obtém memórias ativas para um chat específico (globais + específicas do chat)
   */
  async getActiveMemoriesForChat(userId: string, chatId?: string): Promise<Memory[]> {
    try {
      console.log('🔍 getActiveMemoriesForChat - userId:', userId, 'chatId:', chatId);

      // Buscar memórias globais ativas (sem chatId específico)
      const globalMemories = await this.getMemories(userId, {
        isActive: true
      });

      console.log('📋 Todas as memórias ativas encontradas:', globalMemories.length, globalMemories);

      // Filtrar apenas memórias globais (sem chatId) e específicas do chat atual
      const relevantMemories = globalMemories.filter(memory =>
        !memory.chatId || memory.chatId === chatId
      );

      console.log('✅ Memórias relevantes para o chat:', relevantMemories.length, relevantMemories);

      return relevantMemories;
    } catch (error) {
      console.error('Erro ao buscar memórias ativas para chat:', error);
      throw error;
    }
  }

  /**
   * Formata memórias para inclusão no system prompt
   */
  formatMemoriesForPrompt(memories: Memory[]): string {
    if (memories.length === 0) return '';

    const memoryTexts = memories.map(memory => 
      `• ${memory.title}: ${memory.content}`
    ).join('\n');

    return `\n\n--------------------\nEXTREMAMENTE IMPORTANTE: Memórias do usuário\n---------\n${memoryTexts}\n--------------------\n`;
  }
}

export const memoryService = new MemoryService();
export default memoryService;
