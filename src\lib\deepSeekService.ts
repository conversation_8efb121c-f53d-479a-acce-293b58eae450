import { AIModel } from './types/chat';

export interface DeepSeekModel {
  id: string;
  name: string;
  description: string;
  context_length: number;
  pricing: {
    prompt: string;
    completion: string;
    image?: string;
  };
  architecture?: {
    modality: string;
    tokenizer: string;
    instruct_type?: string;
  };
  created?: number;
}

class DeepSeekService {
  private cache: AIModel[] | null = null;
  private cacheTimestamp: number = 0;
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  /**
   * Get available DeepSeek models from API
   */
  async fetchModels(): Promise<AIModel[]> {
    // Check cache first
    if (this.cache && Date.now() - this.cacheTimestamp < this.cacheExpiry) {
      return this.cache;
    }

    try {
      const response = await fetch('/api/deepseek/models');

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch models');
      }

      // Transform DeepSeek models to our AIModel format
      const models: AIModel[] = result.data.map((model: DeepSeekModel) => ({
        id: model.id,
        name: model.name,
        description: model.description,
        context_length: model.context_length,
        pricing: {
          prompt: model.pricing.prompt,
          completion: model.pricing.completion,
          image: model.pricing.image || '0'
        },
        architecture: model.architecture,
        created: model.created,

      }));

      // Update cache
      this.cache = models;
      this.cacheTimestamp = Date.now();

      return models;
    } catch (error) {
      console.error('Error fetching DeepSeek models:', error);
      throw error;
    }
  }

  /**
   * Filter models by category
   */
  filterByCategory(models: AIModel[], category: 'paid' | 'free'): AIModel[] {
    switch (category) {
      case 'free':
        return []; // DeepSeek models are all paid
      case 'paid':
        return models; // All DeepSeek models are paid
      default:
        return models;
    }
  }

  /**
   * Sort models by specified criteria
   */
  sortModels(models: AIModel[], sortBy: 'newest' | 'price_low' | 'price_high' | 'context_high'): AIModel[] {
    const sortedModels = [...models];

    switch (sortBy) {
      case 'newest':
        return sortedModels.sort((a, b) => (b.created || 0) - (a.created || 0));
      
      case 'price_low':
        return sortedModels.sort((a, b) => {
          const priceA = parseFloat(a.pricing.prompt) + parseFloat(a.pricing.completion);
          const priceB = parseFloat(b.pricing.prompt) + parseFloat(b.pricing.completion);
          return priceA - priceB;
        });
      
      case 'price_high':
        return sortedModels.sort((a, b) => {
          const priceA = parseFloat(a.pricing.prompt) + parseFloat(a.pricing.completion);
          const priceB = parseFloat(b.pricing.prompt) + parseFloat(b.pricing.completion);
          return priceB - priceA;
        });
      
      case 'context_high':
        return sortedModels.sort((a, b) => b.context_length - a.context_length);
      
      default:
        return sortedModels;
    }
  }

  /**
   * Search models by name (legacy method - kept for compatibility)
   * @deprecated Use advancedSearchService.searchModels instead
   */
  searchModels(models: AIModel[], searchTerm: string): AIModel[] {
    if (!searchTerm.trim()) return models;

    const term = searchTerm.toLowerCase();
    return models.filter(model =>
      model.name.toLowerCase().includes(term) ||
      model.id.toLowerCase().includes(term) ||
      (model.description && model.description.toLowerCase().includes(term))
    );
  }

  /**
   * Format price for display
   */
  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    if (numPrice === 0) return 'Grátis';
    if (numPrice < 0.01) return '< $0.01';
    if (numPrice < 1) return `$${numPrice.toFixed(3)}`;
    return `$${numPrice.toFixed(2)}`;
  }

  /**
   * Format context length for display
   */
  formatContextLength(contextLength: number): string {
    if (contextLength >= 1000000) {
      return `${(contextLength / 1000000).toFixed(1)}M`;
    } else if (contextLength >= 1000) {
      return `${(contextLength / 1000).toFixed(0)}K`;
    }
    return contextLength.toString();
  }

  /**
   * Check if model is free
   */
  isFreeModel(model: AIModel): boolean {
    return parseFloat(model.pricing.prompt) === 0 && 
           parseFloat(model.pricing.completion) === 0;
  }

  /**
   * Get total price per million tokens (input + output)
   */
  getTotalPrice(model: AIModel): number {
    return parseFloat(model.pricing.prompt) + parseFloat(model.pricing.completion);
  }
}

export const deepSeekService = new DeepSeekService();
