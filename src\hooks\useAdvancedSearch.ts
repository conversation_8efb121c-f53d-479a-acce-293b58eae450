import { useState, useEffect, useCallback, useMemo } from 'react';
import { AIModel } from '@/lib/types/chat';
import { advancedSearchService, SearchResult, SearchOptions, SearchSuggestion } from '@/lib/advancedSearchService';
import { searchAnalyticsService } from '@/lib/searchAnalyticsService';

interface UseAdvancedSearchOptions extends SearchOptions {
  debounceMs?: number;
  enableSuggestions?: boolean;
  cacheResults?: boolean;
  userId?: string | null;
  trackAnalytics?: boolean;
}

interface UseAdvancedSearchReturn {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  searchResults: SearchResult[];
  suggestions: SearchSuggestion[];
  isSearching: boolean;
  hasSearched: boolean;
  clearSearch: () => void;
  searchHistory: string[];
  popularSearches: Array<{ term: string; count: number }>;
  trackModelSelection: (modelId: string) => void;
}

// Cache para resultados de busca
const searchCache = new Map<string, { results: SearchResult[]; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

/**
 * Hook personalizado para busca avançada com debounce e cache
 */
export function useAdvancedSearch(
  models: AIModel[],
  options: UseAdvancedSearchOptions = {}
): UseAdvancedSearchReturn {
  const {
    debounceMs = 300,
    enableSuggestions = true,
    cacheResults = true,
    userId = null,
    trackAnalytics = true,
    ...searchOptions
  } = options;

  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // Debounce do termo de busca
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  // Função para limpar cache expirado
  const cleanExpiredCache = useCallback(() => {
    const now = Date.now();
    for (const [key, value] of searchCache.entries()) {
      if (now - value.timestamp > CACHE_DURATION) {
        searchCache.delete(key);
      }
    }
  }, []);

  // Função para obter resultados do cache
  const getCachedResults = useCallback((term: string): SearchResult[] | null => {
    if (!cacheResults) return null;
    
    const cached = searchCache.get(term);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.results;
    }
    return null;
  }, [cacheResults]);

  // Função para armazenar resultados no cache
  const setCachedResults = useCallback((term: string, results: SearchResult[]) => {
    if (!cacheResults) return;
    
    searchCache.set(term, {
      results,
      timestamp: Date.now()
    });
    
    // Limpar cache expirado periodicamente
    if (searchCache.size > 50) {
      cleanExpiredCache();
    }
  }, [cacheResults, cleanExpiredCache]);

  // Executar busca quando o termo debounced muda
  useEffect(() => {
    if (!models.length) return;

    const performSearch = async () => {
      if (!debouncedSearchTerm.trim()) {
        setSearchResults([]);
        setSuggestions([]);
        setIsSearching(false);
        setHasSearched(false);
        return;
      }

      setIsSearching(true);
      setHasSearched(true);

      try {
        // Verificar cache primeiro
        const cachedResults = getCachedResults(debouncedSearchTerm);
        if (cachedResults) {
          setSearchResults(cachedResults);
          setIsSearching(false);
          return;
        }

        // Executar busca
        const results = advancedSearchService.searchModels(
          models,
          debouncedSearchTerm,
          searchOptions
        );

        setSearchResults(results);
        setCachedResults(debouncedSearchTerm, results);

        // Registrar analytics se habilitado
        if (trackAnalytics) {
          searchAnalyticsService.trackSearch(
            userId,
            debouncedSearchTerm,
            results.length
          );
        }

        // Gerar sugestões se habilitado
        if (enableSuggestions && debouncedSearchTerm.length >= 2) {
          const newSuggestions = advancedSearchService.generateSuggestions(
            debouncedSearchTerm,
            models
          );
          setSuggestions(newSuggestions);
        } else {
          setSuggestions([]);
        }
      } catch (error) {
        console.error('Erro na busca:', error);
        setSearchResults([]);
        setSuggestions([]);
      } finally {
        setIsSearching(false);
      }
    };

    performSearch();
  }, [debouncedSearchTerm, models, searchOptions, enableSuggestions, getCachedResults, setCachedResults]);

  // Gerar sugestões em tempo real (sem debounce) para autocomplete
  useEffect(() => {
    if (!enableSuggestions || !searchTerm || searchTerm.length < 2) {
      setSuggestions([]);
      return;
    }

    // Apenas gerar sugestões se não estivermos fazendo busca completa
    if (searchTerm !== debouncedSearchTerm) {
      const quickSuggestions = advancedSearchService.generateSuggestions(
        searchTerm,
        models
      );
      setSuggestions(quickSuggestions);
    }
  }, [searchTerm, debouncedSearchTerm, models, enableSuggestions]);

  // Função para limpar busca
  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setDebouncedSearchTerm('');
    setSearchResults([]);
    setSuggestions([]);
    setIsSearching(false);
    setHasSearched(false);
  }, []);

  // Função para rastrear seleção de modelo
  const trackModelSelection = useCallback((modelId: string) => {
    if (trackAnalytics && hasSearched && searchTerm.trim()) {
      searchAnalyticsService.trackModelSelection(userId, searchTerm, modelId);
    }
  }, [trackAnalytics, hasSearched, searchTerm, userId]);

  // Memoizar histórico e buscas populares
  const searchHistory = useMemo(() => {
    return advancedSearchService.getSearchHistory();
  }, [debouncedSearchTerm]); // Atualizar quando uma nova busca for feita

  const popularSearches = useMemo(() => {
    return advancedSearchService.getPopularSearches();
  }, [debouncedSearchTerm]);

  return {
    searchTerm,
    setSearchTerm,
    searchResults,
    suggestions,
    isSearching,
    hasSearched,
    clearSearch,
    searchHistory,
    popularSearches,
    trackModelSelection
  };
}

/**
 * Hook para gerenciar sugestões de busca
 */
export function useSearchSuggestions(models: AIModel[]) {
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  
  const generateSuggestions = useCallback((partialTerm: string) => {
    if (partialTerm.length < 2) {
      setSuggestions([]);
      return;
    }
    
    const newSuggestions = advancedSearchService.generateSuggestions(partialTerm, models);
    setSuggestions(newSuggestions);
  }, [models]);
  
  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
  }, []);
  
  return {
    suggestions,
    generateSuggestions,
    clearSuggestions
  };
}

/**
 * Hook para gerenciar histórico de busca
 */
export function useSearchHistory() {
  const [history, setHistory] = useState<string[]>([]);
  const [popularSearches, setPopularSearches] = useState<Array<{ term: string; count: number }>>([]);
  
  const refreshHistory = useCallback(() => {
    setHistory(advancedSearchService.getSearchHistory());
    setPopularSearches(advancedSearchService.getPopularSearches());
  }, []);
  
  const clearHistory = useCallback(() => {
    advancedSearchService.clearHistory();
    refreshHistory();
  }, [refreshHistory]);
  
  useEffect(() => {
    refreshHistory();
  }, [refreshHistory]);
  
  return {
    history,
    popularSearches,
    refreshHistory,
    clearHistory
  };
}
