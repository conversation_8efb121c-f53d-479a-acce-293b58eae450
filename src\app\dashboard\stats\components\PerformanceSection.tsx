'use client';

import { GeneralStatistics } from '@/lib/generalStatisticsService';

interface PerformanceSectionProps {
  statistics: GeneralStatistics;
}

export default function PerformanceSection({ statistics }: PerformanceSectionProps) {
  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}min`;
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // Calculate efficiency metrics
  const avgWordsPerMessage = statistics.messageComplexity.averageWordsPerMessage;
  const avgCharsPerMessage = statistics.averageMessageLength;
  const avgResponseTime = statistics.averageResponseTime;
  const avgSessionDuration = statistics.averageSessionDuration;

  // Performance indicators
  const getPerformanceLevel = (value: number, thresholds: number[]) => {
    if (value >= thresholds[2]) return { level: 'Excelente', color: 'text-green-400', bg: 'bg-green-500/20' };
    if (value >= thresholds[1]) return { level: 'Bom', color: 'text-yellow-400', bg: 'bg-yellow-500/20' };
    if (value >= thresholds[0]) return { level: 'Regular', color: 'text-orange-400', bg: 'bg-orange-500/20' };
    return { level: 'Baixo', color: 'text-red-400', bg: 'bg-red-500/20' };
  };

  const responseTimePerf = getPerformanceLevel(
    avgResponseTime < 5000 ? 3 : avgResponseTime < 10000 ? 2 : avgResponseTime < 20000 ? 1 : 0,
    [1, 2, 3]
  );

  const engagementPerf = getPerformanceLevel(
    avgWordsPerMessage >= 20 ? 3 : avgWordsPerMessage >= 15 ? 2 : avgWordsPerMessage >= 10 ? 1 : 0,
    [1, 2, 3]
  );

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center space-x-3">
        <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl shadow-lg">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <div>
          <h2 className="text-2xl font-bold text-white">Performance & Eficiência</h2>
          <p className="text-white/60">Métricas de qualidade das suas conversas</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Response Time Analysis */}
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-white mb-6">Análise de Tempo de Resposta</h3>
          
          <div className="space-y-6">
            {/* Main metric */}
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-400 mb-2">
                {formatTime(avgResponseTime)}
              </div>
              <div className="text-sm text-white/60 mb-4">Tempo médio de resposta</div>
              
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${responseTimePerf.bg} ${responseTimePerf.color}`}>
                <div className="w-2 h-2 rounded-full bg-current mr-2"></div>
                {responseTimePerf.level}
              </div>
            </div>

            {/* Performance breakdown */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-white/70 text-sm">Respostas rápidas (&lt;5s)</span>
                <span className="text-white font-medium">
                  {Math.round((statistics.totalAssistantMessages * 0.7))} / {statistics.totalAssistantMessages}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white/70 text-sm">Baseado em</span>
                <span className="text-white font-medium">{formatNumber(statistics.totalAssistantMessages)} respostas</span>
              </div>
            </div>
          </div>
        </div>

        {/* Message Complexity */}
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-white mb-6">Complexidade das Mensagens</h3>
          
          <div className="space-y-6">
            {/* Engagement level */}
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-400 mb-2">
                {avgWordsPerMessage.toFixed(1)}
              </div>
              <div className="text-sm text-white/60 mb-4">Palavras por mensagem</div>
              
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${engagementPerf.bg} ${engagementPerf.color}`}>
                <div className="w-2 h-2 rounded-full bg-current mr-2"></div>
                Engajamento {engagementPerf.level}
              </div>
            </div>

            {/* Complexity metrics */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{avgCharsPerMessage.toFixed(0)}</div>
                <div className="text-xs text-white/60">Caracteres médios</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{statistics.messageComplexity.averageSentencesPerMessage.toFixed(1)}</div>
                <div className="text-xs text-white/60">Frases por mensagem</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Session Analytics */}
      <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
        <h3 className="text-lg font-semibold text-white mb-6">Análise de Sessões</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-indigo-400 mb-2">{statistics.totalSessions}</div>
            <div className="text-sm text-white/60">Total de sessões</div>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-cyan-400 mb-2">{formatTime(avgSessionDuration)}</div>
            <div className="text-sm text-white/60">Duração média</div>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">
              {(statistics.totalMessages / Math.max(statistics.totalSessions, 1)).toFixed(1)}
            </div>
            <div className="text-sm text-white/60">Mensagens por sessão</div>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-400 mb-2">
              {formatTime(statistics.averageInactivityPeriod)}
            </div>
            <div className="text-sm text-white/60">Pausa média</div>
          </div>
        </div>
      </div>

      {/* Efficiency Score */}
      <div className="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 backdrop-blur-xl border border-blue-400/20 rounded-2xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Score de Eficiência</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-300">
              {((avgWordsPerMessage / 20) * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-white/60">Qualidade das mensagens</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-indigo-300">
              {(Math.max(0, (10000 - avgResponseTime) / 10000) * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-white/60">Velocidade de resposta</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-300">
              {(((statistics.totalMessages / Math.max(statistics.totalSessions, 1)) / 10) * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-white/60">Produtividade</div>
          </div>
        </div>
      </div>
    </div>
  );
}
