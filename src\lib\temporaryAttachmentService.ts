import { storage } from './firebase';
import { ref, uploadBytes, getDownloadURL, deleteObject, listAll } from 'firebase/storage';
import { TemporaryAttachmentMetadata, AttachmentMetadata } from './types/chat';

interface FileUploadResult {
  success: boolean;
  attachment?: TemporaryAttachmentMetadata;
  error?: string;
}

interface MultipleFileUploadResult {
  success: boolean;
  attachments: TemporaryAttachmentMetadata[];
  errors: string[];
}

class TemporaryAttachmentService {
  // Tipos de arquivo suportados
  private readonly SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ];

  private readonly SUPPORTED_PDF_TYPES = ['application/pdf'];

  // Limites de tamanho
  private readonly MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly MAX_PDF_SIZE = 25 * 1024 * 1024; // 25MB

  // Duração de vida dos arquivos temporários (10 horas em milissegundos)
  private readonly EXPIRATION_TIME = 10 * 60 * 60 * 1000; // 10 horas

  /**
   * Valida se um arquivo pode ser anexado
   */
  private validateFile(file: File): { valid: boolean; error?: string } {
    const allSupportedTypes = [...this.SUPPORTED_IMAGE_TYPES, ...this.SUPPORTED_PDF_TYPES];
    
    if (!allSupportedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Tipo de arquivo não suportado. Use apenas imagens (JPEG, PNG, GIF, WebP) ou PDFs.'
      };
    }

    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);
    const maxSize = isImage ? this.MAX_IMAGE_SIZE : this.MAX_PDF_SIZE;
    
    if (file.size > maxSize) {
      const maxSizeMB = maxSize / (1024 * 1024);
      return {
        valid: false,
        error: `Arquivo muito grande. Tamanho máximo: ${maxSizeMB}MB`
      };
    }

    return { valid: true };
  }

  /**
   * Gera um ID único para o anexo temporário
   */
  private generateAttachmentId(): string {
    return `temp-attachment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Determina o tipo do anexo baseado no tipo MIME
   */
  private getAttachmentType(mimeType: string): 'image' | 'pdf' {
    return this.SUPPORTED_IMAGE_TYPES.includes(mimeType) ? 'image' : 'pdf';
  }

  /**
   * Faz upload de um arquivo temporário para o Firebase Storage
   */
  async uploadTemporaryFile(
    userId: string,
    file: File
  ): Promise<FileUploadResult> {
    try {
      // Validar arquivo
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // Gerar ID único para o anexo
      const attachmentId = this.generateAttachmentId();
      const fileExtension = file.name.split('.').pop() || '';
      const fileName = `${attachmentId}.${fileExtension}`;

      // Criar referência no Storage (pasta específica para arquivos temporários)
      const attachmentPath = `usuarios/${userId}/temporarios/${fileName}`;
      const attachmentRef = ref(storage, attachmentPath);

      // Upload do arquivo com metadados customizados
      console.log(`Uploading temporary file to: ${attachmentPath}`);
      const now = Date.now();
      const expiresAt = now + this.EXPIRATION_TIME;
      
      const metadata = {
        customMetadata: {
          originalName: file.name,
          isTemporary: 'true',
          expiresAt: expiresAt.toString(),
          uploadedAt: now.toString()
        }
      };
      
      await uploadBytes(attachmentRef, file, metadata);

      // Obter URL de download com token de longa duração
      const downloadURL = await getDownloadURL(attachmentRef);

      // Para arquivos temporários, sempre converter para base64 para evitar problemas de timeout
      let base64Data: string | undefined;
      try {
        base64Data = await this.fileToBase64(file);
        console.log(`Temporary file converted to base64 during upload, type: ${file.type}, length:`, base64Data.length);
      } catch (error) {
        console.error('Error converting temporary file to base64 during upload:', error);
      }

      // Criar metadata do anexo temporário
      const attachment: TemporaryAttachmentMetadata = {
        id: attachmentId,
        type: this.getAttachmentType(file.type),
        filename: file.name,
        url: downloadURL,
        size: file.size,
        uploadedAt: now,
        expiresAt,
        isTemporary: true,
        ...(base64Data && { base64Data })
      };

      console.log(`Temporary file uploaded successfully:`, attachment);

      return {
        success: true,
        attachment
      };

    } catch (error) {
      console.error('Error uploading temporary file:', error);
      return {
        success: false,
        error: 'Erro ao fazer upload do arquivo temporário. Tente novamente.'
      };
    }
  }

  /**
   * Faz upload de múltiplos arquivos temporários
   */
  async uploadMultipleTemporaryFiles(
    userId: string,
    files: File[]
  ): Promise<MultipleFileUploadResult> {
    const attachments: TemporaryAttachmentMetadata[] = [];
    const errors: string[] = [];

    for (const file of files) {
      const result = await this.uploadTemporaryFile(userId, file);
      
      if (result.success && result.attachment) {
        attachments.push(result.attachment);
      } else if (result.error) {
        errors.push(`${file.name}: ${result.error}`);
      }
    }

    return {
      success: attachments.length > 0,
      attachments,
      errors
    };
  }

  /**
   * Converte arquivo para base64 (usado para PDFs na API)
   */
  async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = () => reject(new Error('Erro ao ler arquivo'));
      reader.readAsDataURL(file);
    });
  }

  /**
   * Lista todos os arquivos temporários expirados para um usuário
   */
  async listExpiredTemporaryFiles(userId: string): Promise<string[]> {
    try {
      const tempFolderRef = ref(storage, `usuarios/${userId}/temporarios`);
      const listResult = await listAll(tempFolderRef);
      const expiredFiles: string[] = [];
      const now = Date.now();

      for (const itemRef of listResult.items) {
        try {
          const { getMetadata } = await import('firebase/storage');
          const metadata = await getMetadata(itemRef);
          const expiresAt = parseInt(metadata.customMetadata?.expiresAt || '0');
          
          if (expiresAt > 0 && now > expiresAt) {
            expiredFiles.push(itemRef.fullPath);
          }
        } catch (error) {
          console.error(`Error checking metadata for ${itemRef.fullPath}:`, error);
          // Se não conseguir ler metadados, considerar como expirado para limpeza
          expiredFiles.push(itemRef.fullPath);
        }
      }

      return expiredFiles;
    } catch (error) {
      console.error('Error listing expired temporary files:', error);
      return [];
    }
  }

  /**
   * Remove um arquivo temporário específico
   */
  async deleteTemporaryFile(filePath: string): Promise<boolean> {
    try {
      const fileRef = ref(storage, filePath);
      await deleteObject(fileRef);
      console.log(`Temporary file deleted: ${filePath}`);
      return true;
    } catch (error) {
      console.error(`Error deleting temporary file ${filePath}:`, error);
      return false;
    }
  }

  /**
   * Remove múltiplos arquivos temporários
   */
  async deleteMultipleTemporaryFiles(filePaths: string[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const filePath of filePaths) {
      const deleted = await this.deleteTemporaryFile(filePath);
      if (deleted) {
        success++;
      } else {
        failed++;
      }
    }

    return { success, failed };
  }

  /**
   * Limpa todos os arquivos temporários expirados para um usuário
   */
  async cleanupExpiredFiles(userId: string): Promise<{ cleaned: number; failed: number }> {
    const expiredFiles = await this.listExpiredTemporaryFiles(userId);
    
    if (expiredFiles.length === 0) {
      return { cleaned: 0, failed: 0 };
    }

    console.log(`Found ${expiredFiles.length} expired temporary files for user ${userId}`);
    const result = await this.deleteMultipleTemporaryFiles(expiredFiles);
    
    console.log(`Cleanup completed for user ${userId}: ${result.success} cleaned, ${result.failed} failed`);
    return { cleaned: result.success, failed: result.failed };
  }

  /**
   * Valida se um arquivo pode ser anexado
   */
  canAttachFile(file: File): boolean {
    return this.validateFile(file).valid;
  }

  /**
   * Obtém informações sobre limites de arquivo
   */
  getFileLimits() {
    return {
      maxImageSize: this.MAX_IMAGE_SIZE,
      maxPdfSize: this.MAX_PDF_SIZE,
      supportedImageTypes: this.SUPPORTED_IMAGE_TYPES,
      supportedPdfTypes: this.SUPPORTED_PDF_TYPES,
      expirationTime: this.EXPIRATION_TIME
    };
  }

  /**
   * Converte anexos temporários em anexos permanentes
   * Move arquivos da pasta temporários para a pasta do chat permanente
   */
  async convertTemporaryToPermanent(
    userId: string,
    chatId: string,
    temporaryAttachments: TemporaryAttachmentMetadata[],
    chatName?: string
  ): Promise<{ success: boolean; attachments: AttachmentMetadata[]; errors: string[] }> {
    const convertedAttachments: AttachmentMetadata[] = [];
    const errors: string[] = [];

    for (const tempAttachment of temporaryAttachments) {
      try {
        // Gerar novo ID para o anexo permanente
        const permanentId = `attachment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const fileExtension = tempAttachment.filename.split('.').pop() || '';
        const permanentFileName = `${permanentId}.${fileExtension}`;

        // Caminhos de origem e destino
        const tempPath = `usuarios/${userId}/temporarios/${tempAttachment.id}.${fileExtension}`;
        const permanentPath = `usuarios/${userId}/conversas/${chatId}/anexos/${permanentFileName}`;

        // Referências do Storage
        const tempRef = ref(storage, tempPath);
        const permanentRef = ref(storage, permanentPath);

        try {
          // Obter dados do arquivo temporário
          const { getBytes } = await import('firebase/storage');
          const fileData = await getBytes(tempRef);

          // Criar metadados para o arquivo permanente
          const permanentMetadata = {
            customMetadata: {
              originalName: tempAttachment.filename,
              ...(chatName && { chatName }),
              convertedFrom: 'temporary',
              originalTempId: tempAttachment.id
            }
          };

          // Upload para localização permanente
          await uploadBytes(permanentRef, fileData, permanentMetadata);

          // Obter nova URL de download
          const permanentURL = await getDownloadURL(permanentRef);

          // Criar metadata do anexo permanente
          const permanentAttachment: AttachmentMetadata = {
            id: permanentId,
            type: tempAttachment.type,
            filename: tempAttachment.filename,
            url: permanentURL,
            size: tempAttachment.size,
            uploadedAt: Date.now(),
            ...(tempAttachment.base64Data && { base64Data: tempAttachment.base64Data }),
            ...(chatName && { chatName })
          };

          convertedAttachments.push(permanentAttachment);

          // Remover arquivo temporário após conversão bem-sucedida
          try {
            await deleteObject(tempRef);
            console.log(`Temporary file deleted after conversion: ${tempPath}`);
          } catch (deleteError) {
            console.warn(`Could not delete temporary file ${tempPath}:`, deleteError);
            // Não falhar a conversão se não conseguir deletar o arquivo temporário
          }

          console.log(`Temporary attachment converted to permanent: ${tempAttachment.id} -> ${permanentId}`);

        } catch (storageError) {
          console.error(`Error converting temporary attachment ${tempAttachment.id}:`, storageError);
          errors.push(`${tempAttachment.filename}: Erro ao converter arquivo`);
        }

      } catch (error) {
        console.error(`Error processing temporary attachment ${tempAttachment.id}:`, error);
        errors.push(`${tempAttachment.filename}: Erro no processamento`);
      }
    }

    return {
      success: convertedAttachments.length > 0,
      attachments: convertedAttachments,
      errors
    };
  }
}

// Exportar instância singleton
export const temporaryAttachmentService = new TemporaryAttachmentService();
