import { NextRequest, NextResponse } from 'next/server';
import serverFavoritesService from '@/lib/serverFavoritesService';

// GET - Obter favoritos do usuário
export async function GET(request: NextRequest) {
  try {
    const userId = request.nextUrl.searchParams.get('userId');
    const chatId = request.nextUrl.searchParams.get('chatId');
    const role = request.nextUrl.searchParams.get('role') as 'user' | 'assistant' | null;
    const startDate = request.nextUrl.searchParams.get('startDate');
    const endDate = request.nextUrl.searchParams.get('endDate');

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    let favorites;

    // Filtrar por chat específico
    if (chatId) {
      favorites = await serverFavoritesService.getFavoritesByChat(userId, chatId);
    }
    // Filtrar por role
    else if (role) {
      favorites = await serverFavoritesService.getFavoritesByRole(userId, role);
    }
    // Filtrar por período
    else if (startDate && endDate) {
      favorites = await serverFavoritesService.getFavoritesByDateRange(
        userId,
        parseInt(startDate),
        parseInt(endDate)
      );
    }
    // Obter todos os favoritos
    else {
      favorites = await serverFavoritesService.getFavorites(userId);
    }

    return NextResponse.json({
      success: true,
      favorites
    });

  } catch (error) {
    console.error('Erro ao obter favoritos:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST - Adicionar mensagem aos favoritos
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, messageId, chatId, chatName, message } = body;

    if (!userId || !messageId || !chatId || !chatName || !message) {
      return NextResponse.json(
        { error: 'Todos os campos são obrigatórios: userId, messageId, chatId, chatName, message' },
        { status: 400 }
      );
    }

    await serverFavoritesService.addToFavorites(userId, messageId, chatId, chatName, message);

    return NextResponse.json({
      success: true,
      message: 'Mensagem adicionada aos favoritos'
    });

  } catch (error) {
    console.error('Erro ao adicionar favorito:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Remover mensagem dos favoritos
export async function DELETE(request: NextRequest) {
  try {
    const userId = request.nextUrl.searchParams.get('userId');
    const messageId = request.nextUrl.searchParams.get('messageId');
    const chatId = request.nextUrl.searchParams.get('chatId');

    if (!userId || !messageId || !chatId) {
      return NextResponse.json(
        { error: 'userId, messageId e chatId são obrigatórios' },
        { status: 400 }
      );
    }

    await serverFavoritesService.removeFromFavorites(userId, messageId, chatId);

    return NextResponse.json({
      success: true,
      message: 'Mensagem removida dos favoritos'
    });

  } catch (error) {
    console.error('Erro ao remover favorito:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
