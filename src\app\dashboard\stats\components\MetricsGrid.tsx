'use client';

import { GeneralStatistics } from '@/lib/generalStatisticsService';

interface MetricsGridProps {
  statistics: GeneralStatistics;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  gradient: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

function MetricCard({ title, value, subtitle, icon, gradient, trend }: MetricCardProps) {
  return (
    <div className="group relative">
      {/* Glow effect */}
      <div className={`absolute -inset-0.5 bg-gradient-to-r ${gradient} rounded-2xl blur opacity-20 group-hover:opacity-40 transition duration-300`}></div>
      
      {/* Card content */}
      <div className="relative bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 hover:scale-105">
        <div className="flex items-start justify-between">
          <div className="space-y-2 flex-1">
            <div className="flex items-center space-x-3">
              <div className={`p-2 bg-gradient-to-r ${gradient} rounded-xl shadow-lg`}>
                {icon}
              </div>
              <div>
                <p className="text-sm font-medium text-white/70 uppercase tracking-wide">{title}</p>
                <p className="text-2xl lg:text-3xl font-bold text-white">{value}</p>
              </div>
            </div>
            
            {subtitle && (
              <p className="text-sm text-white/60">{subtitle}</p>
            )}
          </div>

          {trend && (
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-lg ${trend.isPositive ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'}`}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={trend.isPositive ? "M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" : "M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"} />
              </svg>
              <span className="text-xs font-medium">{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function MetricsGrid({ statistics }: MetricsGridProps) {
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}min`;
  };

  const metrics = [
    {
      title: 'Total de Chats',
      value: statistics.totalChats,
      icon: <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" /></svg>,
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'Mensagens',
      value: formatNumber(statistics.totalMessages),
      subtitle: `${formatNumber(statistics.totalUserMessages)} suas • ${formatNumber(statistics.totalAssistantMessages)} IA`,
      icon: <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" /></svg>,
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      title: 'Palavras',
      value: formatNumber(statistics.totalWords),
      subtitle: `Média: ${statistics.averageMessageLength.toFixed(0)} caracteres`,
      icon: <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>,
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      title: 'Tokens',
      value: formatNumber(statistics.totalTokens),
      subtitle: `${formatNumber(statistics.totalPromptTokens)} entrada • ${formatNumber(statistics.totalCompletionTokens)} saída`,
      icon: <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" /></svg>,
      gradient: 'from-orange-500 to-red-500'
    },
    {
      title: 'Custo Total',
      value: `$${statistics.totalCost.toFixed(2)}`,
      subtitle: `Média: $${(statistics.totalCost / Math.max(statistics.totalChats, 1)).toFixed(3)} por chat`,
      icon: <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" /></svg>,
      gradient: 'from-yellow-500 to-orange-500'
    },
    {
      title: 'Tempo de Resposta',
      value: formatTime(statistics.averageResponseTime),
      subtitle: `Baseado em ${formatNumber(statistics.totalAssistantMessages)} respostas`,
      icon: <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>,
      gradient: 'from-indigo-500 to-purple-500'
    },
    {
      title: 'Palavras do Usuário',
      value: formatNumber(statistics.totalUserWords),
      subtitle: `Média: ${(statistics.totalUserWords / Math.max(statistics.totalUserMessages, 1)).toFixed(1)} por mensagem`,
      icon: <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>,
      gradient: 'from-emerald-500 to-teal-500'
    },
    {
      title: 'Palavras da IA',
      value: formatNumber(statistics.totalAssistantWords),
      subtitle: `Média: ${(statistics.totalAssistantWords / Math.max(statistics.totalAssistantMessages, 1)).toFixed(1)} por resposta`,
      icon: <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>,
      gradient: 'from-violet-500 to-purple-500'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {metrics.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          subtitle={metric.subtitle}
          icon={metric.icon}
          gradient={metric.gradient}
        />
      ))}
    </div>
  );
}
