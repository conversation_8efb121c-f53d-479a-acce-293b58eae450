import { useState, useEffect, useRef } from 'react';
import { sessionTimeService } from '@/lib/sessionTimeService';

interface UseSessionTimeProps {
  userId: string | null;
  chatId: string | null;
}

export const useSessionTime = ({ userId, chatId }: UseSessionTimeProps) => {
  const [sessionTime, setSessionTime] = useState('0:00');
  const [isActive, setIsActive] = useState(false);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const previousChatIdRef = useRef<string | null>(null);

  // Função para atualizar o tempo em tempo real
  const updateDisplayTime = () => {
    if (chatId) {
      const currentTime = sessionTimeService.getCurrentSessionTime(chatId);
      const formattedTime = sessionTimeService.formatTime(currentTime);
      setSessionTime(formattedTime);
    }
  };

  // Efeito para gerenciar mudanças de chat
  useEffect(() => {
    const handleChatChange = async () => {
      // Parar sessão anterior se existir
      if (previousChatIdRef.current && previousChatIdRef.current !== chatId && userId) {
        await sessionTimeService.stopSession(previousChatIdRef.current, userId);
        setIsActive(false);
      }

      // Iniciar nova sessão se há userId e chatId
      if (userId && chatId) {
        await sessionTimeService.startSession(userId, chatId);
        setIsActive(true);
        updateDisplayTime(); // Atualizar imediatamente
      } else {
        setIsActive(false);
        setSessionTime('0:00');
      }

      // Atualizar referência do chat anterior
      previousChatIdRef.current = chatId;
    };

    handleChatChange();

    // Cleanup quando o componente é desmontado
    return () => {
      if (chatId && userId) {
        sessionTimeService.stopSession(chatId, userId);
      }
    };
  }, [userId, chatId]);

  // Efeito para atualizar o display em tempo real
  useEffect(() => {
    if (isActive && chatId) {
      // Atualizar a cada segundo para o display
      intervalRef.current = setInterval(updateDisplayTime, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isActive, chatId]);

  // Cleanup geral quando o componente é desmontado
  useEffect(() => {
    return () => {
      sessionTimeService.cleanup();
    };
  }, []);

  return {
    sessionTime,
    isActive
  };
};
