'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useConversations } from '@/contexts/ConversationContext';
import { createConversation, updateConversation, getUserConversations } from '@/lib/conversationService';
import { Eye, EyeOff, Lock } from 'lucide-react';

interface NewConversationModalProps {
  isOpen: boolean;
  onClose: () => void;
  editMode?: boolean;
  conversationId?: string;
}

type TabType = 'geral' | 'avancado';

const NewConversationModal = ({ isOpen, onClose, editMode = false, conversationId }: NewConversationModalProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('geral');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Campos gerais
  const [conversationName, setConversationName] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('');
  const [context, setContext] = useState('');

  // Campos avançados - todos começam no meio/padrão
  const [temperature, setTemperature] = useState(1.0);
  const [repetitionPenalty, setRepetitionPenalty] = useState(1.0);
  const [frequencyPenalty, setFrequencyPenalty] = useState(1.0); // Mudado para 1.0 (meio)
  const [maxTokens, setMaxTokens] = useState(2048);

  // Novo campo para LaTeX
  const [latexInstructions, setLatexInstructions] = useState(false);

  // Campo para senha do chat
  const [chatPassword, setChatPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const { user } = useAuth();
  const { conversations, refreshConversations, addConversation } = useConversations();
  
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      if (editMode && conversationId) {
        loadConversationData();
      } else {
        resetForm();
      }
    } else {
      setTimeout(() => {
        setIsVisible(false);
      }, 300);
    }
  }, [isOpen, editMode, conversationId]);

  const loadConversationData = () => {
    if (!editMode || !conversationId) return;

    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
      setConversationName(conversation.name || '');
      setSystemPrompt(conversation.systemPrompt || '');
      setContext(conversation.context || '');
      setTemperature(conversation.temperature || 1.0);
      setRepetitionPenalty(conversation.repetitionPenalty || 1.0);
      setFrequencyPenalty(conversation.frequencyPenalty || 1.0);
      setMaxTokens(conversation.maxTokens || 2048);
      setLatexInstructions(conversation.latexInstructions || false);
      setChatPassword(conversation.password || '');
      setActiveTab('geral');
      setError('');
    }
  };
  
  const resetForm = () => {
    setConversationName('');
    setSystemPrompt('');
    setContext('');
    setTemperature(1.0); // Padrão no meio (balanceado)
    setRepetitionPenalty(1.0); // Padrão no meio
    setFrequencyPenalty(1.0); // Padrão no meio (neutro)
    setMaxTokens(2048);
    setLatexInstructions(false);
    setChatPassword('');
    setShowPassword(false);
    setActiveTab('geral');
    setError('');
  };
  
  const handleSubmit = async () => {
    if (!user) {
      setError('Você precisa estar logado para ' + (editMode ? 'editar' : 'criar') + ' uma conversa');
      return;
    }

    // Validar se o nome foi preenchido
    if (!conversationName.trim()) {
      setError('O nome da conversa é obrigatório');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      if (editMode && conversationId) {
        // Modo de edição
        const name = conversationName.trim();
        const trimmedPassword = chatPassword.trim();

        const updateData: any = {
          name,
          systemPrompt,
          context,
          temperature,
          repetitionPenalty,
          frequencyPenalty,
          maxTokens,
          latexInstructions
        };

        // Só incluir password se não estiver vazio
        if (trimmedPassword) {
          updateData.password = trimmedPassword;
        }

        await updateConversation(user.uid, conversationId, updateData);
      } else {
        // Modo de criação
        const name = conversationName.trim();
        const trimmedPassword = chatPassword.trim();

        const createData: any = {
          systemPrompt,
          context,
          temperature,
          repetitionPenalty,
          frequencyPenalty,
          maxTokens,
          latexInstructions
        };

        // Só incluir password se não estiver vazio
        if (trimmedPassword) {
          createData.password = trimmedPassword;
        }

        const newChatId = await createConversation(user.uid, name, createData);

        // Buscar a conversa recém-criada e adicioná-la ao contexto
        // Isso automaticamente a selecionará
        const userConversations = await getUserConversations(user.uid, false);
        const newConversation = userConversations.find(conv => conv.id === newChatId);

        if (newConversation) {
          addConversation(newConversation);
        } else {
          // Fallback: atualizar lista completa se não encontrar a conversa
          await refreshConversations();
        }
      }

      // Para modo de edição, ainda precisamos atualizar a lista
      if (editMode) {
        await refreshConversations();
      }

      onClose();
    } catch (err) {
      console.error('Erro ao ' + (editMode ? 'editar' : 'criar') + ' conversa:', err);
      setError('Ocorreu um erro ao ' + (editMode ? 'editar' : 'criar') + ' a conversa. Tente novamente.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleMaxTokensChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    if (value === '') {
      setMaxTokens(0);
    } else {
      setMaxTokens(parseInt(value));
    }
  };
  
  if (!isOpen && !isVisible) return null;

  return (
    <div
      className={`fixed inset-0 bg-black/80 backdrop-blur-xl flex items-center justify-center z-50 transition-all duration-500 p-4 ${
        isOpen ? 'opacity-100' : 'opacity-0'
      }`}
    >
      {/* Background overlay with animated gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-transparent to-cyan-900/20 animate-pulse"></div>

      <div
        className={`relative w-full max-w-4xl max-h-[95vh] overflow-hidden transform transition-all duration-500 ${
          isOpen ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-95 translate-y-8'
        }`}
      >
        {/* Main modal container */}
        <div className="bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-2xl border border-blue-600/30 rounded-2xl shadow-2xl shadow-blue-900/50">
          {/* Decorative glow effect */}
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/10 via-transparent to-cyan-500/10 pointer-events-none"></div>
          {/* Header */}
          <div className="relative flex justify-between items-center p-6 border-b border-blue-700/20">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center shadow-lg shadow-blue-700/30">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  {editMode ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  )}
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white text-glow-sm">
                  {editMode ? 'Editar Conversa' : 'Nova Conversa'}
                </h2>
                <p className="text-sm text-blue-300/70 mt-1">
                  {editMode ? 'Modifique as configurações da sua conversa' : 'Configure sua nova conversa com IA'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="group relative p-3 rounded-xl bg-blue-900/30 hover:bg-red-500/20 backdrop-blur-sm border border-blue-600/20 hover:border-red-500/40 text-blue-300 hover:text-red-300 transition-all duration-300 hover:scale-105"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 transform group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        
          {/* Tabs */}
          <div className="relative px-6 pt-4">
            <div className="flex space-x-1 bg-blue-900/20 backdrop-blur-sm rounded-xl p-1 border border-blue-700/20">
              <button
                className={`relative flex-1 py-3 px-4 text-sm font-medium rounded-lg transition-all duration-300 ${
                  activeTab === 'geral'
                    ? 'text-white bg-gradient-to-r from-blue-600 to-cyan-600 shadow-lg shadow-blue-600/20'
                    : 'text-blue-300 hover:text-white hover:bg-blue-800/30'
                }`}
                onClick={() => setActiveTab('geral')}
              >
                <svg className="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                </svg>
                Geral
              </button>
              <button
                className={`relative flex-1 py-3 px-4 text-sm font-medium rounded-lg transition-all duration-300 ${
                  activeTab === 'avancado'
                    ? 'text-white bg-gradient-to-r from-blue-600 to-cyan-600 shadow-lg shadow-blue-600/20'
                    : 'text-blue-300 hover:text-white hover:bg-blue-800/30'
                }`}
                onClick={() => setActiveTab('avancado')}
              >
                <svg className="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
                Avançado
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="relative p-6 max-h-[60vh] overflow-y-auto">
            {/* Tab content - Geral */}
          {activeTab === 'geral' && (
            <div className="space-y-8">
              {/* Nome da conversa */}
              <div className="group">
                <label className="block text-sm font-medium text-blue-300 mb-3 flex items-center gap-2">
                  <div className="w-5 h-5 rounded-full bg-gradient-to-br from-blue-500/20 to-cyan-500/20 flex items-center justify-center">
                    <svg className="w-3 h-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </div>
                  Nome do chat
                  <span className="text-red-400 text-sm">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={conversationName}
                    onChange={(e) => setConversationName(e.target.value)}
                    placeholder="Ex: Projeto de física"
                    className="w-full bg-blue-900/40 border border-blue-700/50 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 focus:bg-blue-800/50"
                    required
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <p className="text-xs text-blue-400/70 mt-2 flex items-center gap-1">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Nome obrigatório para identificar a conversa
                </p>
              </div>

              {/* System Prompt */}
              <div className="group">
                <label className="block text-sm font-medium text-blue-300 mb-3 flex items-center gap-2">
                  <div className="w-5 h-5 rounded-full bg-gradient-to-br from-blue-500/20 to-cyan-500/20 flex items-center justify-center">
                    <svg className="w-3 h-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  System Prompt (Opcional)
                </label>
                <div className="relative">
                  <textarea
                    value={systemPrompt}
                    onChange={(e) => setSystemPrompt(e.target.value)}
                    placeholder="Instruções para o comportamento da IA"
                    rows={3}
                    className="w-full bg-blue-900/40 border border-blue-700/50 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 focus:bg-blue-800/50 resize-none"
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <p className="text-xs text-blue-400/70 mt-2 flex items-center gap-1">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Define como a IA deve se comportar e responder (ex: "Seja um assistente especializado em matemática")
                </p>
              </div>

              {/* Contexto */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2 flex items-center gap-2">
                  <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  Contexto (Opcional)
                </label>
                <textarea
                  value={context}
                  onChange={(e) => setContext(e.target.value)}
                  placeholder="Informações adicionais de contexto para a conversa"
                  rows={4}
                  className="w-full bg-blue-900/40 border border-blue-700/50 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 focus:bg-blue-800/50 resize-none"
                />
                <p className="text-xs text-blue-400/70 mt-1">
                  Informações de fundo que a IA deve considerar durante toda a conversa
                </p>
              </div>

              {/* Senha do Chat */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2 flex items-center gap-2">
                  <Lock className="w-4 h-4 text-blue-400" />
                  Senha do Chat (Opcional)
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={chatPassword}
                    onChange={(e) => setChatPassword(e.target.value)}
                    placeholder="Deixe vazio para chat sem proteção"
                    className="w-full bg-blue-900/40 border border-blue-700/50 rounded-xl px-4 py-3 pr-12 text-blue-100 placeholder:text-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 focus:bg-blue-800/50"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    {showPassword ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </button>
                </div>
                <p className="text-xs text-blue-400/70 mt-1">
                  Se definida, será necessário inserir a senha para acessar este chat
                </p>
              </div>

              {/* Instruções LaTeX */}
              <div>
                <label className="flex items-center justify-between p-4 bg-blue-900/20 border border-blue-700/30 rounded-lg cursor-pointer hover:bg-blue-900/30 transition-colors">
                  <div className="flex items-center gap-3">
                    <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    <div>
                      <span className="text-sm font-medium text-blue-300">Instruções LaTeX</span>
                      <p className="text-xs text-blue-400/70 mt-1">
                        Habilita formatação matemática avançada usando LaTeX
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={latexInstructions}
                      onChange={(e) => setLatexInstructions(e.target.checked)}
                      className="sr-only"
                    />
                    <div className={`w-11 h-6 rounded-full transition-colors ${
                      latexInstructions ? 'bg-blue-600' : 'bg-blue-900/50'
                    }`}>
                      <div className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform ${
                        latexInstructions ? 'translate-x-5' : 'translate-x-0.5'
                      } mt-0.5`}></div>
                    </div>
                  </div>
                </label>
              </div>
            </div>
          )}
          
          {/* Tab content - Avançado */}
          {activeTab === 'avancado' && (
            <div className="space-y-8">
              {/* Temperatura */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                    <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                      <path fillRule="evenodd" d="M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z" clipRule="evenodd" />
                    </svg>
                    Temperatura
                  </label>
                  <span className="bg-blue-900/50 px-3 py-1 rounded-full text-xs font-mono text-blue-200">{temperature.toFixed(1)}</span>
                </div>
                <div className="px-2">
                  <div className="relative">
                    <div className="flex justify-between text-xs text-blue-400/70 mb-2">
                      <span>Preciso</span>
                      <span className="absolute left-1/2 transform -translate-x-1/2 text-blue-300 font-medium">Balanceado</span>
                      <span>Criativo</span>
                    </div>
                    <div className="relative h-1 bg-blue-900/20 rounded-full">
                      <input
                        type="range"
                        min="0"
                        max="2"
                        step="0.1"
                        value={temperature}
                        onChange={(e) => setTemperature(parseFloat(e.target.value))}
                        className="absolute w-full h-full opacity-0 cursor-pointer z-20"
                      />
                      {/* Marcador do padrão */}
                      <div className="absolute w-0.5 h-3 bg-blue-400/50 rounded-full transform -translate-y-1 top-1/2" style={{left: '50%', marginLeft: '-1px'}}></div>
                      {/* Bolinha do controle com glow */}
                      <div className="absolute w-4 h-4 bg-blue-500 rounded-full shadow-lg transform -translate-y-1/2 top-1/2 transition-all duration-200"
                           style={{
                             left: `calc(${(temperature / 2) * 100}% - 8px)`,
                             boxShadow: '0 0 12px rgba(59, 130, 246, 0.6), 0 0 24px rgba(59, 130, 246, 0.3)'
                           }}></div>
                    </div>
                  </div>
                </div>
                <p className="text-xs text-blue-400/70 px-2">
                  Controla a criatividade das respostas. Valores baixos (0.1-0.7) são mais precisos, valores altos (1.3-2.0) são mais criativos.
                </p>
              </div>
              
              {/* Frequency Penalty */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                    <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Frequency Penalty
                  </label>
                  <span className="bg-blue-900/50 px-3 py-1 rounded-full text-xs font-mono text-blue-200">{frequencyPenalty.toFixed(1)}</span>
                </div>
                <div className="px-2">
                  <div className="relative">
                    <div className="flex justify-between text-xs text-blue-400/70 mb-2">
                      <span>Negativo</span>
                      <span className="absolute left-1/2 transform -translate-x-1/2 text-blue-300 font-medium">Neutro</span>
                      <span>Positivo</span>
                    </div>
                    <div className="relative h-1 bg-blue-900/20 rounded-full">
                      <input
                        type="range"
                        min="0"
                        max="2"
                        step="0.1"
                        value={frequencyPenalty}
                        onChange={(e) => setFrequencyPenalty(parseFloat(e.target.value))}
                        className="absolute w-full h-full opacity-0 cursor-pointer z-20"
                      />
                      {/* Marcador do padrão */}
                      <div className="absolute w-0.5 h-3 bg-blue-400/50 rounded-full transform -translate-y-1 top-1/2" style={{left: '50%', marginLeft: '-1px'}}></div>
                      {/* Bolinha do controle com glow */}
                      <div className="absolute w-4 h-4 bg-blue-500 rounded-full shadow-lg transform -translate-y-1/2 top-1/2 transition-all duration-200"
                           style={{
                             left: `calc(${(frequencyPenalty / 2) * 100}% - 8px)`,
                             boxShadow: '0 0 12px rgba(59, 130, 246, 0.6), 0 0 24px rgba(59, 130, 246, 0.3)'
                           }}></div>
                    </div>
                  </div>
                </div>
                <p className="text-xs text-blue-400/70 px-2">
                  Reduz repetição de palavras baseado na frequência. Valores altos (1.0-2.0) evitam mais repetições.
                </p>
              </div>

              {/* Repetition Penalty */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                    <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    Repetition Penalty
                  </label>
                  <span className="bg-blue-900/50 px-3 py-1 rounded-full text-xs font-mono text-blue-200">{repetitionPenalty.toFixed(1)}</span>
                </div>
                <div className="px-2">
                  <div className="relative">
                    <div className="flex justify-between text-xs text-blue-400/70 mb-2">
                      <span>Mínimo</span>
                      <span className="absolute left-1/2 transform -translate-x-1/2 text-blue-300 font-medium">Padrão</span>
                      <span>Máximo</span>
                    </div>
                    <div className="relative h-1 bg-blue-900/20 rounded-full">
                      <input
                        type="range"
                        min="0"
                        max="2"
                        step="0.1"
                        value={repetitionPenalty}
                        onChange={(e) => setRepetitionPenalty(parseFloat(e.target.value))}
                        className="absolute w-full h-full opacity-0 cursor-pointer z-20"
                      />
                      {/* Marcador do padrão */}
                      <div className="absolute w-0.5 h-3 bg-blue-400/50 rounded-full transform -translate-y-1 top-1/2" style={{left: '50%', marginLeft: '-1px'}}></div>
                      {/* Bolinha do controle com glow */}
                      <div className="absolute w-4 h-4 bg-blue-500 rounded-full shadow-lg transform -translate-y-1/2 top-1/2 transition-all duration-200"
                           style={{
                             left: `calc(${(repetitionPenalty / 2) * 100}% - 8px)`,
                             boxShadow: '0 0 12px rgba(59, 130, 246, 0.6), 0 0 24px rgba(59, 130, 246, 0.3)'
                           }}></div>
                    </div>
                  </div>
                </div>
                <p className="text-xs text-blue-400/70 px-2">
                  Penaliza tokens já utilizados para evitar repetições. Valores altos (1.2-2.0) reduzem mais repetições.
                </p>
              </div>

              {/* Limite de Tokens */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-blue-300 flex items-center gap-2">
                  <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Limite de Tokens
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={maxTokens}
                    onChange={handleMaxTokensChange}
                    className="w-full bg-blue-900/40 border border-blue-700/50 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-400/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 focus:bg-blue-800/50"
                    placeholder="2048"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <span className="text-xs text-blue-400/70 bg-blue-900/50 px-2 py-1 rounded">tokens</span>
                  </div>
                </div>
                <p className="text-xs text-blue-400/70">
                  Máximo de tokens (palavras/partes de palavras) que a IA pode gerar por resposta. Valores típicos: 512-4096.
                </p>
              </div>
            </div>
          )}
          
            {/* Error Message */}
            {error && (
              <div className="mt-6 p-4 bg-gradient-to-r from-red-900/20 to-red-800/20 border border-red-700/30 rounded-xl backdrop-blur-sm">
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center flex-shrink-0">
                    <svg className="w-3 h-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-sm text-red-400">{error}</p>
                </div>
              </div>
            )}
          </div>

          {/* Footer with buttons */}
          <div className="relative px-6 py-4 border-t border-blue-700/20 bg-gradient-to-r from-blue-950/50 to-blue-900/50 backdrop-blur-sm">
            <div className="flex justify-end gap-3">
              <button
                onClick={onClose}
                className="px-6 py-3 rounded-xl text-blue-300 hover:text-white hover:bg-blue-800/30 transition-all duration-300 font-medium border border-blue-700/20 hover:border-blue-600/40 backdrop-blur-sm"
                disabled={isSubmitting}
              >
                Cancelar
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting || !conversationName.trim()}
                className={`px-8 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 rounded-xl text-white font-semibold shadow-lg shadow-blue-700/30 hover:shadow-blue-700/40 transition-all duration-300 hover:scale-105 flex items-center gap-2 ${
                  isSubmitting || !conversationName.trim() ? 'opacity-70 cursor-not-allowed hover:scale-100' : ''
                }`}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-t-white border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
                    <span>{editMode ? 'Salvando...' : 'Criando...'}</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      {editMode ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      )}
                    </svg>
                    <span>{editMode ? 'Salvar Alterações' : 'Criar Conversa'}</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewConversationModal; 