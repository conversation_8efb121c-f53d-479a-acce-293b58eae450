import { db } from './firebase';
import {
  collection,
  doc,
  addDoc,
  getDocs,
  query,
  orderBy,
  serverTimestamp,
  updateDoc,
  deleteDoc,
  writeBatch,
  where
} from 'firebase/firestore';
import { ChatFolder, ChatFolderInput } from './types/chatFolder';

// Cache para evitar requisições desnecessárias
interface FolderCache {
  data: ChatFolder[];
  timestamp: number;
  userId: string;
}

let folderCache: FolderCache | null = null;
const CACHE_DURATION = 30000; // 30 segundos

/**
 * Invalida o cache de pastas
 */
export const invalidateFolderCache = (): void => {
  folderCache = null;
};

/**
 * Cria uma nova pasta de chats
 */
export const createChatFolder = async (
  userId: string,
  folderData: ChatFolderInput
): Promise<string> => {
  try {
    // Obter o próximo número de ordem
    const folders = await getUserChatFolders(userId, false);
    const maxOrder = folders.length > 0 ? Math.max(...folders.map(f => f.order)) : 0;

    const folderDoc = {
      userId,
      name: folderData.name,
      description: folderData.description || '',
      color: folderData.color,
      isExpanded: folderData.isExpanded !== undefined ? folderData.isExpanded : true,
      order: folderData.order !== undefined ? folderData.order : maxOrder + 1,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    // Criar documento no Firestore
    const userFoldersRef = collection(db, 'usuarios', userId, 'pastas_chat');
    const docRef = await addDoc(userFoldersRef, folderDoc);

    // Invalidar cache
    invalidateFolderCache();

    return docRef.id;
  } catch (error) {
    console.error('Error creating chat folder:', error);
    throw error;
  }
};

/**
 * Obtém todas as pastas de chat de um usuário
 */
export const getUserChatFolders = async (
  userId: string,
  useCache = true
): Promise<ChatFolder[]> => {
  try {
    // Verificar cache se solicitado
    if (useCache && folderCache &&
        folderCache.userId === userId &&
        (Date.now() - folderCache.timestamp) < CACHE_DURATION) {
      console.log('Using cached folders');
      return folderCache.data;
    }

    const userFoldersRef = collection(db, 'usuarios', userId, 'pastas_chat');
    const q = query(userFoldersRef, orderBy('order', 'asc'));
    const querySnapshot = await getDocs(q);

    const folders = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId,
        name: data.name || 'Pasta sem nome',
        description: data.description || '',
        color: data.color || '#3B82F6',
        isExpanded: data.isExpanded !== undefined ? data.isExpanded : true,
        order: data.order || 0,
        createdAt: data.createdAt || Date.now(),
        updatedAt: data.updatedAt || Date.now()
      } as ChatFolder;
    });

    // Atualizar cache
    folderCache = {
      data: folders,
      timestamp: Date.now(),
      userId
    };

    return folders;
  } catch (error) {
    console.error('Error getting user chat folders:', error);
    throw error;
  }
};

/**
 * Atualiza uma pasta de chat
 */
export const updateChatFolder = async (
  userId: string,
  folderId: string,
  updates: Partial<ChatFolder>
): Promise<void> => {
  try {
    const folderRef = doc(db, 'usuarios', userId, 'pastas_chat', folderId);
    
    const updateData: any = {
      ...updates,
      updatedAt: Date.now()
    };

    // Remover campos que não devem ser atualizados
    delete updateData.id;
    delete updateData.userId;
    delete updateData.createdAt;

    await updateDoc(folderRef, updateData);

    // Invalidar cache
    invalidateFolderCache();
  } catch (error) {
    console.error('Error updating chat folder:', error);
    throw error;
  }
};

/**
 * Deleta uma pasta de chat e move todos os chats para "sem pasta"
 */
export const deleteChatFolder = async (
  userId: string,
  folderId: string
): Promise<void> => {
  try {
    const batch = writeBatch(db);

    // Deletar a pasta
    const folderRef = doc(db, 'usuarios', userId, 'pastas_chat', folderId);
    batch.delete(folderRef);

    // Mover todos os chats desta pasta para "sem pasta" (folderId = null)
    const conversationsRef = collection(db, 'usuarios', userId, 'conversas');
    const conversationsQuery = query(conversationsRef, where('folderId', '==', folderId));
    const conversationsSnapshot = await getDocs(conversationsQuery);

    conversationsSnapshot.docs.forEach(conversationDoc => {
      const conversationRef = doc(db, 'usuarios', userId, 'conversas', conversationDoc.id);
      batch.update(conversationRef, { folderId: null });
    });

    await batch.commit();

    // Invalidar cache
    invalidateFolderCache();
  } catch (error) {
    console.error('Error deleting chat folder:', error);
    throw error;
  }
};

/**
 * Reordena as pastas
 */
export const reorderChatFolders = async (
  userId: string,
  folderIds: string[]
): Promise<void> => {
  try {
    const batch = writeBatch(db);

    folderIds.forEach((folderId, index) => {
      const folderRef = doc(db, 'usuarios', userId, 'pastas_chat', folderId);
      batch.update(folderRef, { 
        order: index + 1,
        updatedAt: Date.now()
      });
    });

    await batch.commit();

    // Invalidar cache
    invalidateFolderCache();
  } catch (error) {
    console.error('Error reordering chat folders:', error);
    throw error;
  }
};

/**
 * Move uma conversa para uma pasta específica
 */
export const moveConversationToFolder = async (
  userId: string,
  conversationId: string,
  folderId: string | null
): Promise<void> => {
  try {
    const conversationRef = doc(db, 'usuarios', userId, 'conversas', conversationId);
    await updateDoc(conversationRef, { 
      folderId: folderId,
      updatedAt: Date.now()
    });

    console.log(`Conversation ${conversationId} moved to folder ${folderId || 'none'}`);
  } catch (error) {
    console.error('Error moving conversation to folder:', error);
    throw error;
  }
};

/**
 * Obtém estatísticas de uma pasta (número de chats)
 */
export const getFolderStats = async (
  userId: string,
  folderId: string
): Promise<{ chatCount: number }> => {
  try {
    const conversationsRef = collection(db, 'usuarios', userId, 'conversas');
    const q = query(conversationsRef, where('folderId', '==', folderId));
    const querySnapshot = await getDocs(q);

    return {
      chatCount: querySnapshot.size
    };
  } catch (error) {
    console.error('Error getting folder stats:', error);
    throw error;
  }
};
