import { AIModel, ModelCategory, ModelSortBy } from './types/chat';

export interface OpenRouterModel {
  id: string;
  name: string;
  description?: string;
  context_length: number;
  pricing: {
    prompt: string;
    completion: string;
    image?: string;
  };
  architecture?: {
    input_modalities: string[];
    output_modalities: string[];
    tokenizer: string;
  };
  created?: number;
}

export interface OpenRouterResponse {
  data: OpenRouterModel[];
}

class OpenRouterService {
  private baseUrl = 'https://openrouter.ai/api/v1';
  private cache: AIModel[] | null = null;
  private cacheTimestamp: number = 0;
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  /**
   * Fetch all available models from OpenRouter via our API
   */
  async fetchModels(): Promise<AIModel[]> {
    // Check cache first
    if (this.cache && Date.now() - this.cacheTimestamp < this.cacheExpiry) {
      return this.cache;
    }

    try {
      const response = await fetch('/api/openrouter/models');

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch models');
      }

      // Transform OpenRouter models to our AIModel format
      // Convert pricing from per-token to per-million-tokens
      const models: AIModel[] = result.data.map((model: OpenRouterModel) => ({
        id: model.id,
        name: model.name,
        description: model.description,
        context_length: model.context_length,
        pricing: {
          prompt: (parseFloat(model.pricing.prompt) * 1000000).toString(),
          completion: (parseFloat(model.pricing.completion) * 1000000).toString(),
          image: model.pricing.image ? (parseFloat(model.pricing.image) * 1000000).toString() : '0'
        },
        architecture: model.architecture,
        created: model.created,
        isFavorite: false // Will be set based on user preferences
      }));

      // Update cache
      this.cache = models;
      this.cacheTimestamp = Date.now();

      return models;
    } catch (error) {
      console.error('Error fetching OpenRouter models:', error);
      throw error;
    }
  }

  /**
   * Filter models by category (values already converted to per-million-tokens)
   */
  filterByCategory(models: AIModel[], category: ModelCategory): AIModel[] {
    switch (category) {
      case 'free':
        return models.filter(model =>
          parseFloat(model.pricing.prompt) === 0 &&
          parseFloat(model.pricing.completion) === 0
        );
      case 'paid':
        return models.filter(model =>
          parseFloat(model.pricing.prompt) > 0 ||
          parseFloat(model.pricing.completion) > 0
        );
      case 'favorites':
        return models.filter(model => model.isFavorite);
      default:
        return models;
    }
  }

  /**
   * Sort models by specified criteria
   */
  sortModels(models: AIModel[], sortBy: ModelSortBy): AIModel[] {
    const sortedModels = [...models];

    switch (sortBy) {
      case 'newest':
        return sortedModels.sort((a, b) => (b.created || 0) - (a.created || 0));
      
      case 'price_low':
        return sortedModels.sort((a, b) => {
          const priceA = parseFloat(a.pricing.prompt) + parseFloat(a.pricing.completion);
          const priceB = parseFloat(b.pricing.prompt) + parseFloat(b.pricing.completion);
          return priceA - priceB;
        });
      
      case 'price_high':
        return sortedModels.sort((a, b) => {
          const priceA = parseFloat(a.pricing.prompt) + parseFloat(a.pricing.completion);
          const priceB = parseFloat(b.pricing.prompt) + parseFloat(b.pricing.completion);
          return priceB - priceA;
        });
      
      case 'context_high':
        return sortedModels.sort((a, b) => b.context_length - a.context_length);
      
      default:
        return sortedModels;
    }
  }

  /**
   * Search models by name (legacy method - kept for compatibility)
   * @deprecated Use advancedSearchService.searchModels instead
   */
  searchModels(models: AIModel[], searchTerm: string): AIModel[] {
    if (!searchTerm.trim()) return models;

    const term = searchTerm.toLowerCase();
    return models.filter(model =>
      model.name.toLowerCase().includes(term) ||
      model.id.toLowerCase().includes(term) ||
      (model.description && model.description.toLowerCase().includes(term))
    );
  }

  /**
   * Format price for display (already converted to per-million-tokens)
   */
  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    if (numPrice === 0) return 'Grátis';
    if (numPrice < 0.01) return '< $0.01';
    if (numPrice < 1) return `$${numPrice.toFixed(3)}`;
    return `$${numPrice.toFixed(2)}`;
  }

  /**
   * Format context length for display
   */
  formatContextLength(contextLength: number): string {
    if (contextLength >= 1000000) {
      return `${(contextLength / 1000000).toFixed(1)}M`;
    } else if (contextLength >= 1000) {
      return `${(contextLength / 1000).toFixed(0)}K`;
    }
    return contextLength.toString();
  }

  /**
   * Check if model is free (already converted values)
   */
  isFreeModel(model: AIModel): boolean {
    return parseFloat(model.pricing.prompt) === 0 &&
           parseFloat(model.pricing.completion) === 0;
  }

  /**
   * Get total price per million tokens (input + output) - already converted
   */
  getTotalPrice(model: AIModel): number {
    return parseFloat(model.pricing.prompt) + parseFloat(model.pricing.completion);
  }

  /**
   * Get model name by ID
   */
  async getModelNameById(modelId: string): Promise<string> {
    try {
      const models = await this.fetchModels();
      const model = models.find(m => m.id === modelId);
      return model ? model.name : modelId; // Return ID as fallback
    } catch (error) {
      console.error('Error fetching model name:', error);
      return modelId; // Return ID as fallback
    }
  }
}

export const openRouterService = new OpenRouterService();
