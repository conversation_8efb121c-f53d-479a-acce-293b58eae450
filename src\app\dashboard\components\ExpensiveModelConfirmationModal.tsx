'use client';

import { useState, useEffect } from 'react';
import { AIModel } from '@/lib/types/chat';
import { openRouterService } from '@/lib/openRouterService';

interface ExpensiveModelConfirmationModalProps {
  isOpen: boolean;
  model: AIModel | null;
  onConfirm: () => void;
  onCancel: () => void;
}

const ExpensiveModelConfirmationModal = ({
  isOpen,
  model,
  onConfirm,
  onCancel
}: ExpensiveModelConfirmationModalProps) => {
  const [mounted, setMounted] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');
  const [isValid, setIsValid] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isOpen && model) {
      setConfirmationText('');
      setIsValid(false);
    }
  }, [isOpen, model]);

  useEffect(() => {
    if (model) {
      setIsValid(confirmationText.trim() === model.id);
    }
  }, [confirmationText, model]);

  const handleConfirm = () => {
    if (isValid) {
      onConfirm();
      setConfirmationText('');
      setIsValid(false);
    }
  };

  const handleCancel = () => {
    onCancel();
    setConfirmationText('');
    setIsValid(false);
  };

  if (!mounted || !isOpen || !model) return null;

  const totalPrice = openRouterService.getTotalPrice(model);

  return (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-md flex items-center justify-center transition-opacity duration-300 z-[70]"
      onClick={handleCancel}
    >
      <div
        className="glass-effect rounded-2xl shadow-2xl p-0 w-full max-w-lg transform transition-all duration-300 border border-amber-500/40 shadow-amber-500/20"
        onClick={(e) => e.stopPropagation()}
        style={{
          background: 'rgba(15, 23, 42, 0.95)',
          backdropFilter: 'blur(20px)',
        }}
      >
        {/* Modal Header */}
        <div className="p-6 border-b border-amber-600/30">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-amber-500/20 to-orange-500/20 flex items-center justify-center border border-amber-500/30">
              <svg className="w-6 h-6 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-bold text-amber-100">Modelo Caro Detectado</h3>
              <p className="text-amber-300/80 text-sm">Este modelo tem custo elevado</p>
            </div>
          </div>
        </div>

        {/* Modal Body */}
        <div className="p-6 space-y-6">
          {/* Model Info */}
          <div className="bg-gradient-to-br from-amber-900/20 to-orange-900/20 rounded-xl p-4 border border-amber-600/20">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-amber-100 text-lg truncate">{model.name}</h4>
                <p className="text-xs text-amber-300/70 truncate mt-1 font-mono">{model.id}</p>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-3 mt-4">
              <div className="bg-amber-800/20 rounded-lg p-3 border border-amber-600/20 text-center">
                <span className="block text-xs text-amber-300/70 font-medium mb-1">Input</span>
                <span className="text-amber-200 font-semibold">{openRouterService.formatPrice(model.pricing.prompt)}/1M</span>
              </div>
              <div className="bg-amber-800/20 rounded-lg p-3 border border-amber-600/20 text-center">
                <span className="block text-xs text-amber-300/70 font-medium mb-1">Output</span>
                <span className="text-amber-200 font-semibold">{openRouterService.formatPrice(model.pricing.completion)}/1M</span>
              </div>
              <div className="bg-gradient-to-br from-amber-700/30 to-orange-700/30 rounded-lg p-3 border border-amber-500/30 text-center">
                <span className="block text-xs text-amber-300/70 font-medium mb-1">Total</span>
                <span className="text-amber-100 font-bold">${totalPrice.toFixed(2)}/1M</span>
              </div>
            </div>
          </div>

          {/* Warning Message */}
          <div className="bg-gradient-to-r from-amber-900/30 to-orange-900/30 rounded-xl p-4 border border-amber-500/30">
            <div className="flex items-start space-x-3">
              <svg className="w-5 h-5 text-amber-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-amber-200 font-medium text-sm">
                  Este modelo tem custo superior a <span className="font-bold">$20.00</span> por milhão de tokens.
                </p>
                <p className="text-amber-300/80 text-xs mt-1">
                  Certifique-se de que deseja usar este modelo antes de prosseguir.
                </p>
              </div>
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="space-y-3">
            <label className="block text-amber-200 font-medium text-sm">
              Para confirmar, digite o ID do modelo:
            </label>
            <div className="relative">
              <input
                type="text"
                value={confirmationText}
                onChange={(e) => setConfirmationText(e.target.value)}
                placeholder={model.id}
                className="w-full px-4 py-3 bg-slate-800/50 border border-amber-600/30 rounded-xl text-amber-100 placeholder-amber-400/50 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500/50 transition-all duration-200 font-mono text-sm"
              />
              {isValid && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              )}
            </div>
            <p className="text-amber-400/70 text-xs">
              Digite exatamente: <span className="font-mono bg-amber-900/20 px-2 py-1 rounded">{model.id}</span>
            </p>
          </div>
        </div>

        {/* Modal Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-amber-700/30">
          <button
            onClick={handleCancel}
            className="px-6 py-2.5 rounded-lg transition-all duration-200 font-medium bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-white"
          >
            Cancelar
          </button>
          <button
            onClick={handleConfirm}
            disabled={!isValid}
            className={`px-6 py-2.5 text-white rounded-lg transition-all duration-200 font-medium shadow-lg ${
              isValid
                ? 'bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 hover:shadow-amber-500/25'
                : 'bg-slate-600/50 cursor-not-allowed opacity-50'
            }`}
          >
            Confirmar Seleção
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExpensiveModelConfirmationModal;
