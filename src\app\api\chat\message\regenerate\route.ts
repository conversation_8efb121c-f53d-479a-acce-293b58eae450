import { NextRequest, NextResponse } from 'next/server';
import { chatStorageService } from '@/lib/chatStorageService';
import { chatStatisticsService } from '@/lib/chatStatisticsService';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, chatId, fromMessageId, reason } = body;

    // Validar parâmetros obrigatórios
    if (!userId || !chatId || !fromMessageId) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, chatId, fromMessageId' },
        { status: 400 }
      );
    }

    // Obter dados atuais do chat
    const currentData = await chatStorageService.getChatData(userId, chatId);
    
    if (!currentData) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    // Encontrar a mensagem de referência
    const messageIndex = currentData.messages.findIndex(message => message.id === fromMessageId);

    if (messageIndex === -1) {
      return NextResponse.json(
        { error: 'Reference message not found' },
        { status: 404 }
      );
    }

    const referenceMessage = currentData.messages[messageIndex];

    // Verificar se é uma mensagem do usuário
    if (referenceMessage.role !== 'user') {
      return NextResponse.json(
        { error: 'Can only regenerate from user messages' },
        { status: 400 }
      );
    }

    // Manter apenas as mensagens até a mensagem de referência (inclusive)
    const messagesToKeep = currentData.messages.slice(0, messageIndex + 1);

    // Atualizar dados do chat
    const updatedChatData = {
      ...currentData,
      messages: messagesToKeep
    };

    await chatStorageService.updateChatData(userId, chatId, updatedChatData);

    // Atualizar estatísticas de regeneração
    try {
      await chatStatisticsService.updateStatisticsOnMessageRegeneration(
        userId,
        chatId,
        reason || 'Regeneração manual'
      );
    } catch (error) {
      console.error('Error updating regeneration statistics:', error);
      // Não falhar a operação se as estatísticas falharem
    }

    return NextResponse.json({
      success: true,
      message: 'Messages after reference point deleted successfully',
      remainingMessages: messagesToKeep.length
    });

  } catch (error) {
    console.error('Error regenerating from message:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
