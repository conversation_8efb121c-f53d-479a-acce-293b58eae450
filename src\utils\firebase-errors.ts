type FirebaseErrorCode = 
  | 'auth/email-already-in-use'
  | 'auth/invalid-email'
  | 'auth/weak-password'
  | 'auth/user-not-found'
  | 'auth/wrong-password'
  | 'auth/invalid-credential'
  | 'auth/too-many-requests'
  | 'auth/requires-recent-login'
  | 'auth/user-disabled'
  | 'auth/account-exists-with-different-credential'
  | 'auth/operation-not-allowed'
  | string;

const errorMessages: Record<FirebaseErrorCode, string> = {
  'auth/email-already-in-use': 'Este email já está sendo utilizado por outra conta.',
  'auth/invalid-email': 'O endereço de email fornecido é inválido.',
  'auth/weak-password': 'A senha é muito fraca. Use pelo menos 6 caracteres.',
  'auth/user-not-found': 'Não existe usuário com este email.',
  'auth/wrong-password': 'Senha incorreta.',
  'auth/invalid-credential': 'Credenciais inválidas. Verifique seu email e senha.',
  'auth/too-many-requests': 'Acesso temporariamente bloqueado devido a muitas tentativas. Tente novamente mais tarde.',
  'auth/requires-recent-login': 'Esta operação é sensível e requer autenticação recente. Faça login novamente.',
  'auth/user-disabled': 'Esta conta foi desativada.',
  'auth/account-exists-with-different-credential': 'Um usuário já existe com o mesmo email, mas credenciais de login diferentes.',
  'auth/operation-not-allowed': 'Esta operação não é permitida.',
};

export const getFirebaseErrorMessage = (errorCode: string): string => {
  return errorMessages[errorCode] || 'Ocorreu um erro. Por favor, tente novamente mais tarde.';
};

export const handleFirebaseError = (error: any): string => {
  const errorCode = error?.code || 'unknown-error';
  return getFirebaseErrorMessage(errorCode);
}; 