import { useState, useEffect, useCallback } from 'react';
import { ChatMessage, AttachmentMetadata } from '@/lib/types/chat';
import { invalidateConversationCache } from '@/lib/conversationService';
import { chatStatisticsService } from '@/lib/chatStatisticsService';
import { useWordCount } from './useWordCount';

interface UseChatMessagesProps {
  userId: string | null;
  chatId: string | null;
}

interface UseChatMessagesReturn {
  messages: ChatMessage[];
  loading: boolean;
  error: string | null;
  addMessage: (role: 'user' | 'assistant', content: string, attachments?: AttachmentMetadata[]) => Promise<void>;
  addMessageWithUsage: (role: 'user' | 'assistant', content: string, attachments?: AttachmentMetadata[], usage?: any, responseTime?: number, typingSpeed?: number) => Promise<void>;
  addOptimisticMessage: (role: 'user' | 'assistant', content: string, attachments?: AttachmentMetadata[]) => string;
  confirmOptimisticMessage: (tempId: string) => void;
  removeOptimisticMessage: (tempId: string) => void;
  removeMessagesAfter: (messageId: string) => void;
  editMessageOptimistic: (messageId: string, content: string) => void;
  refreshMessages: (showLoading?: boolean) => Promise<void>;
  syncChat: (direction?: 'toFirestore' | 'fromFirestore') => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  editMessage: (messageId: string, content: string) => Promise<void>;
  regenerateFromMessage: (messageId: string) => Promise<void>;
}

export const useChatMessages = ({ userId, chatId }: UseChatMessagesProps): UseChatMessagesReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Hook para contagem de palavras em tempo real
  const { updateWordCount } = useWordCount(chatId, messages);

  // Função para adicionar mensagem otimista (aparece imediatamente)
  const addOptimisticMessage = useCallback((role: 'user' | 'assistant', content: string, attachments?: AttachmentMetadata[]): string => {
    const tempId = `temp-${role}-${Date.now()}-${Math.random()}`;
    const optimisticMessage: ChatMessage = {
      id: tempId,
      role,
      content,
      timestamp: Date.now(),
      ...(attachments && { attachments })
    };

    setMessages(prev => [...prev, optimisticMessage]);
    return tempId;
  }, []);

  // Função para confirmar mensagem otimista (apenas remove da lista otimista)
  const confirmOptimisticMessage = useCallback((tempId: string) => {
    // Não faz nada - a mensagem já está na lista principal
  }, []);

  // Função para remover mensagem otimista (em caso de erro)
  const removeOptimisticMessage = useCallback((tempId: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== tempId));
  }, []);

  // Função para remover mensagens após uma mensagem específica (otimização visual)
  const removeMessagesAfter = useCallback((messageId: string) => {
    setMessages(prev => {
      const messageIndex = prev.findIndex(m => m.id === messageId);
      if (messageIndex === -1) return prev;

      // Manter apenas as mensagens até a mensagem especificada (inclusive)
      return prev.slice(0, messageIndex + 1);
    });
  }, []);

  // Função para editar mensagem de forma otimista (visual imediato)
  const editMessageOptimistic = useCallback((messageId: string, content: string) => {
    setMessages(prev => {
      const messageIndex = prev.findIndex(m => m.id === messageId);
      if (messageIndex === -1) return prev;

      // Criar nova array com a mensagem editada
      const updatedMessages = [...prev];
      updatedMessages[messageIndex] = {
        ...updatedMessages[messageIndex],
        content: content.trim(),
        timestamp: Date.now() // Atualizar timestamp para indicar edição
      };

      return updatedMessages;
    });
  }, []);

  // Função para carregar mensagens
  const refreshMessages = useCallback(async (showLoading: boolean = true) => {
    if (!userId || !chatId) return;

    if (showLoading) {
      setLoading(true);
    }
    setError(null);

    try {
      const response = await fetch(`/api/chat/message?userId=${userId}&chatId=${chatId}`);
      const data = await response.json();

      if (data.success) {
        setMessages(data.messages);
      } else {
        setError(data.error || 'Failed to load messages');
      }
    } catch (err) {
      setError('Network error while loading messages');
      console.error('Error loading messages:', err);
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  }, [userId, chatId]);

  // Função para adicionar uma nova mensagem (com optimistic updates)
  const addMessage = useCallback(async (role: 'user' | 'assistant', content: string, attachments?: AttachmentMetadata[]) => {
    if (!userId || !chatId) {
      setError('User ID and Chat ID are required');
      return;
    }

    setError(null);

    // Criar mensagem temporária para optimistic update
    const tempId = `temp-${role}-${Date.now()}-${Math.random()}`;
    const tempMessage: ChatMessage = {
      id: tempId,
      role,
      content,
      timestamp: Date.now(),
      ...(attachments && { attachments })
    };

    // Adicionar mensagem temporária imediatamente (optimistic update)
    setMessages(prev => [...prev, tempMessage]);

    try {
      const response = await fetch('/api/chat/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          chatId,
          role,
          content,
          attachments,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Recarregar mensagens para obter o ID correto do backend (sem mostrar loading)
        await refreshMessages(false);
        invalidateConversationCache();
      } else {
        // Remover mensagem temporária em caso de erro
        setMessages(prev => prev.filter(msg => msg.id !== tempId));
        setError(data.error || 'Failed to add message');
      }
    } catch (err) {
      // Remover mensagem temporária em caso de erro
      setMessages(prev => prev.filter(msg => msg.id !== tempId));
      setError('Network error while adding message');
      console.error('Error adding message:', err);
    }
  }, [userId, chatId]);

  // Função para adicionar mensagem com dados de usage e estatísticas
  const addMessageWithUsage = useCallback(async (
    role: 'user' | 'assistant',
    content: string,
    attachments?: AttachmentMetadata[],
    usage?: any,
    responseTime?: number,
    typingSpeed?: number
  ) => {
    if (!userId || !chatId) {
      setError('User ID and Chat ID are required');
      return;
    }

    setError(null);

    // Criar mensagem temporária com dados de usage
    const tempId = `temp-${role}-${Date.now()}-${Math.random()}`;
    const tempMessage: ChatMessage = {
      id: tempId,
      role,
      content,
      timestamp: Date.now(),
      ...(attachments && { attachments }),
      ...(usage && {
        usage: {
          prompt_tokens: usage.prompt_tokens || 0,
          completion_tokens: usage.completion_tokens || 0,
          total_tokens: usage.total_tokens || 0,
          cost: usage.cost || 0
        }
      }),
      ...(responseTime && { responseTime }),
      ...(typingSpeed && { typingSpeed })
    };

    // Adicionar mensagem temporária imediatamente (optimistic update)
    setMessages(prev => {
      const newMessages = [...prev, tempMessage];

      // Atualizar estatísticas em background (exceto contagem de palavras que é feita pelo useWordCount)
      if (userId && chatId) {
        const previousMessage = prev.length > 0 ? prev[prev.length - 1] : undefined;
        chatStatisticsService.updateStatisticsOnNewMessageWithoutWordCount(userId, chatId, tempMessage, previousMessage)
          .catch(err => {
            console.error('Error updating statistics:', err);
            // Se o erro for relacionado a propriedades undefined, pode ser necessário migração
            if (err.message && err.message.includes('Cannot read properties of undefined')) {
              console.log('🔄 Tentando migrar estatísticas...');
              // A migração já é feita automaticamente no getChatStatistics
            }
          });
      }

      return newMessages;
    });

    try {
      const response = await fetch('/api/chat/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          chatId,
          role,
          content,
          attachments,
          usage,
          responseTime,
          typingSpeed
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Recarregar mensagens para obter o ID correto do backend (sem mostrar loading)
        await refreshMessages(false);
        invalidateConversationCache();
      } else {
        // Remover mensagem temporária em caso de erro
        setMessages(prev => prev.filter(msg => msg.id !== tempId));
        setError(data.error || 'Failed to add message');
      }
    } catch (err) {
      // Remover mensagem temporária em caso de erro
      setMessages(prev => prev.filter(msg => msg.id !== tempId));
      setError('Network error while adding message');
      console.error('Error adding message:', err);
    }
  }, [userId, chatId]);

  // Função para sincronizar chat
  const syncChat = useCallback(async (direction?: 'toFirestore' | 'fromFirestore') => {
    if (!userId || !chatId) {
      setError('User ID and Chat ID are required');
      return;
    }

    setError(null);

    try {
      const response = await fetch('/api/storage/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          chatId,
          direction,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        setError(data.error || 'Failed to sync chat');
      }
    } catch (err) {
      setError('Network error while syncing chat');
      console.error('Error syncing chat:', err);
    }
  }, [userId, chatId]);

  // Função para deletar uma mensagem
  const deleteMessage = useCallback(async (messageId: string) => {
    if (!userId || !chatId) {
      setError('User ID and Chat ID are required');
      return;
    }

    setError(null);

    try {
      const response = await fetch('/api/chat/message/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          chatId,
          messageId,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Recarregar mensagens após deletar (sem mostrar loading)
        await refreshMessages(false);
        // Invalidar cache de conversas para atualizar última mensagem
        invalidateConversationCache();
      } else {
        setError(data.error || 'Failed to delete message');
      }
    } catch (err) {
      setError('Network error while deleting message');
      console.error('Error deleting message:', err);
    }
  }, [userId, chatId, refreshMessages]);

  // Função para editar uma mensagem
  const editMessage = useCallback(async (messageId: string, content: string) => {
    if (!userId || !chatId) {
      setError('User ID and Chat ID are required');
      return;
    }

    setError(null);

    // Aplicar edição otimista IMEDIATAMENTE (antes da API call)
    editMessageOptimistic(messageId, content);

    try {
      const response = await fetch('/api/chat/message/edit', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          chatId,
          messageId,
          content,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Recarregar mensagens após editar (sem mostrar loading) para sincronizar com backend
        await refreshMessages(false);
        // Invalidar cache de conversas para atualizar última mensagem
        invalidateConversationCache();
      } else {
        // Em caso de erro, reverter a edição otimista recarregando as mensagens
        await refreshMessages(false);
        setError(data.error || 'Failed to edit message');
      }
    } catch (err) {
      // Em caso de erro, reverter a edição otimista recarregando as mensagens
      await refreshMessages(false);
      setError('Network error while editing message');
      console.error('Error editing message:', err);
    }
  }, [userId, chatId, refreshMessages, editMessageOptimistic]);

  // Função para regenerar a partir de uma mensagem
  const regenerateFromMessage = useCallback(async (messageId: string) => {
    if (!userId || !chatId) {
      setError('User ID and Chat ID are required');
      return;
    }

    setError(null);

    try {
      const response = await fetch('/api/chat/message/regenerate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          chatId,
          fromMessageId: messageId,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Recarregar mensagens após regenerar (sem mostrar loading)
        await refreshMessages(false);
        // Invalidar cache de conversas para atualizar última mensagem
        invalidateConversationCache();
      } else {
        setError(data.error || 'Failed to regenerate from message');
      }
    } catch (err) {
      setError('Network error while regenerating from message');
      console.error('Error regenerating from message:', err);
    }
  }, [userId, chatId, refreshMessages]);

  // Limpar mensagens imediatamente quando chatId muda
  useEffect(() => {
    setMessages([]);
    setError(null);
    setLoading(false);
  }, [chatId]);

  // Carregar mensagens quando userId ou chatId mudarem
  useEffect(() => {
    if (userId && chatId) {
      refreshMessages();
    }
  }, [userId, chatId, refreshMessages]);

  return {
    messages,
    loading,
    error,
    addMessage,
    addMessageWithUsage,
    addOptimisticMessage,
    confirmOptimisticMessage,
    removeOptimisticMessage,
    removeMessagesAfter,
    editMessageOptimistic,
    refreshMessages,
    syncChat,
    deleteMessage,
    editMessage,
    regenerateFromMessage,
  };
};
