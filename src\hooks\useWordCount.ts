import { useEffect, useCallback } from 'react';
import { ChatMessage } from '@/lib/types/chat';
import { chatStatisticsService } from '@/lib/chatStatisticsService';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook para gerenciar contagem de palavras em tempo real
 */
export const useWordCount = (chatId: string | null, messages: ChatMessage[]) => {
  const { user } = useAuth();

  // Função para contar palavras de uma mensagem
  const countWords = useCallback((text: string): number => {
    if (!text || text.trim().length === 0) return 0;
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }, []);

  // Função para calcular estatísticas de palavras das mensagens atuais
  const calculateWordStats = useCallback(() => {
    let totalWords = 0;
    let userWords = 0;
    let assistantWords = 0;

    for (const message of messages) {
      const wordCount = countWords(message.content);
      totalWords += wordCount;
      
      if (message.role === 'user') {
        userWords += wordCount;
      } else {
        assistantWords += wordCount;
      }
    }

    return { totalWords, userWords, assistantWords };
  }, [messages, countWords]);

  // Função para atualizar as estatísticas no servidor
  const updateWordCountStatistics = useCallback(async () => {
    if (!user?.uid || !chatId || messages.length === 0) {
      console.log('⏭️ Skipping word count update:', {
        hasUser: !!user?.uid,
        hasChatId: !!chatId,
        messageCount: messages.length
      });
      return;
    }

    console.log(`🔄 Updating word count for chat ${chatId} with ${messages.length} messages`);

    try {
      await chatStatisticsService.updateWordCountFromMessages(user.uid, chatId, messages);
      console.log('✅ Word count statistics updated successfully');
    } catch (error) {
      console.error('❌ Error updating word count statistics:', error);
    }
  }, [user?.uid, chatId, messages]);

  // Atualizar estatísticas sempre que as mensagens mudarem
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateWordCountStatistics();
    }, 1000); // Debounce de 1 segundo para evitar muitas chamadas

    return () => clearTimeout(timeoutId);
  }, [updateWordCountStatistics]);

  // Retornar estatísticas atuais e função de atualização manual
  return {
    wordStats: calculateWordStats(),
    updateWordCount: updateWordCountStatistics,
    countWords
  };
};
