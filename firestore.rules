rules_version='2'

service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORÁRIO: Permitir tudo para usuários autenticados (para desenvolvimento)
    match /{document=**} {
      allow read, write: if request.auth != null;
    }

    // Regras específicas para favoritos
    match /usuarios/{userId}/favoritos/{favoriteId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Regras especiais para área administrativa (acesso público limitado)
    match /admin/credenciais {
      // Permitir leitura para verificar se existe senha (necessário para primeira configuração)
      allow read: if true;
      // Permitir escrita sempre (será controlado pela aplicação)
      allow write: if true;
    }

    match /admin/manutencao {
      // Permitir leitura para verificar status de manutenção
      allow read: if true;
      // Permitir escrita sempre (será controlado pela aplicação)
      allow write: if true;
    }
  }
}
