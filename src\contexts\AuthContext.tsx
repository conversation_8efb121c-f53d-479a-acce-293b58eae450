'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  User, 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword,
  signOut, 
  onAuthStateChanged,
  sendEmailVerification,
  UserCredential,
  updateProfile
} from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { saveUserData, getUserData } from '@/lib/userService';

interface UserData {
  username: string;
  email: string;
  profilePhotoURL?: string;
  chatAppearance?: {
    fontSize: number;
    fontFamily: string;
  };
  streaming?: {
    enabled: boolean;
  };
  chatSessions?: {
    enabled: boolean;
    wordsPerSession: number;
  };
}

interface AuthContextType {
  user: User | null;
  userData: UserData | null;
  loading: boolean;
  signUp: (email: string, password: string, username: string) => Promise<UserCredential>;
  signIn: (email: string, password: string) => Promise<UserCredential>;
  logout: () => Promise<void>;
  sendVerificationEmail: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userData: null,
  loading: true,
  signUp: async () => {
    throw new Error('Function not implemented');
  },
  signIn: async () => {
    throw new Error('Function not implemented');
  },
  logout: async () => {},
  sendVerificationEmail: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  // Busca os dados do usuário do Firestore
  const fetchUserData = async (uid: string) => {
    try {
      const data = await getUserData(uid);
      if (data) {
        setUserData(data);
      }
    } catch (error) {
      console.error('Erro ao buscar dados do usuário:', error);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);
      
      if (user) {
        await fetchUserData(user.uid);
      } else {
        setUserData(null);
      }
      
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signUp = async (email: string, password: string, username: string): Promise<UserCredential> => {
    console.log('🔥 signUp called with:', { email, username });

    const result = await createUserWithEmailAndPassword(auth, email, password);
    console.log('🔥 Firebase Auth user created:', result.user.uid);

    // Atualiza o displayName no perfil do Firebase Auth
    await updateProfile(result.user, {
      displayName: username
    });
    console.log('🔥 Profile updated with displayName:', username);

    // Salva os dados do usuário no Firestore
    console.log('🔥 About to save user data to Firestore...');
    await saveUserData(result.user.uid, {
      username,
      email
    });
    console.log('🔥 User data saved to Firestore successfully');

    // Atualiza o estado local
    setUserData({
      username,
      email
    });
    console.log('🔥 Local state updated');

    await sendEmailVerification(result.user);
    console.log('🔥 Email verification sent');

    return result;
  };

  const signIn = async (email: string, password: string): Promise<UserCredential> => {
    const result = await signInWithEmailAndPassword(auth, email, password);
    
    // Busca os dados do usuário após o login
    await fetchUserData(result.user.uid);
    
    return result;
  };

  const logout = async (): Promise<void> => {
    await signOut(auth);
    setUserData(null);
  };

  const sendVerificationEmail = async (): Promise<void> => {
    if (auth.currentUser) {
      await sendEmailVerification(auth.currentUser);
    } else {
      throw new Error('No user is currently signed in');
    }
  };

  const value = {
    user,
    userData,
    loading,
    signUp,
    signIn,
    logout,
    sendVerificationEmail,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}; 