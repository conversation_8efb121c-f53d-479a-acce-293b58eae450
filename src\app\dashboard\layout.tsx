'use client';

import { ConversationProvider } from '@/contexts/ConversationContext';
import { ChatFolderProvider } from '@/contexts/ChatFolderContext';
import MaintenanceWrapper from '@/components/MaintenanceWrapper';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MaintenanceWrapper>
      <ConversationProvider>
        <ChatFolderProvider>
          {children}
        </ChatFolderProvider>
      </ConversationProvider>
    </MaintenanceWrapper>
  );
}
