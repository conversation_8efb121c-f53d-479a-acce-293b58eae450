'use client';

import React, { createContext, useContext, useCallback } from 'react';

interface BalanceContextType {
  refreshBalance: () => void;
  registerRefreshCallback: (callback: () => void) => void;
}

const BalanceContext = createContext<BalanceContextType>({
  refreshBalance: () => {},
  registerRefreshCallback: () => {},
});

export const useBalance = () => useContext(BalanceContext);

export const BalanceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Esta função será definida pelo componente que usa o hook useOpenRouterBalance
  const [refreshBalanceCallback, setRefreshBalanceCallback] = React.useState<(() => void) | null>(null);

  const refreshBalance = useCallback(() => {
    if (refreshBalanceCallback) {
      refreshBalanceCallback();
    }
  }, [refreshBalanceCallback]);

  // Função para registrar o callback de refresh
  const registerRefreshCallback = useCallback((callback: () => void) => {
    setRefreshBalanceCallback(() => callback);
  }, []);

  const value = {
    refreshBalance,
    registerRefreshCallback,
  };

  return (
    <BalanceContext.Provider value={value}>
      {children}
    </BalanceContext.Provider>
  );
};
