'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { ChatStatistics } from '@/lib/types/chat';
import { chatStatisticsService } from '@/lib/chatStatisticsService';

import { useAuth } from '@/contexts/AuthContext';

interface ChatStatisticsModalProps {
  isOpen: boolean;
  onClose: () => void;
  chatId: string;
  chatName: string;
}

const ChatStatisticsModal = ({ isOpen, onClose, chatId, chatName }: ChatStatisticsModalProps) => {
  const [statistics, setStatistics] = useState<ChatStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (isOpen && chatId && user?.uid) {
      loadStatistics();
    }
  }, [isOpen, chatId, user?.uid]);

  const loadStatistics = async () => {
    if (!user?.uid || !chatId) return;

    setLoading(true);
    setError(null);

    try {
      const stats = await chatStatisticsService.getChatStatistics(user.uid, chatId);
      setStatistics(stats);
    } catch (err) {
      console.error('Error loading statistics:', err);
      setError('Erro ao carregar estatísticas');
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (value: number) => {
    // Verificar se o valor é válido
    if (!value || value === 0 || !isFinite(value) || isNaN(value)) {
      return '0';
    }
    return new Intl.NumberFormat('pt-BR').format(Math.round(value));
  };

  const formatCurrency = (amount: number): string => {
    if (!amount || amount === 0 || !isFinite(amount) || isNaN(amount)) {
      return '$0.0000';
    }
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount);
  };

  const formatDuration = (ms: number) => {
    // Verificar se o valor é válido
    if (!ms || ms === 0 || !isFinite(ms) || isNaN(ms)) {
      return '0s';
    }

    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };



  const getHeatmapColor = (value: number, max: number) => {
    if (max === 0) return 'bg-slate-800/30';
    const intensity = value / max;
    if (intensity === 0) return 'bg-slate-800/30';
    if (intensity < 0.25) return 'bg-blue-900/40';
    if (intensity < 0.5) return 'bg-blue-800/60';
    if (intensity < 0.75) return 'bg-blue-700/80';
    return 'bg-blue-600';
  };

  if (!isOpen) return null;

  const modalContent = (
    <div
      className="modal-overlay fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4"
    >
      <div
        className="modal-content bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 backdrop-blur-xl border border-blue-500/20 rounded-3xl w-full max-w-5xl max-h-[90vh] overflow-hidden shadow-2xl shadow-blue-500/20"
      >
        {/* Header com gradiente */}
        <div className="relative bg-gradient-to-r from-blue-600/20 via-indigo-600/20 to-purple-600/20 border-b border-blue-500/20 p-8">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10"></div>
          <div className="relative flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-500/20 rounded-2xl border border-blue-400/30">
                <svg className="w-8 h-8 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-3xl font-bold text-white">Estatísticas do Chat</h2>
                <p className="text-blue-200/70 mt-1">{chatName}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-blue-300 hover:text-white transition-all duration-300 p-3 hover:bg-blue-500/20 rounded-2xl border border-transparent hover:border-blue-400/30 group"
            >
              <svg className="w-6 h-6 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-8 overflow-y-auto max-h-[calc(90vh-200px)] scrollbar-thin scrollbar-thumb-blue-500/20 scrollbar-track-transparent">
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-3 text-blue-300">Carregando estatísticas...</span>
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <div className="text-red-400 mb-2">⚠️</div>
              <p className="text-red-300">{error}</p>
            </div>
          )}

          {!loading && !error && !statistics && (
            <div className="text-center py-12">
              <div className="text-slate-400 mb-2">📊</div>
              <p className="text-slate-300">Nenhuma estatística disponível</p>
              <p className="text-sm text-slate-400 mt-1">Envie algumas mensagens para gerar estatísticas</p>
            </div>
          )}

          {statistics && (
            <div className="space-y-8">
              {/* Estatísticas Gerais */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="group relative bg-gradient-to-br from-blue-500/10 to-indigo-500/10 backdrop-blur-sm border border-blue-400/20 rounded-2xl p-6 hover:border-blue-300/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-3">
                      <div className="p-2 bg-blue-500/20 rounded-xl">
                        <svg className="w-5 h-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                    </div>
                    <div className="text-3xl font-bold text-white mb-2">{formatNumber(statistics.totalMessages)}</div>
                    <div className="text-sm text-blue-200/70 font-medium">Total de Mensagens</div>
                  </div>
                </div>

                <div className="group relative bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-400/20 rounded-2xl p-6 hover:border-purple-300/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-3">
                      <div className="p-2 bg-purple-500/20 rounded-xl">
                        <svg className="w-5 h-5 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    </div>
                    <div className="text-3xl font-bold text-white mb-2">{formatNumber(statistics.userMessages)}</div>
                    <div className="text-sm text-purple-200/70 font-medium">Suas Mensagens</div>
                  </div>
                </div>

                <div className="group relative bg-gradient-to-br from-emerald-500/10 to-green-500/10 backdrop-blur-sm border border-emerald-400/20 rounded-2xl p-6 hover:border-emerald-300/40 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/10">
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-3">
                      <div className="p-2 bg-emerald-500/20 rounded-xl">
                        <svg className="w-5 h-5 text-emerald-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                    </div>
                    <div className="text-3xl font-bold text-white mb-2">{formatNumber(statistics.assistantMessages)}</div>
                    <div className="text-sm text-emerald-200/70 font-medium">Respostas da IA</div>
                  </div>
                </div>

                <div className="group relative bg-gradient-to-br from-amber-500/10 to-orange-500/10 backdrop-blur-sm border border-amber-400/20 rounded-2xl p-6 hover:border-amber-300/40 transition-all duration-300 hover:shadow-lg hover:shadow-amber-500/10">
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-3">
                      <div className="p-2 bg-amber-500/20 rounded-xl">
                        <svg className="w-5 h-5 text-amber-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                      </div>
                    </div>
                    <div className="text-3xl font-bold text-white mb-2">{formatNumber(statistics.totalAttachments)}</div>
                    <div className="text-sm text-amber-200/70 font-medium">Anexos</div>
                  </div>
                </div>
              </div>

              {/* Contagem de Palavras */}
              <div className="relative bg-gradient-to-br from-slate-800/40 to-indigo-900/20 backdrop-blur-sm border border-indigo-400/20 rounded-2xl p-8 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5"></div>
                <div className="relative">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="p-3 bg-indigo-500/20 rounded-xl border border-indigo-400/30">
                      <svg className="w-6 h-6 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white">Contagem de Palavras</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="group text-center p-6 bg-indigo-500/10 rounded-xl border border-indigo-400/20 hover:border-indigo-300/40 transition-all duration-300">
                      <div className="text-3xl font-bold text-indigo-300 mb-2">{formatNumber(statistics.totalWords || 0)}</div>
                      <div className="text-sm text-indigo-200/70 font-medium">Total de Palavras</div>
                    </div>
                    <div className="group text-center p-6 bg-purple-500/10 rounded-xl border border-purple-400/20 hover:border-purple-300/40 transition-all duration-300">
                      <div className="text-3xl font-bold text-purple-300 mb-2">{formatNumber(statistics.userWords || 0)}</div>
                      <div className="text-sm text-purple-200/70 font-medium">Suas Palavras</div>
                    </div>
                    <div className="group text-center p-6 bg-emerald-500/10 rounded-xl border border-emerald-400/20 hover:border-emerald-300/40 transition-all duration-300">
                      <div className="text-3xl font-bold text-emerald-300 mb-2">{formatNumber(statistics.assistantWords || 0)}</div>
                      <div className="text-sm text-emerald-200/70 font-medium">Palavras da IA</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tokens e Custos */}
              <div className="relative bg-gradient-to-br from-slate-800/40 to-blue-900/20 backdrop-blur-sm border border-blue-400/20 rounded-2xl p-8 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5"></div>
                <div className="relative">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="p-3 bg-blue-500/20 rounded-xl border border-blue-400/30">
                      <svg className="w-6 h-6 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white">Uso de Tokens e Custos</h3>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div className="group text-center p-4 bg-blue-500/10 rounded-xl border border-blue-400/20 hover:border-blue-300/40 transition-all duration-300">
                      <div className="text-2xl font-bold text-blue-300 mb-2">{formatNumber(statistics.totalPromptTokens)}</div>
                      <div className="text-sm text-blue-200/70 font-medium">Tokens de Input</div>
                    </div>
                    <div className="group text-center p-4 bg-emerald-500/10 rounded-xl border border-emerald-400/20 hover:border-emerald-300/40 transition-all duration-300">
                      <div className="text-2xl font-bold text-emerald-300 mb-2">{formatNumber(statistics.totalCompletionTokens)}</div>
                      <div className="text-sm text-emerald-200/70 font-medium">Tokens de Output</div>
                    </div>
                    <div className="group text-center p-4 bg-purple-500/10 rounded-xl border border-purple-400/20 hover:border-purple-300/40 transition-all duration-300">
                      <div className="text-2xl font-bold text-purple-300 mb-2">{formatNumber(statistics.totalTokens)}</div>
                      <div className="text-sm text-purple-200/70 font-medium">Total de Tokens</div>
                    </div>
                    <div className="group text-center p-4 bg-amber-500/10 rounded-xl border border-amber-400/20 hover:border-amber-300/40 transition-all duration-300">
                      <div className="text-2xl font-bold text-amber-300 mb-2">{formatCurrency(statistics.totalCost)}</div>
                      <div className="text-sm text-amber-200/70 font-medium">Custo Total</div>
                    </div>
                  </div>
                </div>
              </div>



              {/* Heatmap de Atividade */}
              <div className="relative bg-gradient-to-br from-slate-800/40 to-indigo-900/20 backdrop-blur-sm border border-blue-400/20 rounded-2xl p-8 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5"></div>
                <div className="relative">
                  <div className="flex items-center space-x-3 mb-8">
                    <div className="p-3 bg-indigo-500/20 rounded-xl border border-indigo-400/30">
                      <svg className="w-6 h-6 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white">Heatmap de Atividade</h3>
                  </div>

                  {/* Por Hora do Dia */}
                  <div className="mb-8">
                    <h4 className="text-lg font-medium text-blue-200 mb-4">Por Hora do Dia</h4>
                    <div className="grid grid-cols-12 gap-2">
                      {statistics.activityHeatmap.hourOfDay.map((count, hour) => {
                        const maxHour = Math.max(...statistics.activityHeatmap.hourOfDay);
                        return (
                          <div
                            key={hour}
                            className={`group h-10 rounded-xl ${getHeatmapColor(count, maxHour)} flex items-center justify-center text-xs text-white/80 font-medium hover:scale-110 transition-all duration-300 cursor-pointer border border-blue-400/20 hover:border-blue-300/40`}
                            title={`${hour}h: ${count} mensagens`}
                          >
                            {hour}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Por Dia da Semana */}
                  <div>
                    <h4 className="text-lg font-medium text-blue-200 mb-4">Por Dia da Semana</h4>
                    <div className="grid grid-cols-7 gap-3">
                      {['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'].map((day, index) => {
                        const count = statistics.activityHeatmap.dayOfWeek[index];
                        const maxDay = Math.max(...statistics.activityHeatmap.dayOfWeek);
                        return (
                          <div key={day} className="text-center">
                            <div className="text-sm text-blue-300/70 mb-2 font-medium">{day}</div>
                            <div
                              className={`group h-16 rounded-xl ${getHeatmapColor(count, maxDay)} flex flex-col items-center justify-center text-sm text-white/80 font-medium hover:scale-105 transition-all duration-300 cursor-pointer border border-blue-400/20 hover:border-blue-300/40`}
                              title={`${day}: ${count} mensagens`}
                            >
                              <div className="text-lg font-bold">{count}</div>
                              <div className="text-xs opacity-70">msgs</div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>

              {/* Palavras Mais Usadas */}
              {statistics.mostUsedWords.length > 0 && (
                <div className="relative bg-gradient-to-br from-slate-800/40 to-purple-900/20 backdrop-blur-sm border border-purple-400/20 rounded-2xl p-8 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5"></div>
                  <div className="relative">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="p-3 bg-purple-500/20 rounded-xl border border-purple-400/30">
                        <svg className="w-6 h-6 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-bold text-white">Palavras Mais Usadas</h3>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                      {statistics.mostUsedWords.map(({ word, count }, index) => (
                        <div key={word} className="group bg-purple-500/10 rounded-xl p-4 text-center border border-purple-400/20 hover:border-purple-300/40 transition-all duration-300 hover:scale-105">
                          <div className="text-sm font-medium text-purple-200 mb-1">{word}</div>
                          <div className="text-xs text-purple-300/70 bg-purple-500/20 px-2 py-1 rounded-lg">{count}x</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}



              {/* Estatísticas de Edição e Regeneração */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="relative bg-gradient-to-br from-blue-500/10 to-cyan-500/10 backdrop-blur-sm border border-blue-400/20 rounded-2xl p-6 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-transparent rounded-2xl"></div>
                  <div className="relative">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="p-3 bg-blue-500/20 rounded-xl border border-blue-400/30">
                        <svg className="w-6 h-6 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-white">Edições</h3>
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 bg-blue-500/10 rounded-xl">
                        <span className="text-blue-200/80 font-medium">Total de edições</span>
                        <span className="text-white font-bold text-lg">{statistics.messageEdits?.totalEdits || 0}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-blue-500/10 rounded-xl">
                        <span className="text-blue-200/80 font-medium">Suas edições</span>
                        <span className="text-white font-bold text-lg">{statistics.messageEdits?.userMessageEdits || 0}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-blue-500/10 rounded-xl">
                        <span className="text-blue-200/80 font-medium">Média por mensagem</span>
                        <span className="text-white font-bold text-lg">{(statistics.messageEdits?.averageEditsPerMessage || 0).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="relative bg-gradient-to-br from-orange-500/10 to-red-500/10 backdrop-blur-sm border border-orange-400/20 rounded-2xl p-6 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 to-transparent rounded-2xl"></div>
                  <div className="relative">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="p-3 bg-orange-500/20 rounded-xl border border-orange-400/30">
                        <svg className="w-6 h-6 text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-white">Regenerações</h3>
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 bg-orange-500/10 rounded-xl">
                        <span className="text-orange-200/80 font-medium">Total</span>
                        <span className="text-white font-bold text-lg">{statistics.messageRegenerations?.totalRegenerations || 0}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-orange-500/10 rounded-xl">
                        <span className="text-orange-200/80 font-medium">Média por resposta</span>
                        <span className="text-white font-bold text-lg">{(statistics.messageRegenerations?.averageRegenerationsPerAssistantMessage || 0).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>



              {/* Complexidade das Mensagens */}
              <div className="relative bg-gradient-to-br from-indigo-500/10 to-purple-500/10 backdrop-blur-sm border border-indigo-400/20 rounded-2xl p-8 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5"></div>
                <div className="relative">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="p-3 bg-indigo-500/20 rounded-xl border border-indigo-400/30">
                      <svg className="w-6 h-6 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white">Complexidade das Mensagens</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="group bg-indigo-500/10 rounded-xl p-6 border border-indigo-400/20 hover:border-indigo-300/40 transition-all duration-300 hover:scale-105">
                      <div className="text-center">
                        <div className="p-3 bg-indigo-500/20 rounded-xl mb-4 mx-auto w-fit">
                          <svg className="w-6 h-6 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                          </svg>
                        </div>
                        <div className="text-3xl font-bold text-white mb-2">{(statistics.messageComplexity?.averageWordsPerMessage || 0).toFixed(1)}</div>
                        <div className="text-sm text-indigo-200/70 font-medium">Palavras por mensagem</div>
                      </div>
                    </div>

                    <div className="group bg-purple-500/10 rounded-xl p-6 border border-purple-400/20 hover:border-purple-300/40 transition-all duration-300 hover:scale-105">
                      <div className="text-center">
                        <div className="p-3 bg-purple-500/20 rounded-xl mb-4 mx-auto w-fit">
                          <svg className="w-6 h-6 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                          </svg>
                        </div>
                        <div className="text-3xl font-bold text-white mb-2">{(statistics.messageComplexity?.averageSentencesPerMessage || 0).toFixed(1)}</div>
                        <div className="text-sm text-purple-200/70 font-medium">Frases por mensagem</div>
                      </div>
                    </div>

                    <div className="group bg-pink-500/10 rounded-xl p-6 border border-pink-400/20 hover:border-pink-300/40 transition-all duration-300 hover:scale-105">
                      <div className="text-center">
                        <div className="p-3 bg-pink-500/20 rounded-xl mb-4 mx-auto w-fit">
                          <svg className="w-6 h-6 text-pink-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                          </svg>
                        </div>
                        <div className="text-3xl font-bold text-white mb-2">{statistics.messageComplexity?.technicalTermsUsed || 0}</div>
                        <div className="text-sm text-pink-200/70 font-medium">Termos técnicos</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Métricas de Tempo */}
              <div className="relative bg-gradient-to-br from-teal-500/10 to-cyan-500/10 backdrop-blur-sm border border-teal-400/20 rounded-2xl p-8 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-teal-500/5 to-cyan-500/5"></div>
                <div className="relative">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="p-3 bg-teal-500/20 rounded-xl border border-teal-400/30">
                      <svg className="w-6 h-6 text-teal-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white">Métricas de Tempo</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="group bg-teal-500/10 rounded-xl p-6 border border-teal-400/20 hover:border-teal-300/40 transition-all duration-300 hover:scale-105">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-teal-500/20 rounded-xl">
                          <svg className="w-6 h-6 text-teal-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-white mb-1">{statistics.readingMetrics?.quickResponses || 0}</div>
                          <div className="text-sm text-teal-200/70 font-medium">Respostas rápidas (&lt;30s)</div>
                        </div>
                      </div>
                    </div>

                    <div className="group bg-cyan-500/10 rounded-xl p-6 border border-cyan-400/20 hover:border-cyan-300/40 transition-all duration-300 hover:scale-105">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-cyan-500/20 rounded-xl">
                          <svg className="w-6 h-6 text-cyan-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-white mb-1">{statistics.readingMetrics?.thoughtfulResponses || 0}</div>
                          <div className="text-sm text-cyan-200/70 font-medium">Respostas pensadas (&gt;2min)</div>
                        </div>
                      </div>
                    </div>

                    <div className="group bg-teal-500/10 rounded-xl p-6 border border-teal-400/20 hover:border-teal-300/40 transition-all duration-300 hover:scale-105">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-teal-500/20 rounded-xl">
                          <svg className="w-6 h-6 text-teal-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-white mb-1">{formatDuration(statistics.readingMetrics?.averageTimeToRespond || 0)}</div>
                          <div className="text-sm text-teal-200/70 font-medium">Tempo médio de resposta</div>
                        </div>
                      </div>
                    </div>

                    <div className="group bg-cyan-500/10 rounded-xl p-6 border border-cyan-400/20 hover:border-cyan-300/40 transition-all duration-300 hover:scale-105">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-cyan-500/20 rounded-xl">
                          <svg className="w-6 h-6 text-cyan-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-white mb-1">{formatDuration(statistics.readingMetrics?.estimatedReadingTime || 0)}</div>
                          <div className="text-sm text-cyan-200/70 font-medium">Tempo de leitura estimado</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Uso de Recursos */}
              {(statistics.featureUsage?.codeLanguages?.length > 0 || (statistics.featureUsage?.attachmentsUsed?.images || 0) > 0 || (statistics.featureUsage?.latexUsage || 0) > 0) && (
                <div className="relative bg-gradient-to-br from-amber-500/10 to-orange-500/10 backdrop-blur-sm border border-amber-400/20 rounded-2xl p-8 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-500/5 to-orange-500/5"></div>
                  <div className="relative">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="p-3 bg-amber-500/20 rounded-xl border border-amber-400/30">
                        <svg className="w-6 h-6 text-amber-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-bold text-white">Uso de Recursos</h3>
                    </div>

                    <div className="space-y-6">
                      {statistics.featureUsage?.codeLanguages?.length > 0 && (
                        <div>
                          <h4 className="text-lg font-medium text-amber-200 mb-4">Linguagens de Programação</h4>
                          <div className="flex flex-wrap gap-3">
                            {statistics.featureUsage.codeLanguages.slice(0, 8).map((lang, index) => (
                              <div key={index} className="group bg-amber-500/10 px-4 py-2 rounded-xl border border-amber-400/20 hover:border-amber-300/40 transition-all duration-300 hover:scale-105">
                                <span className="text-amber-200 font-medium">{lang.language}</span>
                                <span className="text-amber-300/70 ml-2 text-sm">({lang.count})</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="group bg-amber-500/10 rounded-xl p-6 border border-amber-400/20 hover:border-amber-300/40 transition-all duration-300 hover:scale-105">
                          <div className="flex items-center space-x-4">
                            <div className="p-3 bg-amber-500/20 rounded-xl">
                              <svg className="w-6 h-6 text-amber-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                            </div>
                            <div>
                              <div className="text-2xl font-bold text-white mb-1">{statistics.featureUsage?.attachmentsUsed?.images || 0}</div>
                              <div className="text-sm text-amber-200/70 font-medium">Imagens</div>
                            </div>
                          </div>
                        </div>

                        <div className="group bg-orange-500/10 rounded-xl p-6 border border-orange-400/20 hover:border-orange-300/40 transition-all duration-300 hover:scale-105">
                          <div className="flex items-center space-x-4">
                            <div className="p-3 bg-orange-500/20 rounded-xl">
                              <svg className="w-6 h-6 text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </div>
                            <div>
                              <div className="text-2xl font-bold text-white mb-1">{statistics.featureUsage?.attachmentsUsed?.pdfs || 0}</div>
                              <div className="text-sm text-orange-200/70 font-medium">PDFs</div>
                            </div>
                          </div>
                        </div>

                        <div className="group bg-red-500/10 rounded-xl p-6 border border-red-400/20 hover:border-red-300/40 transition-all duration-300 hover:scale-105">
                          <div className="flex items-center space-x-4">
                            <div className="p-3 bg-red-500/20 rounded-xl">
                              <svg className="w-6 h-6 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                              </svg>
                            </div>
                            <div>
                              <div className="text-2xl font-bold text-white mb-1">{statistics.featureUsage?.latexUsage || 0}</div>
                              <div className="text-sm text-red-200/70 font-medium">LaTeX usado</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}



              {/* Informações Gerais */}
              <div className="relative bg-gradient-to-br from-slate-700/20 to-gray-800/20 backdrop-blur-sm border border-slate-400/20 rounded-2xl p-8 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-slate-500/5 to-gray-500/5"></div>
                <div className="relative">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="p-3 bg-slate-500/20 rounded-xl border border-slate-400/30">
                      <svg className="w-6 h-6 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white">Informações Gerais</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="group bg-slate-500/10 rounded-xl p-6 border border-slate-400/20 hover:border-slate-300/40 transition-all duration-300 hover:scale-105">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-slate-500/20 rounded-xl">
                          <svg className="w-6 h-6 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-white mb-1">{formatDate(statistics.firstMessageAt)}</div>
                          <div className="text-sm text-slate-200/70 font-medium">Primeira mensagem</div>
                        </div>
                      </div>
                    </div>

                    <div className="group bg-slate-500/10 rounded-xl p-6 border border-slate-400/20 hover:border-slate-300/40 transition-all duration-300 hover:scale-105">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-slate-500/20 rounded-xl">
                          <svg className="w-6 h-6 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-white mb-1">{formatDate(statistics.lastMessageAt)}</div>
                          <div className="text-sm text-slate-200/70 font-medium">Última mensagem</div>
                        </div>
                      </div>
                    </div>

                    <div className="group bg-slate-500/10 rounded-xl p-6 border border-slate-400/20 hover:border-slate-300/40 transition-all duration-300 hover:scale-105">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-slate-500/20 rounded-xl">
                          <svg className="w-6 h-6 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-white mb-1">{formatNumber(statistics.totalSessions)}</div>
                          <div className="text-sm text-slate-200/70 font-medium">Total de sessões</div>
                        </div>
                      </div>
                    </div>

                    <div className="group bg-slate-500/10 rounded-xl p-6 border border-slate-400/20 hover:border-slate-300/40 transition-all duration-300 hover:scale-105">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-slate-500/20 rounded-xl">
                          <svg className="w-6 h-6 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-white mb-1">{formatDuration(statistics.averageSessionDuration)}</div>
                          <div className="text-sm text-slate-200/70 font-medium">Duração média das sessões</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // Usar portal para renderizar o modal fora da árvore de componentes
  return typeof window !== 'undefined'
    ? createPortal(modalContent, document.body)
    : null;
};

export default ChatStatisticsModal;
