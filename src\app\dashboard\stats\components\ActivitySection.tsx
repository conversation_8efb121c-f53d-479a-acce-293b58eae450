'use client';

import { GeneralStatistics } from '@/lib/generalStatisticsService';

interface ActivitySectionProps {
  statistics: GeneralStatistics;
}

export default function ActivitySection({ statistics }: ActivitySectionProps) {
  const dayNames = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
  const hours = Array.from({ length: 24 }, (_, i) => i);

  // Find max values for normalization
  const maxHourValue = Math.max(...statistics.activityHeatmap.hourOfDay);
  const maxDayValue = Math.max(...statistics.activityHeatmap.dayOfWeek);

  const getIntensity = (value: number, max: number): number => {
    if (max === 0) return 0;
    return Math.min(value / max, 1);
  };

  const getHeatmapColor = (intensity: number): string => {
    if (intensity === 0) return 'bg-white/5';
    if (intensity < 0.2) return 'bg-purple-500/20';
    if (intensity < 0.4) return 'bg-purple-500/40';
    if (intensity < 0.6) return 'bg-purple-500/60';
    if (intensity < 0.8) return 'bg-purple-500/80';
    return 'bg-purple-500';
  };

  // Find peak activity times
  const peakHour = statistics.activityHeatmap.hourOfDay.indexOf(maxHourValue);
  const peakDay = statistics.activityHeatmap.dayOfWeek.indexOf(maxDayValue);

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center space-x-3">
        <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-lg">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <div>
          <h2 className="text-2xl font-bold text-white">Padrões de Atividade</h2>
          <p className="text-white/60">Quando você é mais ativo nas conversas</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Hourly Activity Heatmap */}
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Atividade por Hora</h3>
            <div className="text-sm text-white/60">
              Pico: {peakHour}h ({statistics.activityHeatmap.hourOfDay[peakHour]} mensagens)
            </div>
          </div>
          
          <div className="grid grid-cols-12 gap-1">
            {hours.map((hour) => {
              const intensity = getIntensity(statistics.activityHeatmap.hourOfDay[hour], maxHourValue);
              const isPeak = hour === peakHour;
              
              return (
                <div
                  key={hour}
                  className={`
                    relative h-12 rounded-lg ${getHeatmapColor(intensity)} 
                    flex flex-col items-center justify-center text-xs text-white font-medium 
                    hover:scale-110 transition-all duration-300 cursor-pointer group
                    ${isPeak ? 'ring-2 ring-yellow-400 ring-opacity-60' : ''}
                  `}
                  title={`${hour}h: ${statistics.activityHeatmap.hourOfDay[hour]} mensagens`}
                >
                  <span className="text-[10px] opacity-80">{hour}</span>
                  <span className="text-[8px] opacity-60">{statistics.activityHeatmap.hourOfDay[hour]}</span>
                  
                  {/* Tooltip */}
                  <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                    {hour}h: {statistics.activityHeatmap.hourOfDay[hour]} mensagens
                  </div>
                </div>
              );
            })}
          </div>

          {/* Legend */}
          <div className="flex items-center justify-between mt-4 text-xs text-white/60">
            <span>Menos ativo</span>
            <div className="flex items-center space-x-1">
              {[0, 0.2, 0.4, 0.6, 0.8, 1].map((intensity, index) => (
                <div key={index} className={`w-3 h-3 rounded ${getHeatmapColor(intensity)}`}></div>
              ))}
            </div>
            <span>Mais ativo</span>
          </div>
        </div>

        {/* Weekly Activity */}
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Atividade por Dia</h3>
            <div className="text-sm text-white/60">
              Pico: {dayNames[peakDay]} ({statistics.activityHeatmap.dayOfWeek[peakDay]} mensagens)
            </div>
          </div>
          
          <div className="grid grid-cols-7 gap-2">
            {dayNames.map((dayName, index) => {
              const intensity = getIntensity(statistics.activityHeatmap.dayOfWeek[index], maxDayValue);
              const isPeak = index === peakDay;
              
              return (
                <div
                  key={index}
                  className={`
                    relative h-20 rounded-xl ${getHeatmapColor(intensity)} 
                    flex flex-col items-center justify-center text-sm text-white font-medium 
                    hover:scale-105 transition-all duration-300 cursor-pointer group
                    ${isPeak ? 'ring-2 ring-yellow-400 ring-opacity-60' : ''}
                  `}
                  title={`${dayName}: ${statistics.activityHeatmap.dayOfWeek[index]} mensagens`}
                >
                  <span className="text-xs opacity-80 mb-1">{dayName}</span>
                  <span className="text-lg font-bold">{statistics.activityHeatmap.dayOfWeek[index]}</span>
                  
                  {/* Tooltip */}
                  <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                    {dayName}: {statistics.activityHeatmap.dayOfWeek[index]} mensagens
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Activity Insights */}
      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-xl border border-purple-400/20 rounded-2xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Insights de Atividade</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-300">{peakHour}h</div>
            <div className="text-sm text-white/60">Hora mais ativa</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-pink-300">{dayNames[peakDay]}</div>
            <div className="text-sm text-white/60">Dia mais ativo</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-indigo-300">
              {(statistics.totalMessages / Math.max(statistics.totalSessions, 1)).toFixed(1)}
            </div>
            <div className="text-sm text-white/60">Mensagens por sessão</div>
          </div>
        </div>
      </div>
    </div>
  );
}
