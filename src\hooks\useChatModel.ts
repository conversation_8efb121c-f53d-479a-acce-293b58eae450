import { useState, useEffect, useCallback } from 'react';
import { chatDataService } from '@/lib/chatDataService';
import { syncService } from '@/lib/syncService';
import { getUserDefaultModel } from '@/lib/settingsService';

interface UseChatModelProps {
  userId: string | null;
  chatId: string | null;
  conversation?: { lastUsedModel?: string } | null;
}

interface UseChatModelReturn {
  selectedModel: string;
  setSelectedModel: (model: string) => void;
  loading: boolean;
  error: string | null;
  saveModel: (model: string) => Promise<void>;
}

export const useChatModel = ({ userId, chatId, conversation }: UseChatModelProps): UseChatModelReturn => {
  const [selectedModel, setSelectedModelState] = useState('google/gemini-2.5-flash'); // Default model
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load the last used model when chat changes
  const loadChatModel = useCallback(async () => {
    if (!userId || !chatId) {
      // Get user's default model when no chat is selected
      if (userId) {
        try {
          const defaultModel = await getUserDefaultModel(userId);
          setSelectedModelState(defaultModel);
        } catch (err) {
          console.error('Error getting user default model:', err);
          setSelectedModelState('google/gemini-2.5-flash'); // Fallback
        }
      } else {
        setSelectedModelState('google/gemini-2.5-flash'); // Reset to default when no user
      }
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Load from Storage through API (avoiding CORS issues)
      const chatData = await chatDataService.getChatData(userId, chatId);

      if (chatData && chatData.lastUsedModel) {
        setSelectedModelState(chatData.lastUsedModel);
      } else if (conversation && conversation.lastUsedModel) {
        // Fallback to conversation's last used model
        setSelectedModelState(conversation.lastUsedModel);
      } else {
        // Get user's default model for new chats
        const defaultModel = await getUserDefaultModel(userId);
        setSelectedModelState(defaultModel);
      }
    } catch (err) {
      console.error('Error loading chat model:', err);
      setError('Failed to load chat model');
      // Fallback to user's default model or google/gemini-2.5-flash
      try {
        const defaultModel = await getUserDefaultModel(userId);
        setSelectedModelState(defaultModel);
      } catch (defaultErr) {
        console.error('Error getting user default model:', defaultErr);
        setSelectedModelState('google/gemini-2.5-flash'); // Final fallback
      }
    } finally {
      setLoading(false);
    }
  }, [userId, chatId, conversation]);

  // Save model to chat data
  const saveModel = useCallback(async (model: string) => {
    if (!userId || !chatId) return;

    try {
      // Get current chat data
      const chatData = await chatDataService.getChatData(userId, chatId);

      if (chatData) {
        const updatedChatData = {
          ...chatData,
          lastUsedModel: model,
          lastUpdatedAt: Date.now()
        };

        // Update chat data
        await chatDataService.updateChatData(userId, chatId, updatedChatData);

        // Sync to Firestore in background
        syncService.syncChatToFirestore(userId, chatId).catch(err =>
          console.warn('Failed to sync to Firestore:', err)
        );

        console.log(`Model saved for chat ${chatId}: ${model}`);
      }
    } catch (err) {
      console.error('Error saving chat model:', err);
      setError('Failed to save chat model');
    }
  }, [userId, chatId]);

  // Custom setter that also saves the model
  const setSelectedModel = useCallback(async (model: string) => {
    setSelectedModelState(model);
    await saveModel(model);
  }, [saveModel]);

  // Load model when userId or chatId changes
  useEffect(() => {
    loadChatModel();
  }, [loadChatModel]);

  return {
    selectedModel,
    setSelectedModel,
    loading,
    error,
    saveModel,
  };
};
