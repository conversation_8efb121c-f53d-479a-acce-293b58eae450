// Este serviço agora usa apenas APIs do servidor para evitar problemas de permissão
import { FavoriteMessage, ChatMessage } from './types/chat';

class FavoritesService {
  // Adicionar mensagem aos favoritos via API
  async addToFavorites(
    userId: string,
    messageId: string,
    chatId: string,
    chatName: string,
    message: ChatMessage
  ): Promise<void> {
    try {
      const response = await fetch('/api/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          messageId,
          chatId,
          chatName,
          message
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Erro ao adicionar favorito');
      }

      console.log('Mensagem adicionada aos favoritos via API');
    } catch (error) {
      console.error('Erro ao adicionar mensagem aos favoritos:', error);
      throw error;
    }
  }

  // Remover mensagem dos favoritos via API
  async removeFromFavorites(userId: string, messageId: string, chatId: string): Promise<void> {
    try {
      const params = new URLSearchParams({
        userId,
        messageId,
        chatId
      });

      const response = await fetch(`/api/favorites?${params.toString()}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Erro ao remover favorito');
      }

      console.log('Mensagem removida dos favoritos via API');
    } catch (error) {
      console.error('Erro ao remover mensagem dos favoritos:', error);
      throw error;
    }
  }

  // Obter todas as mensagens favoritas do usuário via API
  async getFavorites(userId: string): Promise<FavoriteMessage[]> {
    try {
      const params = new URLSearchParams({ userId });
      const response = await fetch(`/api/favorites?${params.toString()}`);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Erro ao obter favoritos');
      }

      return data.favorites;
    } catch (error) {
      console.error('Erro ao obter mensagens favoritas:', error);
      throw error;
    }
  }

  // Obter favoritos filtrados por chat via API
  async getFavoritesByChat(userId: string, chatId: string): Promise<FavoriteMessage[]> {
    try {
      const params = new URLSearchParams({ userId, chatId });
      const response = await fetch(`/api/favorites?${params.toString()}`);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Erro ao obter favoritos por chat');
      }

      return data.favorites;
    } catch (error) {
      console.error('Erro ao obter favoritos por chat:', error);
      throw error;
    }
  }

  // Obter favoritos filtrados por período via API
  async getFavoritesByDateRange(
    userId: string,
    startDate: number,
    endDate: number
  ): Promise<FavoriteMessage[]> {
    try {
      const params = new URLSearchParams({
        userId,
        startDate: startDate.toString(),
        endDate: endDate.toString()
      });
      const response = await fetch(`/api/favorites?${params.toString()}`);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Erro ao obter favoritos por período');
      }

      return data.favorites;
    } catch (error) {
      console.error('Erro ao obter favoritos por período:', error);
      throw error;
    }
  }

  // Obter favoritos filtrados por tipo (user/assistant) via API
  async getFavoritesByRole(userId: string, role: 'user' | 'assistant'): Promise<FavoriteMessage[]> {
    try {
      const params = new URLSearchParams({ userId, role });
      const response = await fetch(`/api/favorites?${params.toString()}`);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Erro ao obter favoritos por role');
      }

      return data.favorites;
    } catch (error) {
      console.error('Erro ao obter favoritos por role:', error);
      throw error;
    }
  }
}

export const favoritesService = new FavoritesService();
export default favoritesService;
