import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // DeepSeek models are static, but we can simulate an API call
    // In a real implementation, you would call the DeepSeek API
    
    const deepSeekModels = [
      {
        id: 'deepseek-chat',
        name: 'DeepSeek Chat',
        description: 'Modelo de chat geral da DeepSeek com alta performance',
        context_length: 32768,
        pricing: {
          prompt: '0.14', // $0.14 per 1M tokens
          completion: '0.28', // $0.28 per 1M tokens
          image: '0'
        },
        architecture: {
          input_modalities: ['text'],
          output_modalities: ['text'],
          tokenizer: 'DeepSeek'
        },
        created: Date.now()
      },
      {
        id: 'deepseek-reasoner',
        name: 'DeepSeek Reasoner',
        description: 'Modelo especializado em raciocínio e resolução de problemas complexos',
        context_length: 65536,
        pricing: {
          prompt: '0.55', // $0.55 per 1M tokens
          completion: '2.19', // $2.19 per 1M tokens
          image: '0'
        },
        architecture: {
          input_modalities: ['text'],
          output_modalities: ['text'],
          tokenizer: 'DeepSeek'
        },
        created: Date.now()
      }
    ];

    return NextResponse.json({
      success: true,
      data: deepSeekModels
    });

  } catch (error) {
    console.error('Error fetching DeepSeek models:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch models from DeepSeek',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
