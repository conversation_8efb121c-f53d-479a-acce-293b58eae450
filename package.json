{"name": "rafthoria", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "firebase": "^11.9.1", "katex": "^0.16.22", "lucide-react": "^0.519.0", "next": "14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-katex-svelte": "^1.2.0", "remark-directive": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.0.0", "@next/env": "^14.1.0", "@types/body-parser": "^1.19.6", "@types/caseless": "^0.12.5", "@types/connect": "^3.4.38", "@types/debug": "^4.1.12", "@types/estree": "^1.0.8", "@types/estree-jsx": "^1.0.5", "@types/express": "^5.0.3", "@types/express-serve-static-core": "^5.0.7", "@types/hast": "^3.0.4", "@types/http-errors": "^2.0.5", "@types/jest": "^30.0.0", "@types/json5": "^0.0.30", "@types/jsonwebtoken": "^9.0.10", "@types/katex": "^0.16.7", "@types/long": "^4.0.2", "@types/mdast": "^4.0.4", "@types/mime": "^3.0.4", "@types/ms": "^2.1.0", "@types/node": "^20.19.8", "@types/prop-types": "^15.7.15", "@types/qs": "^6.14.0", "@types/range-parser": "^1.2.7", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/request": "^2.48.12", "@types/send": "^0.17.5", "@types/serve-static": "^1.15.8", "@types/tough-cookie": "^4.0.5", "@types/unist": "^3.0.3", "autoprefixer": "^10.4.17", "babel-jest": "^30.0.4", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "firebase-frameworks": "^0.11.7", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}