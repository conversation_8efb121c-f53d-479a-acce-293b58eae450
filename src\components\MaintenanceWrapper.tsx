'use client';

import { useMaintenanceMode } from '@/hooks/useMaintenanceMode';
import MaintenanceModal from './MaintenanceModal';

interface MaintenanceWrapperProps {
  children: React.ReactNode;
}

const MaintenanceWrapper = ({ children }: MaintenanceWrapperProps) => {
  const { 
    isMaintenanceActive, 
    maintenanceMessage, 
    isLoading, 
    bypassMaintenance 
  } = useMaintenanceMode();

  // Mostrar loading enquanto verifica o status de manutenção
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto"></div>
          <h1 className="mt-6 text-2xl font-bold text-white">RafthorIA</h1>
          <p className="mt-2 text-purple-300">Verificando status do sistema...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {children}
      <MaintenanceModal
        isOpen={isMaintenanceActive}
        message={maintenanceMessage}
        onSuccess={bypassMaintenance}
      />
    </>
  );
};

export default MaintenanceWrapper;
