import { NextRequest, NextResponse } from 'next/server';
import { chatStorageService } from '@/lib/chatStorageService';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const userId = request.nextUrl.searchParams.get('userId');
    const chatId = request.nextUrl.searchParams.get('chatId');

    // Validar parâmetros obrigatórios
    if (!userId || !chatId) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, chatId' },
        { status: 400 }
      );
    }

    // Obter dados do chat
    const chatData = await chatStorageService.getChatData(userId, chatId);

    if (!chatData) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      chatData 
    });

  } catch (error) {
    console.error('Error getting chat data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, chatId, chatData } = body;

    // Validar parâmetros obrigatórios
    if (!userId || !chatId || !chatData) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, chatId, chatData' },
        { status: 400 }
      );
    }

    // Atualizar dados do chat
    await chatStorageService.updateChatData(userId, chatId, chatData);

    return NextResponse.json({ 
      success: true, 
      message: 'Chat data updated successfully' 
    });

  } catch (error) {
    console.error('Error updating chat data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
